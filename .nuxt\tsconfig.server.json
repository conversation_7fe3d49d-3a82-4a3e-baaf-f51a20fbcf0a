{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noEmit": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "resolveJsonModule": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"#imports": ["./types/nitro-imports"], "~/*": ["../*"], "@/*": ["../*"], "~~/*": ["../*"], "@@/*": ["../*"], "nitropack/types": ["../node_modules/nitropack/types"], "nitropack/runtime": ["../node_modules/nitropack/runtime"], "nitropack": ["../node_modules/nitropack"], "defu": ["../node_modules/defu"], "h3": ["../node_modules/h3"], "consola": ["../node_modules/consola"], "ofetch": ["../node_modules/ofetch"], "@nuxt/devtools": ["../node_modules/@nuxt/devtools"], "@vue/runtime-core": ["../node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/@nuxt/schema"], "nuxt": ["../node_modules/nuxt"], "vite/client": ["../node_modules/vite/client"], "#shared": ["../shared"], "assets": ["../assets"], "assets/*": ["../assets/*"], "public": ["../public"], "public/*": ["../public/*"], "#build": ["./"], "#build/*": ["./*"], "#internal/nuxt/paths": ["../node_modules/nuxt/dist/core/runtime/nitro/utils/paths"], "#sitemap": ["../node_modules/@nuxtjs/sitemap/dist/runtime"], "#sitemap/*": ["../node_modules/@nuxtjs/sitemap/dist/runtime/*"], "#site-config": ["../node_modules/nuxt-site-config/dist/runtime"], "#site-config/*": ["../node_modules/nuxt-site-config/dist/runtime/*"], "#content/components": ["./content/components"], "#content/manifest": ["./content/manifest"], "#mdc-configs": ["./mdc-configs"], "#mdc-highlighter": ["./mdc-highlighter"], "#mdc-imports": ["./mdc-imports"], "#image": ["../node_modules/@nuxt/image/dist/runtime"], "#image/*": ["../node_modules/@nuxt/image/dist/runtime/*"], "#unhead/composables": ["../node_modules/nuxt/dist/head/runtime/composables/v3"], "#internal/nuxt-site-config": ["../node_modules/nuxt-site-config/dist/runtime/server/composable-barrel-deprecated"], "#content/dump": ["./content/database.compressed"], "#content/adapter": ["./db0/connectors/better-sqlite3"], "#content/local-adapter": ["./db0/connectors/better-sqlite3"]}, "lib": ["esnext", "webworker", "dom.iterable"]}, "include": ["./content/types.d.ts", "./types/nitro-nuxt.d.ts", "../node_modules/nuxt-site-config/dist/module.mjs/runtime/server", "../node_modules/@nuxtjs/sitemap/runtime/server", "../node_modules/@nuxtjs/mdc/runtime/server", "../node_modules/@nuxt/content/runtime/server", "../node_modules/@nuxtjs/tailwindcss/runtime/server", "../node_modules/@vueuse/nuxt/runtime/server", "../node_modules/@nuxt/image/runtime/server", "../node_modules/@nuxt/devtools/runtime/server", "../node_modules/@nuxt/telemetry/runtime/server", "./module/nuxt-site-config.d.ts", "./module/@nuxtjs-sitemap.d.ts", "./types/nitro.d.ts", "../**/*", "../server/**/*"], "exclude": ["../node_modules", "../node_modules/nuxt/node_modules", "../node_modules/nuxt-site-config/node_modules", "../node_modules/@nuxtjs/sitemap/node_modules", "../node_modules/@nuxtjs/mdc/node_modules", "../node_modules/@nuxt/content/node_modules", "../node_modules/@nuxtjs/tailwindcss/node_modules", "../node_modules/@vueuse/nuxt/node_modules", "../node_modules/@nuxt/image/node_modules", "../node_modules/@nuxt/devtools/node_modules", "../node_modules/@nuxt/telemetry/node_modules", "../dist"]}