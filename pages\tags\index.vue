<script setup lang="ts">
import { useRoute, useAsyncData, queryCollection } from '#imports'

const route = useRoute()

// Mengambil semua artikel
const { data: posts } = await useAsyncData(route.path, () => queryCollection('posts').all())

// Mengekstrak semua tag unik dan menghitung jumlah artikel per tag
const tags = computed(() => {
  if (!posts.value) return []
  
  // Kumpulkan semua tag dari semua artikel
  const tagCounts = {}
  
  posts.value.forEach(post => {
    if (post.tags && Array.isArray(post.tags)) {
      post.tags.forEach(tag => {
        if (!tagCounts[tag]) {
          tagCounts[tag] = 1
        } else {
          tagCounts[tag]++
        }
      })
    }
  })
  
  // Ubah menjadi array dan urutkan berdasarkan jumlah artikel
  return Object.entries(tagCounts)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
})

// Enhanced SEO metadata untuk Tags Index
const baseUrl = 'https://www.jogjaliburan.com'
const currentUrl = `${baseUrl}/tags/`
const title = 'Kategori Wisata - Blog Jogja Liburan | Temukan Destinasi Sesuai Minat'
const description = 'Jelajahi berbagai kategori wisata Yogyakarta: candi, pantai, kuliner, gunung, budaya, dan destinasi menarik lainnya. Temukan panduan sesuai minat liburan Anda'

// Structured data untuk Tags Index
const tagsSchema = {
  '@context': 'https://schema.org',
  '@graph': [
    {
      '@type': 'Organization',
      '@id': `${baseUrl}/#organization`,
      'name': '3J Tour - Jogja Liburan',
      'url': baseUrl,
      'logo': {
        '@type': 'ImageObject',
        'url': `${baseUrl}/3j-tour-logo.webp`
      }
    },
    {
      '@type': 'WebPage',
      '@id': currentUrl,
      'name': title,
      'description': description,
      'url': currentUrl,
      'inLanguage': 'id-ID',
      'isPartOf': {
        '@type': 'WebSite',
        '@id': `${baseUrl}/#website`,
        'name': '3J Tour - Jogja Liburan',
        'url': baseUrl
      },
      'breadcrumb': {
        '@type': 'BreadcrumbList',
        'itemListElement': [
          {
            '@type': 'ListItem',
            'position': 1,
            'name': 'Beranda',
            'item': baseUrl
          },
          {
            '@type': 'ListItem',
            'position': 2,
            'name': 'Kategori Wisata',
            'item': currentUrl
          }
        ]
      }
    },
    {
      '@type': 'CollectionPage',
      '@id': `${currentUrl}#collection`,
      'name': 'Kategori Wisata Yogyakarta',
      'description': description,
      'url': currentUrl,
      'mainEntity': {
        '@type': 'ItemList',
        'name': 'Kategori Wisata',
        'description': 'Daftar kategori destinasi wisata di Yogyakarta',
        'numberOfItems': tags.value.length,
        'itemListElement': tags.value.map((tag, index) => ({
          '@type': 'ListItem',
          'position': index + 1,
          'name': tag.name,
          'url': `${baseUrl}/tags/${tag.name.toLowerCase()}/`,
          'description': `${tag.count} artikel tentang ${tag.name}`
        }))
      }
    }
  ]
}

useSeoMeta({
  title: title,
  description: description,
  keywords: 'kategori wisata jogja, destinasi yogyakarta, wisata candi, pantai jogja, kuliner yogyakarta, gunung merapi, budaya jogja, travel guide',
  author: '3J Tour Team',
  robots: 'index, follow',
  
  // Open Graph
  ogTitle: title,
  ogDescription: description,
  ogType: 'website',
  ogUrl: currentUrl,
  ogImage: `${baseUrl}/images/tags-og.jpg`,
  ogSiteName: '3J Tour - Jogja Liburan',
  ogLocale: 'id_ID',
  
  // Twitter Card
  twitterCard: 'summary_large_image',
  twitterTitle: title,
  twitterDescription: description,
  twitterImage: `${baseUrl}/images/tags-og.jpg`,
  twitterSite: '@jogja_jalan_jalan',
  
  // Additional meta
  themeColor: '#f97316'
})

useHead({
  link: [
    { rel: 'canonical', href: currentUrl }
  ],
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify(tagsSchema)
    }
  ]
})

// Debug tags
console.log('Available tags:', tags.value)
</script>

<template>
  <main class="py-20 bg-gray-50 dark:bg-gray-800 px-5">
    <div class="container mx-auto">
      <!-- Header -->
      <div class="text-center mb-12">
        <h2 class="max-w-3xl mx-auto text-4xl md:text-4xl font-bold text-gray-900 mb-6 text-center">
          Tags <span class="text-blue-500">Blog</span>
        </h2>
        <p class="max-w-3xl mx-auto text-lg text-gray-600 mb-10 text-center">
          Jelajahi artikel berdasarkan <span class="font-bold">topik</span> yang Anda minati
        </p>
      </div>
      
      <!-- Tags Cloud -->
      <div class="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8 mb-12 border-l-4 border-blue-500 relative">
        <div class="absolute top-0 right-0 w-32 h-32 -mt-10 -mr-10 bg-blue-200 rounded-full"></div>
        <div class="absolute bottom-0 left-0 w-24 h-24 -mb-8 -ml-8 bg-blue-200 rounded-full"></div>
        
        <h2 class="text-2xl font-bold mb-8 text-gray-900 dark:text-white">Semua Tags</h2>
        
        <div v-if="tags.length" class="flex flex-wrap gap-3">
          <a 
            v-for="tag in tags" 
            :key="tag.name"
            :href="`/tags/${tag.name.toLowerCase()}/`"
            class="bg-blue-200 hover:bg-blue-600 text-zinc-600 px-4 py-2 rounded-full transition-colors flex items-center gap-2"
          >
            <span>{{ tag.name }}</span>
            <span class="bg-blue-600 text-zinc-100 text-xs font-medium px-2.5 py-0.5 rounded-full">
              {{ tag.count }}
            </span>
          </a>
        </div>
        
        <div v-else class="text-center py-8">
          <p class="text-gray-600 dark:text-gray-300">Belum ada tags yang tersedia</p>
        </div>
      </div>
      
      <!-- Back to Blog -->
      <div class="text-center">
        <a href="/blog/" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-600/90 transition-colors font-medium">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Kembali ke Blog
        </a>
      </div>
    </div>
  </main>
</template>

<style>
/* Styles for tags index page */
</style>