<template>
  <div>
    <!-- Filter Container -->
    <div class="bg-white rounded-xl shadow-sm p-5">
      <!-- Kategori Filter -->
      <div class="mb-6">
        <div class="space-y-2.5">
          <button
            @click="resetFilters"
            class="block w-full px-4 py-2.5 rounded-lg text-sm transition-colors text-left"
            :class="!activeCategory && !activeDuration ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
          >
            All Packet
          </button>
          
          <h3 class="text-xl font-bold mb-4">Paket Hotel & Tanpa Hotel</h3>
          <div class="border-t border-gray-200 my-6"></div>
          
          <NuxtLink
            v-for="category in categories"
            :key="category.slug"
            :to="`/paket-wisata-jogja/${category.slug}/`"
            class="block px-4 py-2.5 rounded-lg text-sm transition-colors"
            :class="activeCategory === category.slug ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
          >
            {{ category.label }}
          </NuxtLink>
        </div>
      </div>

      <!-- Divider -->
      <div class="border-t border-gray-200 my-6"></div>

      <!-- Durasi Filter -->
      <div>
        <h3 class="text-xl font-bold mb-4">Pilihan Durasi Wisata</h3>
        <div class="space-y-2.5">
          <NuxtLink
            v-for="duration in durations"
            :key="duration.slug"
            :to="`/paket-wisata-jogja/${duration.slug}/`"
            class="block px-4 py-2.5 rounded-lg text-sm transition-colors"
            :class="activeDuration === duration.slug ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
          >
            {{ duration.label }}
          </NuxtLink>
        </div>
      </div>

      <!-- Divider -->
      <div class="border-t border-gray-200 my-6"></div>

      <!-- Sewa Mobil Wisata -->
      <div>
        <h3 class="text-xl font-bold mb-4">Pilihan Mobil Wisata</h3>
        <div class="space-y-3">
          <!-- Featured Car Rentals -->
          <template v-if="popularCars.length > 0">
            <NuxtLink 
              v-for="mobil in popularCars.slice(0, 2)" 
              :key="mobil.id"
              :to="`/sewa-mobil-jogja/${mobil.slug}/`"
              class="border border-gray-200 rounded-lg p-3 hover:border-primary transition-colors block"
            >
              <div class="flex items-center gap-3">
                <img 
                  :src="mobil.image" 
                  :alt="mobil.name"
                  class="w-12 h-12 object-cover rounded-lg"
                />
                <div class="flex-1 min-w-0">
                  <p class="font-medium text-sm text-gray-900 truncate">{{ mobil.name }}</p>
                  <div class="flex items-center gap-2 text-xs text-gray-500">
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-3 h-3 text-yellow-500">
                        <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
                      </svg>
                      <span class="ml-1">{{ mobil.rating }}</span>
                    </div>
                    <span>•</span>
                    <span>{{ mobil.capacity }}</span>
                  </div>
                  <div class="text-sm font-medium text-primary mt-1">
                    Mulai {{ getLowestPrice(mobil) }}
                  </div>
                </div>
              </div>
            </NuxtLink>
          </template>
          
          <!-- Fallback if no cars available -->
          <template v-else>
            <div class="text-center py-4 text-gray-500 text-sm">
              <p>Sedang memuat data mobil...</p>
            </div>
          </template>

          <!-- Button Rental Mobil Wisata -->
          <NuxtLink
            to="/sewa-mobil-jogja"
            class="w-full bg-gradient-to-r from-primary to-primary-dark text-white py-3 px-4 rounded-lg font-medium hover:from-primary-dark hover:to-primary transition-all duration-300 shadow-sm hover:shadow-md block"
          >
            <div class="flex items-center justify-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 1-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m8.25 4.5V16.5a1.125 1.125 0 0 1 1.125-1.125h2.25a1.125 1.125 0 0 1 1.125 1.125v1.875a1.125 1.125 0 0 1-1.125 1.125H9.375A1.125 1.125 0 0 1 8.25 15V14.25m8.25 4.5h-2.25a1.125 1.125 0 0 1-1.125-1.125V16.5h3.375a1.125 1.125 0 0 1 1.125 1.125v1.875Z" />
              </svg>
              Rental Mobil Wisata
            </div>
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, nextTick } from 'vue'
import { usePaketWisata } from '~/composables/usePaketWisata'
import sewaMobilData from '~/data/sewa-mobil.json'

const props = defineProps({
  activeCategory: {
    type: String,
    default: ''
  },
  activeDuration: {
    type: String,
    default: ''
  }
})

const { getAllCategories, getAllDurations } = usePaketWisata()

// Get all categories and durations
const categories = getAllCategories.value
const durations = getAllDurations.value

// Reset filters function
const resetFilters = async () => {
  try {
    // Navigate to the main paket wisata page to reset all filters
    await navigateTo('/paket-wisata-jogja/', { 
      replace: true,  // Replace current history entry
      external: false 
    })
    
    // Smooth scroll to the packages section after navigation
    await nextTick()
    setTimeout(() => {
      const targetElement = document.getElementById('paket-wisata-jogja-terlengkap')
      if (targetElement) {
        const headerOffset = 80
        const elementPosition = targetElement.getBoundingClientRect().top
        const offsetPosition = elementPosition + window.pageYOffset - headerOffset
        
        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        })
      }
    }, 100) // Small delay to ensure DOM is ready
  } catch (error) {
    console.warn('Navigation failed, falling back to page reload')
    // Fallback: reload the page if navigation fails
    window.location.href = '/paket-wisata-jogja/'
  }
}

// Get popular car rentals (2 cars with highest rating and reviews)
const popularCars = computed(() => {
  if (!sewaMobilData?.sewaMobil) return []
  
  return sewaMobilData.sewaMobil
    .filter(mobil => mobil.rating >= 4.7) // Filter high rated cars
    .sort((a, b) => {
      // Sort by rating first, then by reviews count
      if (b.rating !== a.rating) {
        return b.rating - a.rating
      }
      return b.reviews - a.reviews
    })
    .slice(0, 2) // Get top 2 cars for sidebar
})

// Get lowest price for a car
const getLowestPrice = (mobil) => {
  if (!mobil) return 'Rp 300.000'
  
  // Handle packages format (object with ms, msb, aio)
  if (mobil.packages) {
    const packages = Object.values(mobil.packages)
    const allPrices = packages.flatMap(pkg => 
      pkg.pricing ? Object.values(pkg.pricing) : []
    )
    if (allPrices.length > 0) {
      const minPrice = Math.min(...allPrices)
      return `Rp ${minPrice.toLocaleString('id-ID')}`
    }
  }
  
  // Handle pricing format (direct object)
  if (mobil.pricing) {
    const prices = Object.values(mobil.pricing)
      .filter(price => typeof price === 'string')
      .map(price => {
        // Extract number from string like "Rp 200.000"
        const match = price.match(/[\d.]+/)
        return match ? parseInt(match[0].replace(/\./g, '')) : 0
      })
      .filter(price => price > 0)
    
    if (prices.length > 0) {
      const minPrice = Math.min(...prices)
      return `Rp ${minPrice.toLocaleString('id-ID')}`
    }
  }
  
  return 'Rp 300.000'
}

</script>
