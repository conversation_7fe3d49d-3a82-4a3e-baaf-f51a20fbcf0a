import process from 'node:process';globalThis._importMeta_={url:import.meta.url,env:process.env};import { tmpdir } from 'node:os';
import { Server } from 'node:http';
import { resolve as resolve$1, dirname, join } from 'node:path';
import nodeCrypto from 'node:crypto';
import { parentPort, threadId } from 'node:worker_threads';
import { defineEventHandler, handleCacheHeaders, splitCookiesString, createEvent, fetchWithEvent, isEvent, eventHandler, setHeaders, sendRedirect, proxyRequest, getRequestHeader, setResponseHeaders, setResponseStatus, send, getRequestHeaders, setResponseHeader, getRequestURL, getResponseHeader, getRequestHost, getRequestProtocol, setHeader, getHeader, getQuery as getQuery$1, getResponseStatus, createError, readBody, getRouterParam, lazyEventHandler, useBase, createApp, createRouter as createRouter$1, toNodeListener, getRouter<PERSON>ara<PERSON>, getResponseStatusText } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/h3/dist/index.mjs';
import { escapeHtml } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/@vue/shared/dist/shared.cjs.js';
import { XMLParser } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/fast-xml-parser/src/fxp.js';
import { createRenderer, getRequestDependencies, getPreloadLinks, getPrefetchLinks } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/vue-bundle-renderer/dist/runtime.mjs';
import { parseURL, withoutBase, joinURL, getQuery, withQuery, withTrailingSlash, hasProtocol, withHttps, withoutProtocol, withoutTrailingSlash, withLeadingSlash, withBase, parsePath, parseQuery, stringifyQuery, encodePath, stringifyParsedURL, joinRelativeURL } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/ufo/dist/index.mjs';
import { renderToString } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/vue/server-renderer/index.mjs';
import { klona } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/klona/dist/index.mjs';
import defu, { defuFn, defu as defu$1, createDefu } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/defu/dist/defu.mjs';
import destr, { destr as destr$1 } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/destr/dist/index.mjs';
import { snakeCase } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/scule/dist/index.mjs';
import { createHead as createHead$1, propsToString, renderSSRHead } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/unhead/dist/server.mjs';
import { stringify, uneval } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/devalue/index.js';
import { isVNode, toValue, isRef } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/vue/index.mjs';
import { DeprecationsPlugin, PromisesPlugin, TemplateParamsPlugin, AliasSortingPlugin } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/unhead/dist/plugins.mjs';
import { createHooks } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/hookable/dist/index.mjs';
import { createFetch, Headers as Headers$1 } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/ofetch/dist/node.mjs';
import { fetchNodeRequestHandler, callNodeRequestHandler } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/node-mock-http/dist/index.mjs';
import { createStorage, prefixStorage } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/unstorage/dist/index.mjs';
import unstorage_47drivers_47fs from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/unstorage/drivers/fs.mjs';
import { digest } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/ohash/dist/index.mjs';
import { toRouteMatcher, createRouter } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/radix3/dist/index.mjs';
import { readFile } from 'node:fs/promises';
import consola, { consola as consola$1, createConsola } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/consola/dist/index.mjs';
import { ErrorParser } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/youch-core/build/index.js';
import { Youch } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/youch/build/index.js';
import { SourceMapConsumer } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/nitropack/node_modules/source-map/source-map.js';
import { AsyncLocalStorage } from 'node:async_hooks';
import { getContext } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/unctx/dist/index.mjs';
import { captureRawStackTrace, parseRawStackTrace } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/errx/dist/index.js';
import devalue from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/@nuxt/devalue/dist/devalue.mjs';
import { walkResolver } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/unhead/dist/utils.mjs';
import { isAbsolute } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/pathe/dist/index.mjs';
import localAdapter from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/db0/dist/connectors/better-sqlite3.mjs';
import { fileURLToPath } from 'node:url';
import { ipxFSStorage, ipxHttpStorage, createIPX, createIPXH3Handler } from 'file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/node_modules/ipx/dist/index.mjs';

const serverAssets = [{"baseName":"server","dir":"E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/server/assets"}];

const assets = createStorage();

for (const asset of serverAssets) {
  assets.mount(asset.baseName, unstorage_47drivers_47fs({ base: asset.dir, ignore: (asset?.ignore || []) }));
}

const storage = createStorage({});

storage.mount('/assets', assets);

storage.mount('data', unstorage_47drivers_47fs({"driver":"fs","base":"./data"}));
storage.mount('root', unstorage_47drivers_47fs({"driver":"fs","readOnly":true,"base":"E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan","watchOptions":{"ignored":[null]}}));
storage.mount('src', unstorage_47drivers_47fs({"driver":"fs","readOnly":true,"base":"E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/server","watchOptions":{"ignored":[null]}}));
storage.mount('build', unstorage_47drivers_47fs({"driver":"fs","readOnly":false,"base":"E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/.nuxt"}));
storage.mount('cache', unstorage_47drivers_47fs({"driver":"fs","readOnly":false,"base":"E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/.nuxt/cache"}));

function useStorage(base = "") {
  return base ? prefixStorage(storage, base) : storage;
}

const Hasher = /* @__PURE__ */ (() => {
  class Hasher2 {
    buff = "";
    #context = /* @__PURE__ */ new Map();
    write(str) {
      this.buff += str;
    }
    dispatch(value) {
      const type = value === null ? "null" : typeof value;
      return this[type](value);
    }
    object(object) {
      if (object && typeof object.toJSON === "function") {
        return this.object(object.toJSON());
      }
      const objString = Object.prototype.toString.call(object);
      let objType = "";
      const objectLength = objString.length;
      objType = objectLength < 10 ? "unknown:[" + objString + "]" : objString.slice(8, objectLength - 1);
      objType = objType.toLowerCase();
      let objectNumber = null;
      if ((objectNumber = this.#context.get(object)) === void 0) {
        this.#context.set(object, this.#context.size);
      } else {
        return this.dispatch("[CIRCULAR:" + objectNumber + "]");
      }
      if (typeof Buffer !== "undefined" && Buffer.isBuffer && Buffer.isBuffer(object)) {
        this.write("buffer:");
        return this.write(object.toString("utf8"));
      }
      if (objType !== "object" && objType !== "function" && objType !== "asyncfunction") {
        if (this[objType]) {
          this[objType](object);
        } else {
          this.unknown(object, objType);
        }
      } else {
        const keys = Object.keys(object).sort();
        const extraKeys = [];
        this.write("object:" + (keys.length + extraKeys.length) + ":");
        const dispatchForKey = (key) => {
          this.dispatch(key);
          this.write(":");
          this.dispatch(object[key]);
          this.write(",");
        };
        for (const key of keys) {
          dispatchForKey(key);
        }
        for (const key of extraKeys) {
          dispatchForKey(key);
        }
      }
    }
    array(arr, unordered) {
      unordered = unordered === void 0 ? false : unordered;
      this.write("array:" + arr.length + ":");
      if (!unordered || arr.length <= 1) {
        for (const entry of arr) {
          this.dispatch(entry);
        }
        return;
      }
      const contextAdditions = /* @__PURE__ */ new Map();
      const entries = arr.map((entry) => {
        const hasher = new Hasher2();
        hasher.dispatch(entry);
        for (const [key, value] of hasher.#context) {
          contextAdditions.set(key, value);
        }
        return hasher.toString();
      });
      this.#context = contextAdditions;
      entries.sort();
      return this.array(entries, false);
    }
    date(date) {
      return this.write("date:" + date.toJSON());
    }
    symbol(sym) {
      return this.write("symbol:" + sym.toString());
    }
    unknown(value, type) {
      this.write(type);
      if (!value) {
        return;
      }
      this.write(":");
      if (value && typeof value.entries === "function") {
        return this.array(
          [...value.entries()],
          true
          /* ordered */
        );
      }
    }
    error(err) {
      return this.write("error:" + err.toString());
    }
    boolean(bool) {
      return this.write("bool:" + bool);
    }
    string(string) {
      this.write("string:" + string.length + ":");
      this.write(string);
    }
    function(fn) {
      this.write("fn:");
      if (isNativeFunction(fn)) {
        this.dispatch("[native]");
      } else {
        this.dispatch(fn.toString());
      }
    }
    number(number) {
      return this.write("number:" + number);
    }
    null() {
      return this.write("Null");
    }
    undefined() {
      return this.write("Undefined");
    }
    regexp(regex) {
      return this.write("regex:" + regex.toString());
    }
    arraybuffer(arr) {
      this.write("arraybuffer:");
      return this.dispatch(new Uint8Array(arr));
    }
    url(url) {
      return this.write("url:" + url.toString());
    }
    map(map) {
      this.write("map:");
      const arr = [...map];
      return this.array(arr, false);
    }
    set(set) {
      this.write("set:");
      const arr = [...set];
      return this.array(arr, false);
    }
    bigint(number) {
      return this.write("bigint:" + number.toString());
    }
  }
  for (const type of [
    "uint8array",
    "uint8clampedarray",
    "unt8array",
    "uint16array",
    "unt16array",
    "uint32array",
    "unt32array",
    "float32array",
    "float64array"
  ]) {
    Hasher2.prototype[type] = function(arr) {
      this.write(type + ":");
      return this.array([...arr], false);
    };
  }
  function isNativeFunction(f) {
    if (typeof f !== "function") {
      return false;
    }
    return Function.prototype.toString.call(f).slice(
      -15
      /* "[native code] }".length */
    ) === "[native code] }";
  }
  return Hasher2;
})();
function serialize(object) {
  const hasher = new Hasher();
  hasher.dispatch(object);
  return hasher.buff;
}
function hash(value) {
  return digest(typeof value === "string" ? value : serialize(value)).replace(/[-_]/g, "").slice(0, 10);
}

function defaultCacheOptions() {
  return {
    name: "_",
    base: "/cache",
    swr: true,
    maxAge: 1
  };
}
function defineCachedFunction(fn, opts = {}) {
  opts = { ...defaultCacheOptions(), ...opts };
  const pending = {};
  const group = opts.group || "nitro/functions";
  const name = opts.name || fn.name || "_";
  const integrity = opts.integrity || hash([fn, opts]);
  const validate = opts.validate || ((entry) => entry.value !== void 0);
  async function get(key, resolver, shouldInvalidateCache, event) {
    const cacheKey = [opts.base, group, name, key + ".json"].filter(Boolean).join(":").replace(/:\/$/, ":index");
    let entry = await useStorage().getItem(cacheKey).catch((error) => {
      console.error(`[cache] Cache read error.`, error);
      useNitroApp().captureError(error, { event, tags: ["cache"] });
    }) || {};
    if (typeof entry !== "object") {
      entry = {};
      const error = new Error("Malformed data read from cache.");
      console.error("[cache]", error);
      useNitroApp().captureError(error, { event, tags: ["cache"] });
    }
    const ttl = (opts.maxAge ?? 0) * 1e3;
    if (ttl) {
      entry.expires = Date.now() + ttl;
    }
    const expired = shouldInvalidateCache || entry.integrity !== integrity || ttl && Date.now() - (entry.mtime || 0) > ttl || validate(entry) === false;
    const _resolve = async () => {
      const isPending = pending[key];
      if (!isPending) {
        if (entry.value !== void 0 && (opts.staleMaxAge || 0) >= 0 && opts.swr === false) {
          entry.value = void 0;
          entry.integrity = void 0;
          entry.mtime = void 0;
          entry.expires = void 0;
        }
        pending[key] = Promise.resolve(resolver());
      }
      try {
        entry.value = await pending[key];
      } catch (error) {
        if (!isPending) {
          delete pending[key];
        }
        throw error;
      }
      if (!isPending) {
        entry.mtime = Date.now();
        entry.integrity = integrity;
        delete pending[key];
        if (validate(entry) !== false) {
          let setOpts;
          if (opts.maxAge && !opts.swr) {
            setOpts = { ttl: opts.maxAge };
          }
          const promise = useStorage().setItem(cacheKey, entry, setOpts).catch((error) => {
            console.error(`[cache] Cache write error.`, error);
            useNitroApp().captureError(error, { event, tags: ["cache"] });
          });
          if (event?.waitUntil) {
            event.waitUntil(promise);
          }
        }
      }
    };
    const _resolvePromise = expired ? _resolve() : Promise.resolve();
    if (entry.value === void 0) {
      await _resolvePromise;
    } else if (expired && event && event.waitUntil) {
      event.waitUntil(_resolvePromise);
    }
    if (opts.swr && validate(entry) !== false) {
      _resolvePromise.catch((error) => {
        console.error(`[cache] SWR handler error.`, error);
        useNitroApp().captureError(error, { event, tags: ["cache"] });
      });
      return entry;
    }
    return _resolvePromise.then(() => entry);
  }
  return async (...args) => {
    const shouldBypassCache = await opts.shouldBypassCache?.(...args);
    if (shouldBypassCache) {
      return fn(...args);
    }
    const key = await (opts.getKey || getKey)(...args);
    const shouldInvalidateCache = await opts.shouldInvalidateCache?.(...args);
    const entry = await get(
      key,
      () => fn(...args),
      shouldInvalidateCache,
      args[0] && isEvent(args[0]) ? args[0] : void 0
    );
    let value = entry.value;
    if (opts.transform) {
      value = await opts.transform(entry, ...args) || value;
    }
    return value;
  };
}
function cachedFunction(fn, opts = {}) {
  return defineCachedFunction(fn, opts);
}
function getKey(...args) {
  return args.length > 0 ? hash(args) : "";
}
function escapeKey(key) {
  return String(key).replace(/\W/g, "");
}
function defineCachedEventHandler(handler, opts = defaultCacheOptions()) {
  const variableHeaderNames = (opts.varies || []).filter(Boolean).map((h) => h.toLowerCase()).sort();
  const _opts = {
    ...opts,
    getKey: async (event) => {
      const customKey = await opts.getKey?.(event);
      if (customKey) {
        return escapeKey(customKey);
      }
      const _path = event.node.req.originalUrl || event.node.req.url || event.path;
      let _pathname;
      try {
        _pathname = escapeKey(decodeURI(parseURL(_path).pathname)).slice(0, 16) || "index";
      } catch {
        _pathname = "-";
      }
      const _hashedPath = `${_pathname}.${hash(_path)}`;
      const _headers = variableHeaderNames.map((header) => [header, event.node.req.headers[header]]).map(([name, value]) => `${escapeKey(name)}.${hash(value)}`);
      return [_hashedPath, ..._headers].join(":");
    },
    validate: (entry) => {
      if (!entry.value) {
        return false;
      }
      if (entry.value.code >= 400) {
        return false;
      }
      if (entry.value.body === void 0) {
        return false;
      }
      if (entry.value.headers.etag === "undefined" || entry.value.headers["last-modified"] === "undefined") {
        return false;
      }
      return true;
    },
    group: opts.group || "nitro/handlers",
    integrity: opts.integrity || hash([handler, opts])
  };
  const _cachedHandler = cachedFunction(
    async (incomingEvent) => {
      const variableHeaders = {};
      for (const header of variableHeaderNames) {
        const value = incomingEvent.node.req.headers[header];
        if (value !== void 0) {
          variableHeaders[header] = value;
        }
      }
      const reqProxy = cloneWithProxy(incomingEvent.node.req, {
        headers: variableHeaders
      });
      const resHeaders = {};
      let _resSendBody;
      const resProxy = cloneWithProxy(incomingEvent.node.res, {
        statusCode: 200,
        writableEnded: false,
        writableFinished: false,
        headersSent: false,
        closed: false,
        getHeader(name) {
          return resHeaders[name];
        },
        setHeader(name, value) {
          resHeaders[name] = value;
          return this;
        },
        getHeaderNames() {
          return Object.keys(resHeaders);
        },
        hasHeader(name) {
          return name in resHeaders;
        },
        removeHeader(name) {
          delete resHeaders[name];
        },
        getHeaders() {
          return resHeaders;
        },
        end(chunk, arg2, arg3) {
          if (typeof chunk === "string") {
            _resSendBody = chunk;
          }
          if (typeof arg2 === "function") {
            arg2();
          }
          if (typeof arg3 === "function") {
            arg3();
          }
          return this;
        },
        write(chunk, arg2, arg3) {
          if (typeof chunk === "string") {
            _resSendBody = chunk;
          }
          if (typeof arg2 === "function") {
            arg2(void 0);
          }
          if (typeof arg3 === "function") {
            arg3();
          }
          return true;
        },
        writeHead(statusCode, headers2) {
          this.statusCode = statusCode;
          if (headers2) {
            if (Array.isArray(headers2) || typeof headers2 === "string") {
              throw new TypeError("Raw headers  is not supported.");
            }
            for (const header in headers2) {
              const value = headers2[header];
              if (value !== void 0) {
                this.setHeader(
                  header,
                  value
                );
              }
            }
          }
          return this;
        }
      });
      const event = createEvent(reqProxy, resProxy);
      event.fetch = (url, fetchOptions) => fetchWithEvent(event, url, fetchOptions, {
        fetch: useNitroApp().localFetch
      });
      event.$fetch = (url, fetchOptions) => fetchWithEvent(event, url, fetchOptions, {
        fetch: globalThis.$fetch
      });
      event.waitUntil = incomingEvent.waitUntil;
      event.context = incomingEvent.context;
      event.context.cache = {
        options: _opts
      };
      const body = await handler(event) || _resSendBody;
      const headers = event.node.res.getHeaders();
      headers.etag = String(
        headers.Etag || headers.etag || `W/"${hash(body)}"`
      );
      headers["last-modified"] = String(
        headers["Last-Modified"] || headers["last-modified"] || (/* @__PURE__ */ new Date()).toUTCString()
      );
      const cacheControl = [];
      if (opts.swr) {
        if (opts.maxAge) {
          cacheControl.push(`s-maxage=${opts.maxAge}`);
        }
        if (opts.staleMaxAge) {
          cacheControl.push(`stale-while-revalidate=${opts.staleMaxAge}`);
        } else {
          cacheControl.push("stale-while-revalidate");
        }
      } else if (opts.maxAge) {
        cacheControl.push(`max-age=${opts.maxAge}`);
      }
      if (cacheControl.length > 0) {
        headers["cache-control"] = cacheControl.join(", ");
      }
      const cacheEntry = {
        code: event.node.res.statusCode,
        headers,
        body
      };
      return cacheEntry;
    },
    _opts
  );
  return defineEventHandler(async (event) => {
    if (opts.headersOnly) {
      if (handleCacheHeaders(event, { maxAge: opts.maxAge })) {
        return;
      }
      return handler(event);
    }
    const response = await _cachedHandler(
      event
    );
    if (event.node.res.headersSent || event.node.res.writableEnded) {
      return response.body;
    }
    if (handleCacheHeaders(event, {
      modifiedTime: new Date(response.headers["last-modified"]),
      etag: response.headers.etag,
      maxAge: opts.maxAge
    })) {
      return;
    }
    event.node.res.statusCode = response.code;
    for (const name in response.headers) {
      const value = response.headers[name];
      if (name === "set-cookie") {
        event.node.res.appendHeader(
          name,
          splitCookiesString(value)
        );
      } else {
        if (value !== void 0) {
          event.node.res.setHeader(name, value);
        }
      }
    }
    return response.body;
  });
}
function cloneWithProxy(obj, overrides) {
  return new Proxy(obj, {
    get(target, property, receiver) {
      if (property in overrides) {
        return overrides[property];
      }
      return Reflect.get(target, property, receiver);
    },
    set(target, property, value, receiver) {
      if (property in overrides) {
        overrides[property] = value;
        return true;
      }
      return Reflect.set(target, property, value, receiver);
    }
  });
}
const cachedEventHandler = defineCachedEventHandler;

const inlineAppConfig = {
  "nuxt": {}
};



const appConfig = defuFn(inlineAppConfig);

function getEnv(key, opts) {
  const envKey = snakeCase(key).toUpperCase();
  return destr(
    process.env[opts.prefix + envKey] ?? process.env[opts.altPrefix + envKey]
  );
}
function _isObject(input) {
  return typeof input === "object" && !Array.isArray(input);
}
function applyEnv(obj, opts, parentKey = "") {
  for (const key in obj) {
    const subKey = parentKey ? `${parentKey}_${key}` : key;
    const envValue = getEnv(subKey, opts);
    if (_isObject(obj[key])) {
      if (_isObject(envValue)) {
        obj[key] = { ...obj[key], ...envValue };
        applyEnv(obj[key], opts, subKey);
      } else if (envValue === void 0) {
        applyEnv(obj[key], opts, subKey);
      } else {
        obj[key] = envValue ?? obj[key];
      }
    } else {
      obj[key] = envValue ?? obj[key];
    }
    if (opts.envExpansion && typeof obj[key] === "string") {
      obj[key] = _expandFromEnv(obj[key]);
    }
  }
  return obj;
}
const envExpandRx = /\{\{([^{}]*)\}\}/g;
function _expandFromEnv(value) {
  return value.replace(envExpandRx, (match, key) => {
    return process.env[key] || match;
  });
}

const _inlineRuntimeConfig = {
  "app": {
    "baseURL": "/",
    "buildId": "dev",
    "buildAssetsDir": "/_nuxt/",
    "cdnURL": ""
  },
  "nitro": {
    "envPrefix": "NUXT_",
    "routeRules": {
      "/__nuxt_error": {
        "cache": false
      },
      "/api/**": {
        "prerender": true,
        "cache": {
          "maxAge": 86400
        }
      },
      "/sewa-mobil-xpander/": {
        "redirect": {
          "to": "/sewa-mobil-jogja/sewa-mobil-xpander-jogja/",
          "statusCode": 307
        }
      },
      "/sewa-mobil-all-new-innova/": {
        "redirect": {
          "to": "/sewa-mobil-jogja/sewa-mobil-all-new-innova-jogja/",
          "statusCode": 307
        }
      },
      "/sewa-mobil-hyundai-h1/": {
        "redirect": {
          "to": "/sewa-mobil-jogja/sewa-mobil-hyundai-h-1-jogja/",
          "statusCode": 307
        }
      },
      "/sewa-mobil-all-new-avanza/": {
        "redirect": {
          "to": "/sewa-mobil-jogja/sewa-mobil-avanza-jogja/",
          "statusCode": 307
        }
      },
      "/sewa-mobil-hyundai-h1-royale/": {
        "redirect": {
          "to": "/sewa-mobil-jogja/sewa-mobil-hyundai-h1-royale/",
          "statusCode": 307
        }
      },
      "/sewa-mobil-all-new-innova-zenix/": {
        "redirect": {
          "to": "/sewa-mobil-jogja/sewa-mobil-all-new-innova-zenix/",
          "statusCode": 307
        }
      },
      "/sewa-mobil-elf-short-11-seats/": {
        "redirect": {
          "to": "/sewa-mobil-jogja/sewa-mobil-elf-short-11-seats/",
          "statusCode": 307
        }
      },
      "/sewa-mobil-fortuner-vrz/": {
        "redirect": {
          "to": "/sewa-mobil-jogja/sewa-mobil-fortuner-vrz/",
          "statusCode": 307
        }
      },
      "/sewa-mobil-new-alphard-transformer/": {
        "redirect": {
          "to": "/sewa-mobil-jogja/sewa-mobil-new-alphard-transformer/",
          "statusCode": 307
        }
      },
      "/sewa-mobil-hiace-commuter-14-seats/": {
        "redirect": {
          "to": "/sewa-mobil-jogja/sewa-mobil-hiace-commuter-14-seats/",
          "statusCode": 307
        }
      },
      "/sewa-hiace-premio-12-14-seats/": {
        "redirect": {
          "to": "/sewa-mobil-jogja/sewa-mobil-hiace-premio-12-14-seats/",
          "statusCode": 307
        }
      },
      "/sewa-bigbus-40-50-seats/": {
        "redirect": {
          "to": "/sewa-mobil-jogja/sewa-mobil-big-bus-40-50-seats/",
          "statusCode": 307
        }
      },
      "/sewa-mobil-grand-avanza/": {
        "redirect": {
          "to": "/sewa-mobil-jogja/sewa-mobil-grand-avanza-jogja/",
          "statusCode": 307
        }
      },
      "/sewa-hiace-premio-luxury/": {
        "redirect": {
          "to": "/sewa-mobil-jogja/sewa-mobil-hiace-premio-luxury/",
          "statusCode": 307
        }
      },
      "/sewa-medium-bus-31-33-seats/": {
        "redirect": {
          "to": "/sewa-mobil-jogja/sewa-mobil-medium-bus-31-33-seats/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-dan-hotel-brahmasta/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-brahmasta-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-dan-hotel-nagapasa/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-nagapasa-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-hotel-andanu/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-andanu-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-dan-hotel-kalimasada/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-kalimasada-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-dan-hotel-gandiwa/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-gandiwa-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-dan-hotel-rujapala/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-rujapala-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-dan-hotel-pancanaka/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-pancanaka-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-dan-hotel-nenggala/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-nenggala-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-dan-hotel-sudarsana/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-sudarsana-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-hotel-nagaloka/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-nagaloka-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-hotel-sempati/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-sempati-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-hotel-taksaka/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-taksaka-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-hotel-bendana/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-bendana-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-hotel-antaboga/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-antaboga-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-hotel-jatayu/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-jatayu-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-hotel-bisma/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-bisma-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-hotel-4d-anoman/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-anoman-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-hotel-4d-arjuna/": {
        "redirect": {
          "to": "/paket-wisata-jogja/paket-arjuna-dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-bisma/": {
        "redirect": {
          "to": "/paket-wisata-jogja/4d-bisma/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-4d-anoman/": {
        "redirect": {
          "to": "/paket-wisata-jogja/4d-anoman/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-4d-arjuna/": {
        "redirect": {
          "to": "/paket-wisata-jogja/4d-arjuna/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-nagaloka/": {
        "redirect": {
          "to": "/paket-wisata-jogja/4d-nagaloka/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-abimanyu/": {
        "redirect": {
          "to": "/paket-wisata-jogja/abimanyu/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-andanu/": {
        "redirect": {
          "to": "/paket-wisata-jogja/andanu/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-anoman/": {
        "redirect": {
          "to": "/paket-wisata-jogja/anoman/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-antaboga/": {
        "redirect": {
          "to": "/paket-wisata-jogja/antaboga/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-antasena/": {
        "redirect": {
          "to": "/paket-wisata-jogja/antasena/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-arjuna/": {
        "redirect": {
          "to": "/paket-wisata-jogja/arjuna/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-bendana/": {
        "redirect": {
          "to": "/paket-wisata-jogja/bendana/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-brahmasta/": {
        "redirect": {
          "to": "/paket-wisata-jogja/brahmasta/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-bugisan/": {
        "redirect": {
          "to": "/paket-wisata-jogja/bugisan/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-gandiwa/": {
        "redirect": {
          "to": "/paket-wisata-jogja/gandiwa/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-gatotkaca/": {
        "redirect": {
          "to": "/paket-wisata-jogja/gatotkaca/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-jagakarya/": {
        "redirect": {
          "to": "/paket-wisata-jogja/jagakarya/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-jatayu/": {
        "redirect": {
          "to": "/paket-wisata-jogja/jatayu/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-kalimasada/": {
        "redirect": {
          "to": "/paket-wisata-jogja/kalimasada/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-kresna/": {
        "redirect": {
          "to": "/paket-wisata-jogja/kresna/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-mantrijero/": {
        "redirect": {
          "to": "/paket-wisata-jogja/mantrijero/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-nagapasa/": {
        "redirect": {
          "to": "/paket-wisata-jogja/nagapasa/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-nenggala/": {
        "redirect": {
          "to": "/paket-wisata-jogja/nenggala/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-pancanaka/": {
        "redirect": {
          "to": "/paket-wisata-jogja/pancanaka/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-rujapala/": {
        "redirect": {
          "to": "/paket-wisata-jogja/rujapala/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-sempati/": {
        "redirect": {
          "to": "/paket-wisata-jogja/sempati/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-sudarsana/": {
        "redirect": {
          "to": "/paket-wisata-jogja/sudarsana/",
          "statusCode": 307
        }
      },
      "/paket-wisata-jogja-taksaka/": {
        "redirect": {
          "to": "/paket-wisata-jogja/taksaka/",
          "statusCode": 307
        }
      },
      "/paket-wisata-tanpa-hotel/": {
        "redirect": {
          "to": "/paket-wisata-jogja/tanpa-hotel/",
          "statusCode": 307
        }
      },
      "/paket-wisata-hotel/": {
        "redirect": {
          "to": "/paket-wisata-jogja/dengan-hotel/",
          "statusCode": 307
        }
      },
      "/paket-gathering-jogja-ananta/": {
        "redirect": {
          "to": "/paket-gathering/paket-gathering-jogja-ananta/",
          "statusCode": 307
        }
      },
      "/paket-gathering-jogja-bhagawanta/": {
        "redirect": {
          "to": "/paket-gathering/paket-gathering-jogja-bhagawanta/",
          "statusCode": 307
        }
      },
      "/paket-gathering-jogja-jayantaka/": {
        "redirect": {
          "to": "/paket-gathering/paket-gathering-jogja-jayantaka/",
          "statusCode": 307
        }
      },
      "/paket-gathering-jogja-padmana/": {
        "redirect": {
          "to": "/paket-gathering/paket-gathering-jogja-padmana/",
          "statusCode": 307
        }
      },
      "/paket-honeymoon-ramayana/": {
        "redirect": {
          "to": "/paket-honeymoon/paket-honeymoon-ramayana/",
          "statusCode": 307
        }
      },
      "/paket-honeymoon-mahabarata/": {
        "redirect": {
          "to": "/paket-honeymoon/paket-honeymoon-mahabrata/",
          "statusCode": 307
        }
      },
      "/paket-honeymoon-anjani/": {
        "redirect": {
          "to": "/paket-honeymoon/paket-honeymoon-anjani/",
          "statusCode": 307
        }
      },
      "/paket-honeymoon-srikandi/": {
        "redirect": {
          "to": "/paket-honeymoon/paket-honeymoon-srikandi/",
          "statusCode": 307
        }
      },
      "/__sitemap__/style.xsl": {
        "headers": {
          "Content-Type": "application/xslt+xml"
        }
      },
      "/sitemap.xml": {
        "headers": {
          "Content-Type": "text/xml; charset=UTF-8",
          "Cache-Control": "public, max-age=600, must-revalidate",
          "X-Sitemap-Prerendered": "2025-06-26T12:29:58.861Z"
        }
      },
      "/__nuxt_content/**": {
        "robots": false
      },
      "/__nuxt_content/posts/sql_dump.txt": {
        "prerender": true
      },
      "/_nuxt/builds/meta/**": {
        "headers": {
          "cache-control": "public, max-age=31536000, immutable"
        }
      },
      "/_nuxt/builds/**": {
        "headers": {
          "cache-control": "public, max-age=1, immutable"
        }
      },
      "/data/**": {
        "headers": {
          "cache-control": "public, max-age=31536000, immutable"
        }
      }
    }
  },
  "public": {
    "content": {
      "wsUrl": "ws://localhost:4000/"
    },
    "mdc": {
      "components": {
        "prose": true,
        "map": {}
      },
      "headings": {
        "anchorLinks": {
          "h1": false,
          "h2": true,
          "h3": true,
          "h4": true,
          "h5": false,
          "h6": false
        }
      }
    }
  },
  "sitemap": {
    "isI18nMapped": false,
    "sitemapName": "sitemap.xml",
    "isMultiSitemap": false,
    "excludeAppSources": [],
    "cacheMaxAgeSeconds": 0,
    "autoLastmod": false,
    "defaultSitemapsChunkSize": 1000,
    "minify": false,
    "sortEntries": true,
    "debug": false,
    "discoverImages": true,
    "discoverVideos": true,
    "sitemapsPathPrefix": "/__sitemap__/",
    "isNuxtContentDocumentDriven": false,
    "xsl": "/__sitemap__/style.xsl",
    "xslTips": true,
    "xslColumns": [
      {
        "label": "URL",
        "width": "50%"
      },
      {
        "label": "Images",
        "width": "25%",
        "select": "count(image:image)"
      },
      {
        "label": "Last Updated",
        "width": "25%",
        "select": "concat(substring(sitemap:lastmod,0,11),concat(' ', substring(sitemap:lastmod,12,5)),concat(' ', substring(sitemap:lastmod,20,6)))"
      }
    ],
    "credits": true,
    "version": "7.4.1",
    "sitemaps": {
      "sitemap.xml": {
        "sitemapName": "sitemap.xml",
        "route": "sitemap.xml",
        "defaults": {
          "changefreq": "daily",
          "priority": 0.5,
          "lastmod": "2025-06-26T12:29:58.685Z"
        },
        "include": [],
        "exclude": [
          "/_**",
          "/_nuxt/**",
          "/__nuxt_content/**"
        ],
        "includeAppSources": true
      }
    }
  },
  "content": {
    "databaseVersion": "v3.5.0",
    "version": "3.6.1",
    "database": {
      "type": "sqlite",
      "filename": "./contents.sqlite"
    },
    "localDatabase": {
      "type": "sqlite",
      "filename": "E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/.data/content/contents.sqlite"
    },
    "integrityCheck": true
  },
  "nuxt-site-config": {
    "stack": [
      {
        "_context": "system",
        "_priority": -15,
        "name": "jogjaliburan",
        "env": "development"
      },
      {
        "_context": "package.json",
        "_priority": -10,
        "name": "travel-tour-website"
      },
      {
        "_priority": -3,
        "_context": "nuxt-site-config:config",
        "url": "https://www.jogjaliburan.com/",
        "trailingSlash": true
      },
      {
        "_context": "buildEnv",
        "_priority": -1,
        "url": "https://www.jogjaliburan.com/",
        "name": "My Awesome Website"
      }
    ],
    "version": "3.2.1",
    "debug": false,
    "multiTenancy": []
  },
  "ipx": {
    "baseURL": "/_ipx",
    "alias": {},
    "fs": {
      "dir": [
        "E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/public"
      ]
    },
    "http": {
      "domains": [
        "liburanjogja.b-cdn.net",
        "www.jogjaliburan.com",
        "jogjaliburan.com"
      ]
    }
  }
};
const envOptions = {
  prefix: "NITRO_",
  altPrefix: _inlineRuntimeConfig.nitro.envPrefix ?? process.env.NITRO_ENV_PREFIX ?? "_",
  envExpansion: _inlineRuntimeConfig.nitro.envExpansion ?? process.env.NITRO_ENV_EXPANSION ?? false
};
const _sharedRuntimeConfig = _deepFreeze(
  applyEnv(klona(_inlineRuntimeConfig), envOptions)
);
function useRuntimeConfig(event) {
  if (!event) {
    return _sharedRuntimeConfig;
  }
  if (event.context.nitro.runtimeConfig) {
    return event.context.nitro.runtimeConfig;
  }
  const runtimeConfig = klona(_inlineRuntimeConfig);
  applyEnv(runtimeConfig, envOptions);
  event.context.nitro.runtimeConfig = runtimeConfig;
  return runtimeConfig;
}
_deepFreeze(klona(appConfig));
function _deepFreeze(object) {
  const propNames = Object.getOwnPropertyNames(object);
  for (const name of propNames) {
    const value = object[name];
    if (value && typeof value === "object") {
      _deepFreeze(value);
    }
  }
  return Object.freeze(object);
}
new Proxy(/* @__PURE__ */ Object.create(null), {
  get: (_, prop) => {
    console.warn(
      "Please use `useRuntimeConfig()` instead of accessing config directly."
    );
    const runtimeConfig = useRuntimeConfig();
    if (prop in runtimeConfig) {
      return runtimeConfig[prop];
    }
    return void 0;
  }
});

const config = useRuntimeConfig();
const _routeRulesMatcher = toRouteMatcher(
  createRouter({ routes: config.nitro.routeRules })
);
function createRouteRulesHandler(ctx) {
  return eventHandler((event) => {
    const routeRules = getRouteRules(event);
    if (routeRules.headers) {
      setHeaders(event, routeRules.headers);
    }
    if (routeRules.redirect) {
      let target = routeRules.redirect.to;
      if (target.endsWith("/**")) {
        let targetPath = event.path;
        const strpBase = routeRules.redirect._redirectStripBase;
        if (strpBase) {
          targetPath = withoutBase(targetPath, strpBase);
        }
        target = joinURL(target.slice(0, -3), targetPath);
      } else if (event.path.includes("?")) {
        const query = getQuery(event.path);
        target = withQuery(target, query);
      }
      return sendRedirect(event, target, routeRules.redirect.statusCode);
    }
    if (routeRules.proxy) {
      let target = routeRules.proxy.to;
      if (target.endsWith("/**")) {
        let targetPath = event.path;
        const strpBase = routeRules.proxy._proxyStripBase;
        if (strpBase) {
          targetPath = withoutBase(targetPath, strpBase);
        }
        target = joinURL(target.slice(0, -3), targetPath);
      } else if (event.path.includes("?")) {
        const query = getQuery(event.path);
        target = withQuery(target, query);
      }
      return proxyRequest(event, target, {
        fetch: ctx.localFetch,
        ...routeRules.proxy
      });
    }
  });
}
function getRouteRules(event) {
  event.context._nitro = event.context._nitro || {};
  if (!event.context._nitro.routeRules) {
    event.context._nitro.routeRules = getRouteRulesForPath(
      withoutBase(event.path.split("?")[0], useRuntimeConfig().app.baseURL)
    );
  }
  return event.context._nitro.routeRules;
}
function getRouteRulesForPath(path) {
  return defu({}, ..._routeRulesMatcher.matchAll(path).reverse());
}

function _captureError(error, type) {
  console.error(`[${type}]`, error);
  useNitroApp().captureError(error, { tags: [type] });
}
function trapUnhandledNodeErrors() {
  process.on(
    "unhandledRejection",
    (error) => _captureError(error, "unhandledRejection")
  );
  process.on(
    "uncaughtException",
    (error) => _captureError(error, "uncaughtException")
  );
}
function joinHeaders(value) {
  return Array.isArray(value) ? value.join(", ") : String(value);
}
function normalizeFetchResponse(response) {
  if (!response.headers.has("set-cookie")) {
    return response;
  }
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: normalizeCookieHeaders(response.headers)
  });
}
function normalizeCookieHeader(header = "") {
  return splitCookiesString(joinHeaders(header));
}
function normalizeCookieHeaders(headers) {
  const outgoingHeaders = new Headers();
  for (const [name, header] of headers) {
    if (name === "set-cookie") {
      for (const cookie of normalizeCookieHeader(header)) {
        outgoingHeaders.append("set-cookie", cookie);
      }
    } else {
      outgoingHeaders.set(name, joinHeaders(header));
    }
  }
  return outgoingHeaders;
}

function isJsonRequest(event) {
  if (hasReqHeader(event, "accept", "text/html")) {
    return false;
  }
  return hasReqHeader(event, "accept", "application/json") || hasReqHeader(event, "user-agent", "curl/") || hasReqHeader(event, "user-agent", "httpie/") || hasReqHeader(event, "sec-fetch-mode", "cors") || event.path.startsWith("/api/") || event.path.endsWith(".json");
}
function hasReqHeader(event, name, includes) {
  const value = getRequestHeader(event, name);
  return value && typeof value === "string" && value.toLowerCase().includes(includes);
}

const errorHandler$0 = (async function errorhandler(error, event, { defaultHandler }) {
  if (event.handled || isJsonRequest(event)) {
    return;
  }
  const defaultRes = await defaultHandler(error, event, { json: true });
  const statusCode = error.statusCode || 500;
  if (statusCode === 404 && defaultRes.status === 302) {
    setResponseHeaders(event, defaultRes.headers);
    setResponseStatus(event, defaultRes.status, defaultRes.statusText);
    return send(event, JSON.stringify(defaultRes.body, null, 2));
  }
  if (typeof defaultRes.body !== "string" && Array.isArray(defaultRes.body.stack)) {
    defaultRes.body.stack = defaultRes.body.stack.join("\n");
  }
  const errorObject = defaultRes.body;
  const url = new URL(errorObject.url);
  errorObject.url = withoutBase(url.pathname, useRuntimeConfig(event).app.baseURL) + url.search + url.hash;
  errorObject.message ||= "Server Error";
  errorObject.data ||= error.data;
  errorObject.statusMessage ||= error.statusMessage;
  delete defaultRes.headers["content-type"];
  delete defaultRes.headers["content-security-policy"];
  setResponseHeaders(event, defaultRes.headers);
  const reqHeaders = getRequestHeaders(event);
  const isRenderingError = event.path.startsWith("/__nuxt_error") || !!reqHeaders["x-nuxt-error"];
  const res = isRenderingError ? null : await useNitroApp().localFetch(
    withQuery(joinURL(useRuntimeConfig(event).app.baseURL, "/__nuxt_error"), errorObject),
    {
      headers: { ...reqHeaders, "x-nuxt-error": "true" },
      redirect: "manual"
    }
  ).catch(() => null);
  if (event.handled) {
    return;
  }
  if (!res) {
    const { template } = await Promise.resolve().then(function () { return errorDev; }) ;
    {
      errorObject.description = errorObject.message;
    }
    setResponseHeader(event, "Content-Type", "text/html;charset=UTF-8");
    return send(event, template(errorObject));
  }
  const html = await res.text();
  for (const [header, value] of res.headers.entries()) {
    setResponseHeader(event, header, value);
  }
  setResponseStatus(event, res.status && res.status !== 200 ? res.status : defaultRes.status, res.statusText || defaultRes.statusText);
  return send(event, html);
});

function defineNitroErrorHandler(handler) {
  return handler;
}

const errorHandler$1 = defineNitroErrorHandler(
  async function defaultNitroErrorHandler(error, event) {
    const res = await defaultHandler(error, event);
    if (!event.node?.res.headersSent) {
      setResponseHeaders(event, res.headers);
    }
    setResponseStatus(event, res.status, res.statusText);
    return send(
      event,
      typeof res.body === "string" ? res.body : JSON.stringify(res.body, null, 2)
    );
  }
);
async function defaultHandler(error, event, opts) {
  const isSensitive = error.unhandled || error.fatal;
  const statusCode = error.statusCode || 500;
  const statusMessage = error.statusMessage || "Server Error";
  const url = getRequestURL(event, { xForwardedHost: true, xForwardedProto: true });
  if (statusCode === 404) {
    const baseURL = "/";
    if (/^\/[^/]/.test(baseURL) && !url.pathname.startsWith(baseURL)) {
      const redirectTo = `${baseURL}${url.pathname.slice(1)}${url.search}`;
      return {
        status: 302,
        statusText: "Found",
        headers: { location: redirectTo },
        body: `Redirecting...`
      };
    }
  }
  await loadStackTrace(error).catch(consola.error);
  const youch = new Youch();
  if (isSensitive && !opts?.silent) {
    const tags = [error.unhandled && "[unhandled]", error.fatal && "[fatal]"].filter(Boolean).join(" ");
    const ansiError = await (await youch.toANSI(error)).replaceAll(process.cwd(), ".");
    consola.error(
      `[request error] ${tags} [${event.method}] ${url}

`,
      ansiError
    );
  }
  const useJSON = opts?.json || !getRequestHeader(event, "accept")?.includes("text/html");
  const headers = {
    "content-type": useJSON ? "application/json" : "text/html",
    // Prevent browser from guessing the MIME types of resources.
    "x-content-type-options": "nosniff",
    // Prevent error page from being embedded in an iframe
    "x-frame-options": "DENY",
    // Prevent browsers from sending the Referer header
    "referrer-policy": "no-referrer",
    // Disable the execution of any js
    "content-security-policy": "script-src 'self' 'unsafe-inline'; object-src 'none'; base-uri 'self';"
  };
  if (statusCode === 404 || !getResponseHeader(event, "cache-control")) {
    headers["cache-control"] = "no-cache";
  }
  const body = useJSON ? {
    error: true,
    url,
    statusCode,
    statusMessage,
    message: error.message,
    data: error.data,
    stack: error.stack?.split("\n").map((line) => line.trim())
  } : await youch.toHTML(error, {
    request: {
      url: url.href,
      method: event.method,
      headers: getRequestHeaders(event)
    }
  });
  return {
    status: statusCode,
    statusText: statusMessage,
    headers,
    body
  };
}
async function loadStackTrace(error) {
  if (!(error instanceof Error)) {
    return;
  }
  const parsed = await new ErrorParser().defineSourceLoader(sourceLoader).parse(error);
  const stack = error.message + "\n" + parsed.frames.map((frame) => fmtFrame(frame)).join("\n");
  Object.defineProperty(error, "stack", { value: stack });
  if (error.cause) {
    await loadStackTrace(error.cause).catch(consola.error);
  }
}
async function sourceLoader(frame) {
  if (!frame.fileName || frame.fileType !== "fs" || frame.type === "native") {
    return;
  }
  if (frame.type === "app") {
    const rawSourceMap = await readFile(`${frame.fileName}.map`, "utf8").catch(() => {
    });
    if (rawSourceMap) {
      const consumer = await new SourceMapConsumer(rawSourceMap);
      const originalPosition = consumer.originalPositionFor({ line: frame.lineNumber, column: frame.columnNumber });
      if (originalPosition.source && originalPosition.line) {
        frame.fileName = resolve$1(dirname(frame.fileName), originalPosition.source);
        frame.lineNumber = originalPosition.line;
        frame.columnNumber = originalPosition.column || 0;
      }
    }
  }
  const contents = await readFile(frame.fileName, "utf8").catch(() => {
  });
  return contents ? { contents } : void 0;
}
function fmtFrame(frame) {
  if (frame.type === "native") {
    return frame.raw;
  }
  const src = `${frame.fileName || ""}:${frame.lineNumber}:${frame.columnNumber})`;
  return frame.functionName ? `at ${frame.functionName} (${src}` : `at ${src}`;
}

const errorHandlers = [errorHandler$0, errorHandler$1];

async function errorHandler(error, event) {
  for (const handler of errorHandlers) {
    try {
      await handler(error, event, { defaultHandler });
      if (event.handled) {
        return; // Response handled
      }
    } catch(error) {
      // Handler itself thrown, log and continue
      console.error(error);
    }
  }
  // H3 will handle fallback
}

const script = `
if (!window.__NUXT_DEVTOOLS_TIME_METRIC__) {
  Object.defineProperty(window, '__NUXT_DEVTOOLS_TIME_METRIC__', {
    value: {},
    enumerable: false,
    configurable: true,
  })
}
window.__NUXT_DEVTOOLS_TIME_METRIC__.appInit = Date.now()
`;

const _ZQMesIEr59jBBjSpaXwuV0xZ2ghuLgy5hYN3VBfogw = (function(nitro) {
  nitro.hooks.hook("render:html", (htmlContext) => {
    htmlContext.head.push(`<script>${script}<\/script>`);
  });
});

const rootDir = "E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan";

const appHead = {"meta":[{"name":"viewport","content":"width=device-width, initial-scale=1"},{"charset":"utf-8"},{"name":"description","content":"Professional travel tour website offering the best destinations around the world"},{"name":"google-site-verification","content":"LfcLcfVK2oDOl1qJ1s13gKrdTku3Xkpeo95zhyMPi70"}],"link":[{"rel":"stylesheet","href":"https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"},{"rel":"stylesheet","href":"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"}],"style":[],"script":[{"innerHTML":"(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\nnew Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\nj=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n})(window,document,'script','dataLayer','GTM-M4X39CC');","type":"text/javascript"},{"src":"https://www.googletagmanager.com/gtag/js?id=AW-*********","async":true},{"innerHTML":"\n                        window.dataLayer = window.dataLayer || [];\n                        function gtag(){dataLayer.push(arguments);}\n                        gtag('js', new Date());\n                        gtag('config', 'AW-*********');\n                    ","type":"text/javascript"}],"noscript":[{"innerHTML":"<iframe src=\"https://www.googletagmanager.com/ns.html?id=GTM-M4X39CC\"\nheight=\"0\" width=\"0\" style=\"display:none;visibility:hidden\"></iframe>"}],"title":"3J Tour - Liburan Terbaik di Jogja"};

const appRootTag = "div";

const appRootAttrs = {"id":"__nuxt"};

const appTeleportTag = "div";

const appTeleportAttrs = {"id":"teleports"};

const appId = "nuxt-app";

const devReducers = {
  VNode: (data) => isVNode(data) ? { type: data.type, props: data.props } : void 0,
  URL: (data) => data instanceof URL ? data.toString() : void 0
};
const asyncContext = getContext("nuxt-dev", { asyncContext: true, AsyncLocalStorage });
const _Utrd98Z9gx4fS91ckpl6CH5q3sbifaLjr88vCqf5le0 = (nitroApp) => {
  const handler = nitroApp.h3App.handler;
  nitroApp.h3App.handler = (event) => {
    return asyncContext.callAsync({ logs: [], event }, () => handler(event));
  };
  onConsoleLog((_log) => {
    const ctx = asyncContext.tryUse();
    if (!ctx) {
      return;
    }
    const rawStack = captureRawStackTrace();
    if (!rawStack || rawStack.includes("runtime/vite-node.mjs")) {
      return;
    }
    const trace = [];
    let filename = "";
    for (const entry of parseRawStackTrace(rawStack)) {
      if (entry.source === globalThis._importMeta_.url) {
        continue;
      }
      if (EXCLUDE_TRACE_RE.test(entry.source)) {
        continue;
      }
      filename ||= entry.source.replace(withTrailingSlash(rootDir), "");
      trace.push({
        ...entry,
        source: entry.source.startsWith("file://") ? entry.source.replace("file://", "") : entry.source
      });
    }
    const log = {
      ..._log,
      // Pass along filename to allow the client to display more info about where log comes from
      filename,
      // Clean up file names in stack trace
      stack: trace
    };
    ctx.logs.push(log);
  });
  nitroApp.hooks.hook("afterResponse", () => {
    const ctx = asyncContext.tryUse();
    if (!ctx) {
      return;
    }
    return nitroApp.hooks.callHook("dev:ssr-logs", { logs: ctx.logs, path: ctx.event.path });
  });
  nitroApp.hooks.hook("render:html", (htmlContext) => {
    const ctx = asyncContext.tryUse();
    if (!ctx) {
      return;
    }
    try {
      const reducers = Object.assign(/* @__PURE__ */ Object.create(null), devReducers, ctx.event.context._payloadReducers);
      htmlContext.bodyAppend.unshift(`<script type="application/json" data-nuxt-logs="${appId}">${stringify(ctx.logs, reducers)}<\/script>`);
    } catch (e) {
      const shortError = e instanceof Error && "toString" in e ? ` Received \`${e.toString()}\`.` : "";
      console.warn(`[nuxt] Failed to stringify dev server logs.${shortError} You can define your own reducer/reviver for rich types following the instructions in https://nuxt.com/docs/api/composables/use-nuxt-app#payload.`);
    }
  });
};
const EXCLUDE_TRACE_RE = /\/node_modules\/(?:.*\/)?(?:nuxt|nuxt-nightly|nuxt-edge|nuxt3|consola|@vue)\/|core\/runtime\/nitro/;
function onConsoleLog(callback) {
  consola$1.addReporter({
    log(logObj) {
      callback(logObj);
    }
  });
  consola$1.wrapConsole();
}

function normalizeSiteConfig(config) {
  if (typeof config.indexable !== "undefined")
    config.indexable = String(config.indexable) !== "false";
  if (typeof config.trailingSlash !== "undefined" && !config.trailingSlash)
    config.trailingSlash = String(config.trailingSlash) !== "false";
  if (config.url && !hasProtocol(String(config.url), { acceptRelative: true, strict: false }))
    config.url = withHttps(String(config.url));
  const keys = Object.keys(config).sort((a, b) => a.localeCompare(b));
  const newConfig = {};
  for (const k of keys)
    newConfig[k] = config[k];
  return newConfig;
}
function createSiteConfigStack(options) {
  const debug = options?.debug || false;
  const stack = [];
  function push(input) {
    if (!input || typeof input !== "object" || Object.keys(input).length === 0) {
      return () => {
      };
    }
    if (!input._context && debug) {
      let lastFunctionName = new Error("tmp").stack?.split("\n")[2].split(" ")[5];
      if (lastFunctionName?.includes("/"))
        lastFunctionName = "anonymous";
      input._context = lastFunctionName;
    }
    const entry = {};
    for (const k in input) {
      const val = input[k];
      if (typeof val !== "undefined" && val !== "")
        entry[k] = val;
    }
    let idx;
    if (Object.keys(entry).filter((k) => !k.startsWith("_")).length > 0)
      idx = stack.push(entry);
    return () => {
      if (typeof idx !== "undefined") {
        stack.splice(idx - 1, 1);
      }
    };
  }
  function get(options2) {
    const siteConfig = {};
    if (options2?.debug)
      siteConfig._context = {};
    siteConfig._priority = {};
    for (const o in stack.sort((a, b) => (a._priority || 0) - (b._priority || 0))) {
      for (const k in stack[o]) {
        const key = k;
        const val = options2?.resolveRefs ? toValue(stack[o][k]) : stack[o][k];
        if (!k.startsWith("_") && typeof val !== "undefined" && val !== "") {
          siteConfig[k] = val;
          if (typeof stack[o]._priority !== "undefined" && stack[o]._priority !== -1) {
            siteConfig._priority[key] = stack[o]._priority;
          }
          if (options2?.debug)
            siteConfig._context[key] = stack[o]._context?.[key] || stack[o]._context || "anonymous";
        }
      }
    }
    return options2?.skipNormalize ? siteConfig : normalizeSiteConfig(siteConfig);
  }
  return {
    stack,
    push,
    get
  };
}

function envSiteConfig(env) {
  return Object.fromEntries(Object.entries(env).filter(([k]) => k.startsWith("NUXT_SITE_") || k.startsWith("NUXT_PUBLIC_SITE_")).map(([k, v]) => [
    k.replace(/^NUXT_(PUBLIC_)?SITE_/, "").split("_").map((s, i) => i === 0 ? s.toLowerCase() : s[0].toUpperCase() + s.slice(1).toLowerCase()).join(""),
    v
  ]));
}

const logger$1 = /* @__PURE__ */ createConsola({
  defaults: {
    tag: "nuxt-site-config"
  }
});

function useSiteConfig(e, _options) {
  if (!e.context._initedSiteConfig) {
    logger$1.warn("Site config has not been initialized yet. If you're trying to access site config in a server middleware then this not yet supported. See https://github.com/harlan-zw/nuxt-seo/issues/397");
  }
  e.context.siteConfig = e.context.siteConfig || createSiteConfigStack();
  const options = defu$1(_options, useRuntimeConfig(e)["nuxt-site-config"], { debug: false });
  return e.context.siteConfig.get(options);
}

const _7ox7cGMr_6vYkukwYpVvqpPGmv1L2CU84vHyeI1J12w = defineNitroPlugin(async (nitroApp) => {
  nitroApp.hooks.hook("render:html", async (ctx, { event }) => {
    const routeOptions = getRouteRules(event);
    const isIsland = process.env.NUXT_COMPONENT_ISLANDS && event.path.startsWith("/__nuxt_island");
    event.path;
    const noSSR = event.context.nuxt?.noSSR || routeOptions.ssr === false && !isIsland || (false);
    if (noSSR) {
      const siteConfig = Object.fromEntries(
        Object.entries(useSiteConfig(event)).map(([k, v]) => [k, toValue(v)])
      );
      ctx.body.push(`<script>window.__NUXT_SITE_CONFIG__=${devalue(siteConfig)}<\/script>`);
    }
  });
});

const plugins = [
  _ZQMesIEr59jBBjSpaXwuV0xZ2ghuLgy5hYN3VBfogw,
_Utrd98Z9gx4fS91ckpl6CH5q3sbifaLjr88vCqf5le0,
_7ox7cGMr_6vYkukwYpVvqpPGmv1L2CU84vHyeI1J12w
];

function useNitroOrigin(e) {
  const cert = process.env.NITRO_SSL_CERT;
  const key = process.env.NITRO_SSL_KEY;
  let host = process.env.NITRO_HOST || process.env.HOST || false;
  let port = false;
  port = process.env.NITRO_PORT || process.env.PORT || "3000";
  let protocol = cert && key || false ? "https" : "http";
  if (process.env.__NUXT_DEV__) {
    const origin = JSON.parse(process.env.__NUXT_DEV__).proxy.url;
    host = withoutProtocol(origin);
    protocol = origin.includes("https") ? "https" : "http";
  } else if (process.env.NUXT_VITE_NODE_OPTIONS) {
    const origin = JSON.parse(process.env.NUXT_VITE_NODE_OPTIONS).baseURL.replace("/__nuxt_vite_node__", "");
    host = withoutProtocol(origin);
    protocol = origin.includes("https") ? "https" : "http";
  } else if (e) {
    host = getRequestHost(e, { xForwardedHost: true }) || host;
    protocol = getRequestProtocol(e, { xForwardedProto: true }) || protocol;
  }
  if (typeof host === "string" && host.includes(":")) {
    port = host.split(":").pop();
    host = host.split(":")[0];
  }
  port = port ? `:${port}` : "";
  return withTrailingSlash(`${protocol}://${host}${port}`);
}

const _6L9AR9 = eventHandler(async (e) => {
  if (e.context._initedSiteConfig)
    return;
  const runtimeConfig = useRuntimeConfig(e);
  const config = runtimeConfig["nuxt-site-config"];
  const nitroApp = useNitroApp();
  const siteConfig = e.context.siteConfig || createSiteConfigStack({
    debug: config.debug
  });
  const nitroOrigin = useNitroOrigin(e);
  e.context.siteConfigNitroOrigin = nitroOrigin;
  {
    siteConfig.push({
      _context: "nitro:init",
      _priority: -4,
      url: nitroOrigin
    });
  }
  siteConfig.push({
    _context: "runtimeEnv",
    _priority: 0,
    ...runtimeConfig.site || {},
    ...runtimeConfig.public.site || {},
    // @ts-expect-error untyped
    ...envSiteConfig(globalThis._importMeta_.env)
    // just in-case, shouldn't be needed
  });
  const buildStack = config.stack || [];
  buildStack.forEach((c) => siteConfig.push(c));
  if (e.context._nitro.routeRules.site) {
    siteConfig.push({
      _context: "route-rules",
      ...e.context._nitro.routeRules.site
    });
  }
  if (config.multiTenancy) {
    const host = parseURL(nitroOrigin).host;
    const tenant = config.multiTenancy?.find((t) => t.hosts.includes(host));
    if (tenant) {
      siteConfig.push({
        _context: `multi-tenancy:${host}`,
        _priority: 0,
        ...tenant.config
      });
    }
  }
  const ctx = { siteConfig, event: e };
  await nitroApp.hooks.callHook("site-config:init", ctx);
  e.context.siteConfig = ctx.siteConfig;
  e.context._initedSiteConfig = true;
});

const _SdWPpK = eventHandler(async (e) => {
  const siteConfig = useSiteConfig(e);
  const nitroOrigin = useNitroOrigin(e);
  const runtimeConfig = useRuntimeConfig(e);
  const stack = e.context.siteConfig.stack;
  setHeader(e, "Content-Type", "application/json");
  return {
    config: siteConfig,
    stack,
    nitroOrigin,
    version: runtimeConfig["nuxt-site-config"].version
  };
});

const checksums = {
  "posts": "v3.5.0--RpHEGLtRaJdfpJrKS1630rTy2A_pE_x9X9iI1Kz5TiA"
};
const checksumsStructure = {
  "posts": "m_HFdI6rclOqLTEt_JyfR58Z2T2DWG0iIsUq9cQaddk"
};
const tables = {
  "posts": "_content_posts",
  "info": "_content_info"
};
const contentManifest = {
  "posts": {
    "type": "page",
    "fields": {
      "id": "string",
      "title": "string",
      "authors": "json",
      "body": "json",
      "date": "date",
      "description": "string",
      "extension": "string",
      "image": "json",
      "meta": "json",
      "navigation": "json",
      "path": "string",
      "popular": "boolean",
      "seo": "json",
      "sitemap": "json",
      "stem": "string",
      "tags": "json"
    }
  },
  "info": {
    "type": "data",
    "fields": {}
  }
};

const buildGroup = (group, type) => {
  const conditions = group._conditions;
  return conditions.length > 0 ? `(${conditions.join(` ${type} `)})` : "";
};
const collectionQueryGroup = (collection) => {
  const conditions = [];
  const query = {
    // @ts-expect-error -- internal
    _conditions: conditions,
    where(field, operator, value) {
      let condition;
      switch (operator.toUpperCase()) {
        case "IN":
        case "NOT IN":
          if (Array.isArray(value)) {
            const values = value.map((val) => singleQuote(val)).join(", ");
            condition = `"${String(field)}" ${operator.toUpperCase()} (${values})`;
          } else {
            throw new TypeError(`Value for ${operator} must be an array`);
          }
          break;
        case "BETWEEN":
        case "NOT BETWEEN":
          if (Array.isArray(value) && value.length === 2) {
            condition = `"${String(field)}" ${operator.toUpperCase()} ${singleQuote(value[0])} AND ${singleQuote(value[1])}`;
          } else {
            throw new Error(`Value for ${operator} must be an array with two elements`);
          }
          break;
        case "IS NULL":
        case "IS NOT NULL":
          condition = `"${String(field)}" ${operator.toUpperCase()}`;
          break;
        case "LIKE":
        case "NOT LIKE":
          condition = `"${String(field)}" ${operator.toUpperCase()} ${singleQuote(value)}`;
          break;
        default:
          condition = `"${String(field)}" ${operator} ${singleQuote(typeof value === "boolean" ? Number(value) : value)}`;
      }
      conditions.push(`${condition}`);
      return query;
    },
    andWhere(groupFactory) {
      const group = groupFactory(collectionQueryGroup());
      conditions.push(buildGroup(group, "AND"));
      return query;
    },
    orWhere(groupFactory) {
      const group = groupFactory(collectionQueryGroup());
      conditions.push(buildGroup(group, "OR"));
      return query;
    }
  };
  return query;
};
const collectionQueryBuilder = (collection, fetch) => {
  const params = {
    conditions: [],
    selectedFields: [],
    offset: 0,
    limit: 0,
    orderBy: [],
    // Count query
    count: {
      field: "",
      distinct: false
    }
  };
  const query = {
    // @ts-expect-error -- internal
    __params: params,
    andWhere(groupFactory) {
      const group = groupFactory(collectionQueryGroup());
      params.conditions.push(buildGroup(group, "AND"));
      return query;
    },
    orWhere(groupFactory) {
      const group = groupFactory(collectionQueryGroup());
      params.conditions.push(buildGroup(group, "OR"));
      return query;
    },
    path(path) {
      return query.where("path", "=", withoutTrailingSlash(path));
    },
    skip(skip) {
      params.offset = skip;
      return query;
    },
    where(field, operator, value) {
      query.andWhere((group) => group.where(String(field), operator, value));
      return query;
    },
    limit(limit) {
      params.limit = limit;
      return query;
    },
    select(...fields) {
      if (fields.length) {
        params.selectedFields.push(...fields);
      }
      return query;
    },
    order(field, direction) {
      params.orderBy.push(`"${String(field)}" ${direction}`);
      return query;
    },
    async all() {
      return fetch(collection, buildQuery()).then((res) => res || []);
    },
    async first() {
      return fetch(collection, buildQuery({ limit: 1 })).then((res) => res[0] || null);
    },
    async count(field = "*", distinct = false) {
      return fetch(collection, buildQuery({
        count: { field: String(field), distinct }
      })).then((m) => m[0].count);
    }
  };
  function buildQuery(opts = {}) {
    let query2 = "SELECT ";
    if (opts?.count) {
      query2 += `COUNT(${opts.count.distinct ? "DISTINCT " : ""}${opts.count.field}) as count`;
    } else {
      const fields = Array.from(new Set(params.selectedFields));
      query2 += fields.length > 0 ? fields.map((f) => `"${String(f)}"`).join(", ") : "*";
    }
    query2 += ` FROM ${tables[String(collection)]}`;
    if (params.conditions.length > 0) {
      query2 += ` WHERE ${params.conditions.join(" AND ")}`;
    }
    if (params.orderBy.length > 0) {
      query2 += ` ORDER BY ${params.orderBy.join(", ")}`;
    } else {
      query2 += ` ORDER BY stem ASC`;
    }
    const limit = opts?.limit || params.limit;
    if (limit > 0) {
      if (params.offset > 0) {
        query2 += ` LIMIT ${limit} OFFSET ${params.offset}`;
      } else {
        query2 += ` LIMIT ${limit}`;
      }
    }
    return query2;
  }
  return query;
};
function singleQuote(value) {
  return `'${String(value).replace(/'/g, "''")}'`;
}

async function fetchDatabase(event, collection) {
  return await $fetch(`/__nuxt_content/${collection}/sql_dump.txt`, {
    context: event ? { cloudflare: event.context.cloudflare } : {},
    responseType: "text",
    headers: {
      "content-type": "text/plain",
      ...event?.node?.req?.headers?.cookie ? { cookie: event.node.req.headers.cookie } : {}
    },
    query: { v: checksums[String(collection)], t: Date.now()  }
  });
}
async function fetchQuery(event, collection, sql) {
  return await $fetch(`/__nuxt_content/${collection}/query`, {
    context: event ? { cloudflare: event.context.cloudflare } : {},
    headers: {
      "content-type": "application/json",
      ...event?.node?.req?.headers?.cookie ? { cookie: event.node.req.headers.cookie } : {}
    },
    query: { v: checksums[String(collection)], t: Date.now()  },
    method: "POST",
    body: {
      sql
    }
  });
}

const queryCollection = (event, collection) => {
  return collectionQueryBuilder(collection, (collection2, sql) => fetchQuery(event, collection2, sql));
};

const _heihII = defineEventHandler(async (e) => {
  const collections = [];
  for (const collection in contentManifest) {
    if (contentManifest[collection].fields.sitemap) {
      collections.push(collection);
    }
  }
  const contentList = [];
  for (const collection of collections) {
    contentList.push(
      queryCollection(e, collection).select("path", "sitemap").where("path", "IS NOT NULL").where("sitemap", "IS NOT NULL").all()
    );
  }
  const results = await Promise.all(contentList);
  return results.flatMap((c) => {
    return c.filter((c2) => c2.sitemap !== false && c2.path).flatMap((c2) => ({
      loc: c2.path,
      ...c2.sitemap || {}
    }));
  }).filter(Boolean);
});

const logger = createConsola({
  defaults: {
    tag: "@nuxt/sitemap"
  }
});
const merger = createDefu((obj, key, value) => {
  if (Array.isArray(obj[key]) && Array.isArray(value))
    obj[key] = Array.from(/* @__PURE__ */ new Set([...obj[key], ...value]));
  return obj[key];
});
function mergeOnKey(arr, key) {
  const seen = /* @__PURE__ */ new Map();
  let resultLength = 0;
  const result = Array.from({ length: arr.length });
  for (const item of arr) {
    const k = item[key];
    if (seen.has(k)) {
      const existingIndex = seen.get(k);
      result[existingIndex] = merger(item, result[existingIndex]);
    } else {
      seen.set(k, resultLength);
      result[resultLength++] = item;
    }
  }
  return result.slice(0, resultLength);
}
function splitForLocales(path, locales) {
  const prefix = withLeadingSlash(path).split("/")[1];
  if (locales.includes(prefix))
    return [prefix, path.replace(`/${prefix}`, "")];
  return [null, path];
}
const StringifiedRegExpPattern = /\/(.*?)\/([gimsuy]*)$/;
function normalizeRuntimeFilters(input) {
  return (input || []).map((rule) => {
    if (rule instanceof RegExp || typeof rule === "string")
      return rule;
    const match = rule.regex.match(StringifiedRegExpPattern);
    if (match)
      return new RegExp(match[1], match[2]);
    return false;
  }).filter(Boolean);
}
function createPathFilter(options = {}) {
  const urlFilter = createFilter(options);
  return (loc) => {
    let path = loc;
    try {
      path = parseURL(loc).pathname;
    } catch {
      return false;
    }
    return urlFilter(path);
  };
}
function createFilter(options = {}) {
  const include = options.include || [];
  const exclude = options.exclude || [];
  if (include.length === 0 && exclude.length === 0)
    return () => true;
  return function(path) {
    for (const v of [{ rules: exclude, result: false }, { rules: include, result: true }]) {
      const regexRules = v.rules.filter((r) => r instanceof RegExp);
      if (regexRules.some((r) => r.test(path)))
        return v.result;
      const stringRules = v.rules.filter((r) => typeof r === "string");
      if (stringRules.length > 0) {
        const routes = {};
        for (const r of stringRules) {
          if (r === path)
            return v.result;
          routes[r] = true;
        }
        const routeRulesMatcher = toRouteMatcher(createRouter({ routes, strictTrailingSlash: false }));
        if (routeRulesMatcher.matchAll(path).length > 0)
          return Boolean(v.result);
      }
    }
    return include.length === 0;
  };
}

function xmlEscape(str) {
  return str.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&apos;");
}
function useSitemapRuntimeConfig(e) {
  const clone = JSON.parse(JSON.stringify(useRuntimeConfig(e).sitemap));
  for (const k in clone.sitemaps) {
    const sitemap = clone.sitemaps[k];
    sitemap.include = normalizeRuntimeFilters(sitemap.include);
    sitemap.exclude = normalizeRuntimeFilters(sitemap.exclude);
    clone.sitemaps[k] = sitemap;
  }
  return Object.freeze(clone);
}

async function tryFetchWithFallback(url, options, event) {
  const isExternalUrl = !url.startsWith("/");
  if (isExternalUrl) {
    const strategies = [
      // Strategy 1: Use globalThis.$fetch (original approach)
      () => globalThis.$fetch(url, options),
      // Strategy 2: If event is available, try using event context even for external URLs
      event ? () => event.$fetch(url, options) : null,
      // Strategy 3: Use native fetch as last resort
      () => $fetch(url, options)
    ].filter(Boolean);
    let lastError = null;
    for (const strategy of strategies) {
      try {
        return await strategy();
      } catch (error) {
        lastError = error;
        continue;
      }
    }
    throw lastError;
  }
  const fetchContainer = url.startsWith("/") && event ? event : globalThis;
  return await fetchContainer.$fetch(url, options);
}
async function fetchDataSource(input, event) {
  const context = typeof input.context === "string" ? { name: input.context } : input.context || { name: "fetch" };
  const url = typeof input.fetch === "string" ? input.fetch : input.fetch[0];
  const options = typeof input.fetch === "string" ? {} : input.fetch[1];
  const start = Date.now();
  const isExternalUrl = !url.startsWith("/");
  const timeout = isExternalUrl ? 1e4 : options.timeout || 5e3;
  const timeoutController = new AbortController();
  const abortRequestTimeout = setTimeout(() => timeoutController.abort(), timeout);
  try {
    let isMaybeErrorResponse = false;
    const isXmlRequest = parseURL(url).pathname.endsWith(".xml");
    const mergedHeaders = defu$1(
      options?.headers,
      {
        Accept: isXmlRequest ? "text/xml" : "application/json"
      },
      event ? { host: getRequestHost(event, { xForwardedHost: true }) } : {}
    );
    const fetchOptions = {
      ...options,
      responseType: isXmlRequest ? "text" : "json",
      signal: timeoutController.signal,
      headers: mergedHeaders,
      // Use ofetch's built-in retry for external sources
      ...isExternalUrl && {
        retry: 2,
        retryDelay: 200
      },
      // @ts-expect-error untyped
      onResponse({ response }) {
        if (typeof response._data === "string" && response._data.startsWith("<!DOCTYPE html>"))
          isMaybeErrorResponse = true;
      }
    };
    const res = await tryFetchWithFallback(url, fetchOptions, event);
    const timeTakenMs = Date.now() - start;
    if (isMaybeErrorResponse) {
      return {
        ...input,
        context,
        urls: [],
        timeTakenMs,
        error: "Received HTML response instead of JSON"
      };
    }
    let urls = [];
    if (typeof res === "object") {
      urls = res.urls || res;
    } else if (typeof res === "string" && parseURL(url).pathname.endsWith(".xml")) {
      const { parseSitemapXml } = await Promise.resolve().then(function () { return utils; });
      const result = parseSitemapXml(res);
      urls = result.urls;
    }
    return {
      ...input,
      context,
      timeTakenMs,
      urls
    };
  } catch (_err) {
    const error = _err;
    if (isExternalUrl) {
      const errorInfo = {
        url,
        timeout,
        error: error.message,
        statusCode: error.response?.status,
        statusText: error.response?.statusText,
        method: options?.method || "GET"
      };
      logger.error("Failed to fetch external source.", errorInfo);
    } else {
      logger.error("Failed to fetch source.", { url, error: error.message });
    }
    return {
      ...input,
      context,
      urls: [],
      error: error.message,
      _isFailure: true
      // Mark as failure to prevent caching
    };
  } finally {
    if (abortRequestTimeout) {
      clearTimeout(abortRequestTimeout);
    }
  }
}
function globalSitemapSources() {
  return Promise.resolve().then(function () { return globalSources; }).then((m) => m.sources);
}
function childSitemapSources(definition) {
  return definition?._hasSourceChunk ? Promise.resolve().then(function () { return childSources; }).then((m) => m.sources[definition.sitemapName] || []) : Promise.resolve([]);
}
async function resolveSitemapSources(sources, event) {
  return (await Promise.all(
    sources.map((source) => {
      if (typeof source === "object" && "urls" in source) {
        return {
          timeTakenMs: 0,
          ...source,
          urls: source.urls
        };
      }
      if (source.fetch)
        return fetchDataSource(source, event);
      return {
        ...source,
        error: "Invalid source"
      };
    })
  )).flat();
}

const _ME1LU5 = defineEventHandler(async (e) => {
  const _runtimeConfig = useSitemapRuntimeConfig();
  const { sitemaps: _sitemaps } = _runtimeConfig;
  const runtimeConfig = { ..._runtimeConfig };
  delete runtimeConfig.sitemaps;
  const globalSources = await globalSitemapSources();
  const nitroOrigin = useNitroOrigin(e);
  const sitemaps = {};
  for (const s of Object.keys(_sitemaps)) {
    sitemaps[s] = {
      ..._sitemaps[s],
      sources: await resolveSitemapSources(await childSitemapSources(_sitemaps[s]), e)
    };
  }
  return {
    nitroOrigin,
    sitemaps,
    runtimeConfig,
    globalSources: await resolveSitemapSources(globalSources, e)
  };
});

function resolveSitePath(pathOrUrl, options) {
  let path = pathOrUrl;
  if (hasProtocol(pathOrUrl, { strict: false, acceptRelative: true })) {
    const parsed = parseURL(pathOrUrl);
    path = parsed.pathname;
  }
  const base = withLeadingSlash(options.base || "/");
  if (base !== "/" && path.startsWith(base)) {
    path = path.slice(base.length);
  }
  let origin = withoutTrailingSlash(options.absolute ? options.siteUrl : "");
  if (base !== "/" && origin.endsWith(base)) {
    origin = origin.slice(0, origin.indexOf(base));
  }
  const baseWithOrigin = options.withBase ? withBase(base, origin || "/") : origin;
  const resolvedUrl = withBase(path, baseWithOrigin);
  return path === "/" && !options.withBase ? withTrailingSlash(resolvedUrl) : fixSlashes(options.trailingSlash, resolvedUrl);
}
const fileExtensions = [
  // Images
  "jpg",
  "jpeg",
  "png",
  "gif",
  "bmp",
  "webp",
  "svg",
  "ico",
  // Documents
  "pdf",
  "doc",
  "docx",
  "xls",
  "xlsx",
  "ppt",
  "pptx",
  "txt",
  "md",
  "markdown",
  // Archives
  "zip",
  "rar",
  "7z",
  "tar",
  "gz",
  // Audio
  "mp3",
  "wav",
  "flac",
  "ogg",
  "opus",
  "m4a",
  "aac",
  "midi",
  "mid",
  // Video
  "mp4",
  "avi",
  "mkv",
  "mov",
  "wmv",
  "flv",
  "webm",
  // Web
  "html",
  "css",
  "js",
  "json",
  "xml",
  "tsx",
  "jsx",
  "ts",
  "vue",
  "svelte",
  "xsl",
  "rss",
  "atom",
  // Programming
  "php",
  "py",
  "rb",
  "java",
  "c",
  "cpp",
  "h",
  "go",
  // Data formats
  "csv",
  "tsv",
  "sql",
  "yaml",
  "yml",
  // Fonts
  "woff",
  "woff2",
  "ttf",
  "otf",
  "eot",
  // Executables/Binaries
  "exe",
  "msi",
  "apk",
  "ipa",
  "dmg",
  "iso",
  "bin",
  // Scripts/Config
  "bat",
  "cmd",
  "sh",
  "env",
  "htaccess",
  "conf",
  "toml",
  "ini",
  // Package formats
  "deb",
  "rpm",
  "jar",
  "war",
  // E-books
  "epub",
  "mobi",
  // Common temporary/backup files
  "log",
  "tmp",
  "bak",
  "old",
  "sav"
];
function isPathFile(path) {
  const lastSegment = path.split("/").pop();
  const ext = (lastSegment || path).match(/\.[0-9a-z]+$/i)?.[0];
  return ext && fileExtensions.includes(ext.replace(".", ""));
}
function fixSlashes(trailingSlash, pathOrUrl) {
  const $url = parseURL(pathOrUrl);
  if (isPathFile($url.pathname))
    return pathOrUrl;
  const fixedPath = trailingSlash ? withTrailingSlash($url.pathname) : withoutTrailingSlash($url.pathname);
  return `${$url.protocol ? `${$url.protocol}//` : ""}${$url.host || ""}${fixedPath}${$url.search || ""}${$url.hash || ""}`;
}

function createSitePathResolver(e, options = {}) {
  const siteConfig = useSiteConfig(e);
  const nitroOrigin = useNitroOrigin(e);
  const nuxtBase = useRuntimeConfig(e).app.baseURL || "/";
  return (path) => {
    return resolveSitePath(path, {
      ...options,
      siteUrl: options.canonical !== false || false ? siteConfig.url : nitroOrigin,
      trailingSlash: siteConfig.trailingSlash,
      base: nuxtBase
    });
  };
}

const _pMKyjm = defineEventHandler(async (e) => {
  const fixPath = createSitePathResolver(e, { absolute: false, withBase: true });
  const { sitemapName: fallbackSitemapName, cacheMaxAgeSeconds, version, xslColumns, xslTips } = useSitemapRuntimeConfig();
  setHeader(e, "Content-Type", "application/xslt+xml");
  if (cacheMaxAgeSeconds)
    setHeader(e, "Cache-Control", `public, max-age=${cacheMaxAgeSeconds}, must-revalidate`);
  else
    setHeader(e, "Cache-Control", `no-cache, no-store`);
  const svgIcon = `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="icon" style="margin-right: 4px; font-size: 25px;" width="1em" height="1em" viewBox="0 0 32 32"><path fill="#93c5fd" d="M4 26h4v4H4zm10 0h4v4h-4zm10 0h4v4h-4zm1-10h-8v-2h-2v2H7a2.002 2.002 0 0 0-2 2v6h2v-6h8v6h2v-6h8v6h2v-6a2.002 2.002 0 0 0-2-2zM9 2v10h14V2zm2 2h2v6h-2zm10 6h-6V4h6z"></path></svg>`;
  const creditName = `<a href="https://github.com/nuxt-modules/sitemap" style="color: black; display: flex; align-items: center; font-weight: 500;" target="_blank" rel="noopener">${svgIcon} Nuxt Sitemap v${version}</a>`;
  const { name: siteName, url: siteUrl } = useSiteConfig(e);
  const referrer = getHeader(e, "Referer") || "/";
  const referrerPath = parseURL(referrer).pathname;
  const isNotIndexButHasIndex = referrerPath !== "/sitemap.xml" && referrerPath !== "/sitemap_index.xml" && referrerPath.endsWith(".xml");
  const sitemapName = parseURL(referrer).pathname.split("/").pop()?.split("-sitemap")[0] || fallbackSitemapName;
  const title = `${siteName}${sitemapName !== "sitemap.xml" ? ` - ${sitemapName === "sitemap_index.xml" ? "index" : sitemapName}` : ""}`.replace(/&/g, "&amp;");
  const canonicalQuery = getQuery(referrer).canonical;
  const isShowingCanonical = typeof canonicalQuery !== "undefined" && canonicalQuery !== "false";
  const conditionalTips = [
    'You are looking at a <a href="https://developer.mozilla.org/en-US/docs/Web/XSLT/Transforming_XML_with_XSLT/An_Overview" style="color: #398465" target="_blank">XML stylesheet</a>. Read the <a href="https://nuxtseo.com/sitemap/guides/customising-ui" style="color: #398465" target="_blank">docs</a> to learn how to customize it. View the page source to see the raw XML.',
    `URLs missing? Check Nuxt Devtools Sitemap tab (or the <a href="${xmlEscape(withQuery("/__sitemap__/debug.json", { sitemap: sitemapName }))}" style="color: #398465" target="_blank">debug endpoint</a>).`
  ];
  const fetchErrors = [];
  const xslQuery = getQuery$1(e);
  if (xslQuery.error_messages) {
    const errorMessages = xslQuery.error_messages;
    const errorUrls = xslQuery.error_urls;
    if (errorMessages) {
      const messages = Array.isArray(errorMessages) ? errorMessages : [errorMessages];
      const urls = Array.isArray(errorUrls) ? errorUrls : errorUrls ? [errorUrls] : [];
      messages.forEach((msg, i) => {
        const errorParts = [xmlEscape(msg)];
        if (urls[i]) {
          errorParts.push(xmlEscape(urls[i]));
        }
        fetchErrors.push(`<strong style="color: #dc2626;">Error ${i + 1}:</strong> ${errorParts.join(" - ")}`);
      });
    }
  }
  if (!isShowingCanonical) {
    const canonicalPreviewUrl = withQuery(referrer, { canonical: "" });
    conditionalTips.push(`Your canonical site URL is <strong>${xmlEscape(siteUrl)}</strong>.`);
    conditionalTips.push(`You can preview your canonical sitemap by visiting <a href="${xmlEscape(canonicalPreviewUrl)}" style="color: #398465; white-space: nowrap;">${xmlEscape(fixPath(canonicalPreviewUrl))}?canonical</a>`);
  } else {
    conditionalTips.push(`You are viewing the canonical sitemap. You can switch to using the request origin: <a href="${xmlEscape(fixPath(referrer))}" style="color: #398465; white-space: nowrap ">${xmlEscape(fixPath(referrer))}</a>`);
  }
  const hasRuntimeErrors = fetchErrors.length > 0;
  const showDevTips = xslTips !== false;
  const showSidebar = showDevTips || hasRuntimeErrors;
  const devTips = showDevTips ? conditionalTips.map((t) => `<li><p>${t}</p></li>`).join("\n") : "";
  const runtimeErrors = hasRuntimeErrors ? fetchErrors.map((t) => `<li><p>${t}</p></li>`).join("\n") : "";
  let columns = [...xslColumns];
  if (!columns.length) {
    columns = [
      { label: "URL", width: "50%" },
      { label: "Images", width: "25%", select: "count(image:image)" },
      { label: "Last Updated", width: "25%", select: "concat(substring(sitemap:lastmod,0,11),concat(' ', substring(sitemap:lastmod,12,5)),concat(' ', substring(sitemap:lastmod,20,6)))" }
    ];
  }
  return `<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="2.0"
                xmlns:html="http://www.w3.org/TR/REC-html40"
                xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
                xmlns:sitemap="http://www.sitemaps.org/schemas/sitemap/0.9"
                xmlns:xhtml="http://www.w3.org/1999/xhtml"
                xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
  <xsl:output method="html" version="1.0" encoding="UTF-8" indent="yes"/>
  <xsl:template match="/">
    <html xmlns="http://www.w3.org/1999/xhtml">
      <head>
        <title>XML Sitemap</title>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        <style type="text/css">
          body {
            font-family: Inter, Helvetica, Arial, sans-serif;
            font-size: 14px;
            color: #333;
          }

          table {
            border: none;
            border-collapse: collapse;
          }

          .bg-yellow-200 {
            background-color: #fef9c3;
          }

          .p-5 {
            padding: 1.25rem;
          }

          .rounded {
            border-radius: 4px;
            }

          .shadow {
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
          }

          #sitemap tr:nth-child(odd) td {
            background-color: #f8f8f8 !important;
          }

          #sitemap tbody tr:hover td {
            background-color: #fff;
          }

          #sitemap tbody tr:hover td, #sitemap tbody tr:hover td a {
            color: #000;
          }

          .expl a {
            color: #398465;
            font-weight: 600;
          }

          .expl a:visited {
            color: #398465;
          }

          a {
            color: #000;
            text-decoration: none;
          }

          a:visited {
            color: #777;
          }

          a:hover {
            text-decoration: underline;
          }

          td {
            font-size: 12px;
          }

          .text-2xl {
            font-size: 2rem;
            font-weight: 600;
            line-height: 1.25;
          }

          th {
            text-align: left;
            padding-right: 30px;
            font-size: 12px;
          }

          thead th {
            border-bottom: 1px solid #000;
          }
          .fixed { position: fixed; }
          .right-2 { right: 2rem; }
          .top-2 { top: 2rem; }
          .w-30 { width: 30rem; }
          p { margin: 0; }
          li { padding-bottom: 0.5rem; line-height: 1.5; }
          h1 { margin: 0; }
          .mb-5 { margin-bottom: 1.25rem; }
          .mb-3 { margin-bottom: 0.75rem; }
        </style>
      </head>
      <body>
        <div style="grid-template-columns: 1fr 1fr; display: grid; margin: 3rem;">
            <div>
             <div id="content">
          <h1 class="text-2xl mb-3">XML Sitemap</h1>
          <h2>${xmlEscape(title)}</h2>
          ${isNotIndexButHasIndex ? `<p style="font-size: 12px; margin-bottom: 1rem;"><a href="${xmlEscape(fixPath("/sitemap_index.xml"))}">${xmlEscape(fixPath("/sitemap_index.xml"))}</a></p>` : ""}
          <xsl:if test="count(sitemap:sitemapindex/sitemap:sitemap) &gt; 0">
            <p class="expl" style="margin-bottom: 1rem;">
              This XML Sitemap Index file contains
              <xsl:value-of select="count(sitemap:sitemapindex/sitemap:sitemap)"/> sitemaps.
            </p>
            <table id="sitemap" cellpadding="3">
              <thead>
                <tr>
                  <th width="75%">Sitemap</th>
                  <th width="25%">Last Modified</th>
                </tr>
              </thead>
              <tbody>
                <xsl:for-each select="sitemap:sitemapindex/sitemap:sitemap">
                  <xsl:variable name="sitemapURL">
                    <xsl:value-of select="sitemap:loc"/>
                  </xsl:variable>
                  <tr>
                    <td>
                      <a href="{$sitemapURL}">
                        <xsl:value-of select="sitemap:loc"/>
                      </a>
                    </td>
                    <td>
                      <xsl:value-of
                        select="concat(substring(sitemap:lastmod,0,11),concat(' ', substring(sitemap:lastmod,12,5)),concat(' ', substring(sitemap:lastmod,20,6)))"/>
                    </td>
                  </tr>
                </xsl:for-each>
              </tbody>
            </table>
          </xsl:if>
          <xsl:if test="count(sitemap:sitemapindex/sitemap:sitemap) &lt; 1">
            <p class="expl" style="margin-bottom: 1rem;">
              This XML Sitemap contains
              <xsl:value-of select="count(sitemap:urlset/sitemap:url)"/> URLs.
            </p>
            <table id="sitemap" cellpadding="3">
              <thead>
                <tr>
                  ${columns.map((c) => `<th width="${c.width}">${c.label}</th>`).join("\n")}
                </tr>
              </thead>
              <tbody>
                <xsl:variable name="lower" select="'abcdefghijklmnopqrstuvwxyz'"/>
                <xsl:variable name="upper" select="'ABCDEFGHIJKLMNOPQRSTUVWXYZ'"/>
                <xsl:for-each select="sitemap:urlset/sitemap:url">
                  <tr>
                    <td>
                      <xsl:variable name="itemURL">
                        <xsl:value-of select="sitemap:loc"/>
                      </xsl:variable>
                      <a href="{$itemURL}">
                        <xsl:value-of select="sitemap:loc"/>
                      </a>
                    </td>
                    ${columns.filter((c) => c.label !== "URL").map((c) => `<td>
<xsl:value-of select="${c.select}"/>
</td>`).join("\n")}
                  </tr>
                </xsl:for-each>
              </tbody>
            </table>
          </xsl:if>
        </div>
        </div>
                    ${showSidebar ? `<div class="w-30 top-2 shadow rounded p-5 right-2" style="margin: 0 auto;">
                      ${showDevTips ? `<div><p><strong>Development Tips</strong></p><ul style="margin: 1rem 0; padding: 0;">${devTips}</ul></div>` : ""}
                      ${hasRuntimeErrors ? `<div${showDevTips ? ' style="margin-top: 1.5rem; padding-top: 1rem; border-top: 1px solid #e5e7eb;"' : ""}><p><strong style="color: #dc2626;">Runtime Errors</strong></p><ul style="margin: 1rem 0; padding: 0;">${runtimeErrors}</ul></div>` : ""}
                      ${showDevTips ? `<p style="margin-top: 1rem;">${creditName}</p>` : ""}
                    </div>` : ""}
        </div>
      </body>
    </html>
  </xsl:template>
</xsl:stylesheet>
`;
});

function withoutQuery(path) {
  return path.split("?")[0];
}
function createNitroRouteRuleMatcher() {
  const { nitro, app } = useRuntimeConfig();
  const _routeRulesMatcher = toRouteMatcher(
    createRouter({
      routes: Object.fromEntries(
        Object.entries(nitro?.routeRules || {}).map(([path, rules]) => [path === "/" ? path : withoutTrailingSlash(path), rules])
      )
    })
  );
  return (pathOrUrl) => {
    const path = pathOrUrl[0] === "/" ? pathOrUrl : parseURL(pathOrUrl, app.baseURL).pathname;
    const pathWithoutQuery = withoutQuery(path);
    return defu$1({}, ..._routeRulesMatcher.matchAll(
      // radix3 does not support trailing slashes
      withoutBase(pathWithoutQuery === "/" ? pathWithoutQuery : withoutTrailingSlash(pathWithoutQuery), app.baseURL)
    ).reverse());
  };
}

function resolve(s, resolvers) {
  if (typeof s === "undefined" || !resolvers)
    return s;
  s = typeof s === "string" ? s : s.toString();
  if (hasProtocol(s, { acceptRelative: true, strict: false }))
    return resolvers.fixSlashes(s);
  return resolvers.canonicalUrlResolver(s);
}
function removeTrailingSlash(s) {
  return s.replace(/\/(\?|#|$)/, "$1");
}
function preNormalizeEntry(_e, resolvers) {
  const e = typeof _e === "string" ? { loc: _e } : { ..._e };
  if (e.url && !e.loc) {
    e.loc = e.url;
    delete e.url;
  }
  if (typeof e.loc !== "string") {
    e.loc = "";
  }
  e.loc = removeTrailingSlash(e.loc);
  e._abs = hasProtocol(e.loc, { acceptRelative: false, strict: false });
  try {
    e._path = e._abs ? parseURL(e.loc) : parsePath(e.loc);
  } catch (e2) {
    e2._path = null;
  }
  if (e._path) {
    const query = parseQuery(e._path.search);
    const qs = stringifyQuery(query);
    e._relativeLoc = `${encodePath(e._path?.pathname)}${qs.length ? `?${qs}` : ""}`;
    if (e._path.host) {
      e.loc = stringifyParsedURL(e._path);
    } else {
      e.loc = e._relativeLoc;
    }
  } else if (!isEncoded(e.loc)) {
    e.loc = encodeURI(e.loc);
  }
  if (e.loc === "")
    e.loc = `/`;
  e.loc = resolve(e.loc, resolvers);
  e._key = `${e._sitemap || ""}${withoutTrailingSlash(e.loc)}`;
  return e;
}
function isEncoded(url) {
  try {
    return url !== decodeURIComponent(url);
  } catch {
    return false;
  }
}
function normaliseEntry(_e, defaults, resolvers) {
  const e = defu$1(_e, defaults);
  if (e.lastmod) {
    const date = normaliseDate(e.lastmod);
    if (date)
      e.lastmod = date;
    else
      delete e.lastmod;
  }
  if (!e.lastmod)
    delete e.lastmod;
  e.loc = resolve(e.loc, resolvers);
  if (e.alternatives) {
    const alternatives = e.alternatives.map((a) => ({ ...a }));
    for (let i = 0; i < alternatives.length; i++) {
      const alt = alternatives[i];
      if (typeof alt.href === "string") {
        alt.href = resolve(alt.href, resolvers);
      } else if (typeof alt.href === "object" && alt.href) {
        alt.href = resolve(alt.href.href, resolvers);
      }
    }
    e.alternatives = mergeOnKey(alternatives, "hreflang");
  }
  if (e.images) {
    const images = e.images.map((i) => ({ ...i }));
    for (let i = 0; i < images.length; i++) {
      images[i].loc = resolve(images[i].loc, resolvers);
    }
    e.images = mergeOnKey(images, "loc");
  }
  if (e.videos) {
    const videos = e.videos.map((v) => ({ ...v }));
    for (let i = 0; i < videos.length; i++) {
      if (videos[i].content_loc) {
        videos[i].content_loc = resolve(videos[i].content_loc, resolvers);
      }
    }
    e.videos = mergeOnKey(videos, "content_loc");
  }
  return e;
}
const IS_VALID_W3C_DATE = [
  /(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))/,
  /^\d{4}-[01]\d-[0-3]\d$/,
  /^\d{4}-[01]\d$/,
  /^\d{4}$/
];
function isValidW3CDate(d) {
  return IS_VALID_W3C_DATE.some((r) => r.test(d));
}
function normaliseDate(d) {
  if (typeof d === "string") {
    if (d.includes("T")) {
      const t = d.split("T")[1];
      if (!t.includes("+") && !t.includes("-") && !t.includes("Z")) {
        d += "Z";
      }
    }
    if (!isValidW3CDate(d))
      return false;
    d = new Date(d);
    d.setMilliseconds(0);
    if (Number.isNaN(d.getTime()))
      return false;
  }
  const z = (n) => `0${n}`.slice(-2);
  const date = `${d.getUTCFullYear()}-${z(d.getUTCMonth() + 1)}-${z(d.getUTCDate())}`;
  if (d.getUTCHours() > 0 || d.getUTCMinutes() > 0 || d.getUTCSeconds() > 0) {
    return `${date}T${z(d.getUTCHours())}:${z(d.getUTCMinutes())}:${z(d.getUTCSeconds())}Z`;
  }
  return date;
}

function sortInPlace(urls) {
  urls.sort((a, b) => {
    const aLoc = typeof a === "string" ? a : a.loc;
    const bLoc = typeof b === "string" ? b : b.loc;
    const aSegments = aLoc.split("/").length;
    const bSegments = bLoc.split("/").length;
    if (aSegments !== bSegments) {
      return aSegments - bSegments;
    }
    return aLoc.localeCompare(bLoc, void 0, { numeric: true });
  });
  return urls;
}

function parseChunkInfo(sitemapName, sitemaps, defaultChunkSize = 1e3) {
  if (typeof sitemaps.chunks !== "undefined" && !Number.isNaN(Number(sitemapName))) {
    return {
      isChunked: true,
      baseSitemapName: "sitemap",
      chunkIndex: Number(sitemapName),
      chunkSize: defaultChunkSize
    };
  }
  if (sitemapName.includes("-")) {
    const parts = sitemapName.split("-");
    const lastPart = parts.pop();
    if (!Number.isNaN(Number(lastPart))) {
      const baseSitemapName = parts.join("-");
      const baseSitemap = sitemaps[baseSitemapName];
      if (baseSitemap && (baseSitemap.chunks || baseSitemap._isChunking)) {
        const chunkSize = typeof baseSitemap.chunks === "number" ? baseSitemap.chunks : baseSitemap.chunkSize || defaultChunkSize;
        return {
          isChunked: true,
          baseSitemapName,
          chunkIndex: Number(lastPart),
          chunkSize
        };
      }
    }
  }
  return {
    isChunked: false,
    baseSitemapName: sitemapName,
    chunkIndex: void 0,
    chunkSize: defaultChunkSize
  };
}
function sliceUrlsForChunk(urls, sitemapName, sitemaps, defaultChunkSize = 1e3) {
  const chunkInfo = parseChunkInfo(sitemapName, sitemaps, defaultChunkSize);
  if (chunkInfo.isChunked && chunkInfo.chunkIndex !== void 0) {
    const startIndex = chunkInfo.chunkIndex * chunkInfo.chunkSize;
    const endIndex = (chunkInfo.chunkIndex + 1) * chunkInfo.chunkSize;
    return urls.slice(startIndex, endIndex);
  }
  return urls;
}

function escapeValueForXml(value) {
  if (value === true || value === false)
    return value ? "yes" : "no";
  return xmlEscape(String(value));
}
const URLSET_OPENING_TAG = '<urlset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:video="http://www.google.com/schemas/sitemap-video/1.1" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1" xmlns:news="http://www.google.com/schemas/sitemap-news/0.9" xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd http://www.google.com/schemas/sitemap-image/1.1 http://www.google.com/schemas/sitemap-image/1.1/sitemap-image.xsd" xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
function buildUrlXml(url) {
  const capacity = 50;
  const parts = Array.from({ length: capacity });
  let partIndex = 0;
  parts[partIndex++] = "    <url>";
  if (url.loc) {
    parts[partIndex++] = `        <loc>${escapeValueForXml(url.loc)}</loc>`;
  }
  if (url.lastmod) {
    parts[partIndex++] = `        <lastmod>${url.lastmod}</lastmod>`;
  }
  if (url.changefreq) {
    parts[partIndex++] = `        <changefreq>${url.changefreq}</changefreq>`;
  }
  if (url.priority !== void 0) {
    const priorityValue = Number.parseFloat(String(url.priority));
    const formattedPriority = priorityValue % 1 === 0 ? String(priorityValue) : priorityValue.toFixed(1);
    parts[partIndex++] = `        <priority>${formattedPriority}</priority>`;
  }
  const keys = Object.keys(url).filter((k) => !k.startsWith("_") && !["loc", "lastmod", "changefreq", "priority"].includes(k));
  for (const key of keys) {
    const value = url[key];
    if (value === void 0 || value === null) continue;
    switch (key) {
      case "alternatives":
        if (Array.isArray(value) && value.length > 0) {
          for (const alt of value) {
            const attrs = Object.entries(alt).map(([k, v]) => `${k}="${escapeValueForXml(v)}"`).join(" ");
            parts[partIndex++] = `        <xhtml:link rel="alternate" ${attrs} />`;
          }
        }
        break;
      case "images":
        if (Array.isArray(value) && value.length > 0) {
          for (const img of value) {
            parts[partIndex++] = "        <image:image>";
            parts[partIndex++] = `            <image:loc>${escapeValueForXml(img.loc)}</image:loc>`;
            if (img.title) parts[partIndex++] = `            <image:title>${escapeValueForXml(img.title)}</image:title>`;
            if (img.caption) parts[partIndex++] = `            <image:caption>${escapeValueForXml(img.caption)}</image:caption>`;
            if (img.geo_location) parts[partIndex++] = `            <image:geo_location>${escapeValueForXml(img.geo_location)}</image:geo_location>`;
            if (img.license) parts[partIndex++] = `            <image:license>${escapeValueForXml(img.license)}</image:license>`;
            parts[partIndex++] = "        </image:image>";
          }
        }
        break;
      case "videos":
        if (Array.isArray(value) && value.length > 0) {
          for (const video of value) {
            parts[partIndex++] = "        <video:video>";
            parts[partIndex++] = `            <video:title>${escapeValueForXml(video.title)}</video:title>`;
            if (video.thumbnail_loc) {
              parts[partIndex++] = `            <video:thumbnail_loc>${escapeValueForXml(video.thumbnail_loc)}</video:thumbnail_loc>`;
            }
            parts[partIndex++] = `            <video:description>${escapeValueForXml(video.description)}</video:description>`;
            if (video.content_loc) {
              parts[partIndex++] = `            <video:content_loc>${escapeValueForXml(video.content_loc)}</video:content_loc>`;
            }
            if (video.player_loc) {
              const attrs = video.player_loc.allow_embed ? ' allow_embed="yes"' : "";
              const autoplay = video.player_loc.autoplay ? ' autoplay="yes"' : "";
              parts[partIndex++] = `            <video:player_loc${attrs}${autoplay}>${escapeValueForXml(video.player_loc)}</video:player_loc>`;
            }
            if (video.duration !== void 0) {
              parts[partIndex++] = `            <video:duration>${video.duration}</video:duration>`;
            }
            if (video.expiration_date) {
              parts[partIndex++] = `            <video:expiration_date>${video.expiration_date}</video:expiration_date>`;
            }
            if (video.rating !== void 0) {
              parts[partIndex++] = `            <video:rating>${video.rating}</video:rating>`;
            }
            if (video.view_count !== void 0) {
              parts[partIndex++] = `            <video:view_count>${video.view_count}</video:view_count>`;
            }
            if (video.publication_date) {
              parts[partIndex++] = `            <video:publication_date>${video.publication_date}</video:publication_date>`;
            }
            if (video.family_friendly !== void 0) {
              parts[partIndex++] = `            <video:family_friendly>${video.family_friendly === "yes" || video.family_friendly === true ? "yes" : "no"}</video:family_friendly>`;
            }
            if (video.restriction) {
              const relationship = video.restriction.relationship || "allow";
              parts[partIndex++] = `            <video:restriction relationship="${relationship}">${escapeValueForXml(video.restriction.restriction)}</video:restriction>`;
            }
            if (video.platform) {
              const relationship = video.platform.relationship || "allow";
              parts[partIndex++] = `            <video:platform relationship="${relationship}">${escapeValueForXml(video.platform.platform)}</video:platform>`;
            }
            if (video.requires_subscription !== void 0) {
              parts[partIndex++] = `            <video:requires_subscription>${video.requires_subscription === "yes" || video.requires_subscription === true ? "yes" : "no"}</video:requires_subscription>`;
            }
            if (video.price) {
              const prices = Array.isArray(video.price) ? video.price : [video.price];
              for (const price of prices) {
                const attrs = [];
                if (price.currency) attrs.push(`currency="${price.currency}"`);
                if (price.type) attrs.push(`type="${price.type}"`);
                const attrsStr = attrs.length > 0 ? " " + attrs.join(" ") : "";
                parts[partIndex++] = `            <video:price${attrsStr}>${escapeValueForXml(price.price)}</video:price>`;
              }
            }
            if (video.uploader) {
              const info = video.uploader.info ? ` info="${escapeValueForXml(video.uploader.info)}"` : "";
              parts[partIndex++] = `            <video:uploader${info}>${escapeValueForXml(video.uploader.uploader)}</video:uploader>`;
            }
            if (video.live !== void 0) {
              parts[partIndex++] = `            <video:live>${video.live === "yes" || video.live === true ? "yes" : "no"}</video:live>`;
            }
            if (video.tag) {
              const tags = Array.isArray(video.tag) ? video.tag : [video.tag];
              for (const tag of tags) {
                parts[partIndex++] = `            <video:tag>${escapeValueForXml(tag)}</video:tag>`;
              }
            }
            if (video.category) {
              parts[partIndex++] = `            <video:category>${escapeValueForXml(video.category)}</video:category>`;
            }
            if (video.gallery_loc) {
              const title = video.gallery_loc.title ? ` title="${escapeValueForXml(video.gallery_loc.title)}"` : "";
              parts[partIndex++] = `            <video:gallery_loc${title}>${escapeValueForXml(video.gallery_loc)}</video:gallery_loc>`;
            }
            parts[partIndex++] = "        </video:video>";
          }
        }
        break;
      case "news":
        if (value) {
          parts[partIndex++] = "        <news:news>";
          parts[partIndex++] = "            <news:publication>";
          parts[partIndex++] = `                <news:name>${escapeValueForXml(value.publication.name)}</news:name>`;
          parts[partIndex++] = `                <news:language>${escapeValueForXml(value.publication.language)}</news:language>`;
          parts[partIndex++] = "            </news:publication>";
          if (value.title) {
            parts[partIndex++] = `            <news:title>${escapeValueForXml(value.title)}</news:title>`;
          }
          if (value.publication_date) {
            parts[partIndex++] = `            <news:publication_date>${value.publication_date}</news:publication_date>`;
          }
          if (value.access) {
            parts[partIndex++] = `            <news:access>${value.access}</news:access>`;
          }
          if (value.genres) {
            parts[partIndex++] = `            <news:genres>${escapeValueForXml(value.genres)}</news:genres>`;
          }
          if (value.keywords) {
            parts[partIndex++] = `            <news:keywords>${escapeValueForXml(value.keywords)}</news:keywords>`;
          }
          if (value.stock_tickers) {
            parts[partIndex++] = `            <news:stock_tickers>${escapeValueForXml(value.stock_tickers)}</news:stock_tickers>`;
          }
          parts[partIndex++] = "        </news:news>";
        }
        break;
    }
  }
  parts[partIndex++] = "    </url>";
  return parts.slice(0, partIndex).join("\n");
}
function urlsToXml(urls, resolvers, { version, xsl, credits, minify }, errorInfo) {
  const estimatedSize = urls.length + 5;
  const xmlParts = Array.from({ length: estimatedSize });
  let partIndex = 0;
  let xslHref = xsl ? resolvers.relativeBaseUrlResolver(xsl) : false;
  if (xslHref && errorInfo && errorInfo.messages.length > 0) {
    xslHref = withQuery(xslHref, {
      errors: "true",
      error_messages: errorInfo.messages,
      error_urls: errorInfo.urls
    });
  }
  if (xslHref) {
    xmlParts[partIndex++] = `<?xml version="1.0" encoding="UTF-8"?><?xml-stylesheet type="text/xsl" href="${escapeValueForXml(xslHref)}"?>`;
  } else {
    xmlParts[partIndex++] = '<?xml version="1.0" encoding="UTF-8"?>';
  }
  xmlParts[partIndex++] = URLSET_OPENING_TAG;
  for (const url of urls) {
    xmlParts[partIndex++] = buildUrlXml(url);
  }
  xmlParts[partIndex++] = "</urlset>";
  if (credits) {
    xmlParts[partIndex++] = `<!-- XML Sitemap generated by @nuxtjs/sitemap v${version} at ${(/* @__PURE__ */ new Date()).toISOString()} -->`;
  }
  const xmlContent = xmlParts.slice(0, partIndex);
  if (minify) {
    return xmlContent.join("").replace(/(?<!<[^>]*)\s(?![^<]*>)/g, "");
  }
  return xmlContent.join("\n");
}

function resolveSitemapEntries(sitemap, urls, runtimeConfig, resolvers) {
  const {
    autoI18n,
    isI18nMapped
  } = runtimeConfig;
  const filterPath = createPathFilter({
    include: sitemap.include,
    exclude: sitemap.exclude
  });
  const _urls = urls.map((_e) => {
    const e = preNormalizeEntry(_e, resolvers);
    if (!e.loc || !filterPath(e.loc))
      return false;
    return e;
  }).filter(Boolean);
  let validI18nUrlsForTransform = [];
  let warnIncorrectI18nTransformUsage = false;
  const withoutPrefixPaths = {};
  if (autoI18n && autoI18n.strategy !== "no_prefix") {
    const localeCodes = autoI18n.locales.map((l) => l.code);
    validI18nUrlsForTransform = _urls.map((_e, i) => {
      if (_e._abs)
        return false;
      const split = splitForLocales(_e._relativeLoc, localeCodes);
      let localeCode = split[0];
      const pathWithoutPrefix = split[1];
      if (!localeCode)
        localeCode = autoI18n.defaultLocale;
      const e = _e;
      e._pathWithoutPrefix = pathWithoutPrefix;
      const locale = autoI18n.locales.find((l) => l.code === localeCode);
      if (!locale)
        return false;
      e._locale = locale;
      e._index = i;
      e._key = `${e._sitemap || ""}${e._path?.pathname || "/"}${e._path.search}`;
      withoutPrefixPaths[pathWithoutPrefix] = withoutPrefixPaths[pathWithoutPrefix] || [];
      if (!withoutPrefixPaths[pathWithoutPrefix].some((e2) => e2._locale.code === locale.code))
        withoutPrefixPaths[pathWithoutPrefix].push(e);
      return e;
    }).filter(Boolean);
    for (const e of validI18nUrlsForTransform) {
      if (!e._i18nTransform && !e.alternatives?.length) {
        const alternatives = withoutPrefixPaths[e._pathWithoutPrefix].map((u) => {
          const entries = [];
          if (u._locale.code === autoI18n.defaultLocale) {
            entries.push({
              href: u.loc,
              hreflang: "x-default"
            });
          }
          entries.push({
            href: u.loc,
            hreflang: u._locale._hreflang || autoI18n.defaultLocale
          });
          return entries;
        }).flat().filter(Boolean);
        if (alternatives.length)
          e.alternatives = alternatives;
      } else if (e._i18nTransform) {
        delete e._i18nTransform;
        if (autoI18n.strategy === "no_prefix") {
          warnIncorrectI18nTransformUsage = true;
        }
        if (autoI18n.differentDomains) {
          e.alternatives = [
            {
              // apply default locale domain
              ...autoI18n.locales.find((l) => [l.code, l.language].includes(autoI18n.defaultLocale)),
              code: "x-default"
            },
            ...autoI18n.locales.filter((l) => !!l.domain)
          ].map((locale) => {
            return {
              hreflang: locale._hreflang,
              href: joinURL(withHttps(locale.domain), e._pathWithoutPrefix)
            };
          });
        } else {
          for (const l of autoI18n.locales) {
            let loc = e._pathWithoutPrefix;
            if (autoI18n.pages) {
              const pageKey = e._pathWithoutPrefix.replace(/^\//, "").replace(/\/index$/, "") || "index";
              const pageMappings = autoI18n.pages[pageKey];
              if (pageMappings && pageMappings[l.code] !== void 0) {
                const customPath = pageMappings[l.code];
                if (customPath === false)
                  continue;
                if (typeof customPath === "string")
                  loc = customPath.startsWith("/") ? customPath : `/${customPath}`;
              } else if (!autoI18n.differentDomains && !(["prefix_and_default", "prefix_except_default"].includes(autoI18n.strategy) && l.code === autoI18n.defaultLocale)) {
                loc = joinURL(`/${l.code}`, e._pathWithoutPrefix);
              }
            } else {
              if (!autoI18n.differentDomains && !(["prefix_and_default", "prefix_except_default"].includes(autoI18n.strategy) && l.code === autoI18n.defaultLocale))
                loc = joinURL(`/${l.code}`, e._pathWithoutPrefix);
            }
            const _sitemap = isI18nMapped ? l._sitemap : void 0;
            const newEntry = preNormalizeEntry({
              _sitemap,
              ...e,
              _index: void 0,
              _key: `${_sitemap || ""}${loc || "/"}${e._path.search}`,
              _locale: l,
              loc,
              alternatives: [{ code: "x-default", _hreflang: "x-default" }, ...autoI18n.locales].map((locale) => {
                const code = locale.code === "x-default" ? autoI18n.defaultLocale : locale.code;
                const isDefault = locale.code === "x-default" || locale.code === autoI18n.defaultLocale;
                let href = e._pathWithoutPrefix;
                if (autoI18n.pages) {
                  const pageKey = e._pathWithoutPrefix.replace(/^\//, "").replace(/\/index$/, "") || "index";
                  const pageMappings = autoI18n.pages[pageKey];
                  if (pageMappings && pageMappings[code] !== void 0) {
                    const customPath = pageMappings[code];
                    if (customPath === false)
                      return false;
                    if (typeof customPath === "string")
                      href = customPath.startsWith("/") ? customPath : `/${customPath}`;
                  } else if (autoI18n.strategy === "prefix") {
                    href = joinURL("/", code, e._pathWithoutPrefix);
                  } else if (["prefix_and_default", "prefix_except_default"].includes(autoI18n.strategy)) {
                    if (!isDefault) {
                      href = joinURL("/", code, e._pathWithoutPrefix);
                    }
                  }
                } else {
                  if (autoI18n.strategy === "prefix") {
                    href = joinURL("/", code, e._pathWithoutPrefix);
                  } else if (["prefix_and_default", "prefix_except_default"].includes(autoI18n.strategy)) {
                    if (!isDefault) {
                      href = joinURL("/", code, e._pathWithoutPrefix);
                    }
                  }
                }
                if (!filterPath(href))
                  return false;
                return {
                  hreflang: locale._hreflang,
                  href
                };
              }).filter(Boolean)
            }, resolvers);
            if (e._locale.code === newEntry._locale.code) {
              _urls[e._index] = newEntry;
              e._index = void 0;
            } else {
              _urls.push(newEntry);
            }
          }
        }
      }
      if (isI18nMapped) {
        e._sitemap = e._sitemap || e._locale._sitemap;
        e._key = `${e._sitemap || ""}${e.loc || "/"}${e._path.search}`;
      }
      if (e._index)
        _urls[e._index] = e;
    }
  }
  if (warnIncorrectI18nTransformUsage) {
    logger.warn("You're using _i18nTransform with the `no_prefix` strategy. This will cause issues with the sitemap. Please remove the _i18nTransform flag or change i18n strategy.");
  }
  return _urls;
}
async function buildSitemapUrls(sitemap, resolvers, runtimeConfig, nitro) {
  const {
    sitemaps,
    // enhancing
    autoI18n,
    isI18nMapped,
    isMultiSitemap,
    // sorting
    sortEntries,
    // chunking
    defaultSitemapsChunkSize
  } = runtimeConfig;
  const chunkInfo = parseChunkInfo(sitemap.sitemapName, sitemaps, defaultSitemapsChunkSize);
  function maybeSort(urls2) {
    return sortEntries ? sortInPlace(urls2) : urls2;
  }
  function maybeSlice(urls2) {
    return sliceUrlsForChunk(urls2, sitemap.sitemapName, sitemaps, defaultSitemapsChunkSize);
  }
  if (autoI18n?.differentDomains) {
    const domain = autoI18n.locales.find((e) => [e.language, e.code].includes(sitemap.sitemapName))?.domain;
    if (domain) {
      const _tester = resolvers.canonicalUrlResolver;
      resolvers.canonicalUrlResolver = (path) => resolveSitePath(path, {
        absolute: true,
        withBase: false,
        siteUrl: withHttps(domain),
        trailingSlash: _tester("/test/").endsWith("/"),
        base: "/"
      });
    }
  }
  let effectiveSitemap = sitemap;
  const baseSitemapName = chunkInfo.baseSitemapName;
  if (chunkInfo.isChunked && baseSitemapName !== sitemap.sitemapName && sitemaps[baseSitemapName]) {
    effectiveSitemap = sitemaps[baseSitemapName];
  }
  let sourcesInput = effectiveSitemap.includeAppSources ? await globalSitemapSources() : [];
  sourcesInput.push(...await childSitemapSources(effectiveSitemap));
  if (nitro && resolvers.event) {
    const ctx = {
      event: resolvers.event,
      sitemapName: baseSitemapName,
      sources: sourcesInput
    };
    await nitro.hooks.callHook("sitemap:sources", ctx);
    sourcesInput = ctx.sources;
  }
  const sources = await resolveSitemapSources(sourcesInput, resolvers.event);
  const failedSources = sources.filter((source) => source.error && source._isFailure).map((source) => ({
    url: typeof source.fetch === "string" ? source.fetch : source.fetch?.[0] || "unknown",
    error: source.error || "Unknown error"
  }));
  const resolvedCtx = {
    urls: sources.flatMap((s) => s.urls),
    sitemapName: sitemap.sitemapName,
    event: resolvers.event
  };
  await nitro?.hooks.callHook("sitemap:input", resolvedCtx);
  const enhancedUrls = resolveSitemapEntries(sitemap, resolvedCtx.urls, { autoI18n, isI18nMapped }, resolvers);
  const filteredUrls = enhancedUrls.filter((e) => {
    if (isMultiSitemap && e._sitemap && sitemap.sitemapName)
      return e._sitemap === sitemap.sitemapName;
    return true;
  });
  const sortedUrls = maybeSort(filteredUrls);
  const urls = maybeSlice(sortedUrls);
  return { urls, failedSources };
}

function defineNitroPlugin(def) {
  return def;
}

function defineRenderHandler(render) {
  const runtimeConfig = useRuntimeConfig();
  return eventHandler(async (event) => {
    const nitroApp = useNitroApp();
    const ctx = { event, render, response: void 0 };
    await nitroApp.hooks.callHook("render:before", ctx);
    if (!ctx.response) {
      if (event.path === `${runtimeConfig.app.baseURL}favicon.ico`) {
        setResponseHeader(event, "Content-Type", "image/x-icon");
        return send(
          event,
          "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
        );
      }
      ctx.response = await ctx.render(event);
      if (!ctx.response) {
        const _currentStatus = getResponseStatus(event);
        setResponseStatus(event, _currentStatus === 200 ? 500 : _currentStatus);
        return send(
          event,
          "No response returned from render handler: " + event.path
        );
      }
    }
    await nitroApp.hooks.callHook("render:response", ctx.response, ctx);
    if (ctx.response.headers) {
      setResponseHeaders(event, ctx.response.headers);
    }
    if (ctx.response.statusCode || ctx.response.statusMessage) {
      setResponseStatus(
        event,
        ctx.response.statusCode,
        ctx.response.statusMessage
      );
    }
    return ctx.response.body;
  });
}

const scheduledTasks = false;

const tasks = {
  
};

const __runningTasks__ = {};
async function runTask(name, {
  payload = {},
  context = {}
} = {}) {
  if (__runningTasks__[name]) {
    return __runningTasks__[name];
  }
  if (!(name in tasks)) {
    throw createError({
      message: `Task \`${name}\` is not available!`,
      statusCode: 404
    });
  }
  if (!tasks[name].resolve) {
    throw createError({
      message: `Task \`${name}\` is not implemented!`,
      statusCode: 501
    });
  }
  const handler = await tasks[name].resolve();
  const taskEvent = { name, payload, context };
  __runningTasks__[name] = handler.run(taskEvent);
  try {
    const res = await __runningTasks__[name];
    return res;
  } finally {
    delete __runningTasks__[name];
  }
}

function buildAssetsDir() {
  return useRuntimeConfig().app.buildAssetsDir;
}
function buildAssetsURL(...path) {
  return joinRelativeURL(publicAssetsURL(), buildAssetsDir(), ...path);
}
function publicAssetsURL(...path) {
  const app = useRuntimeConfig().app;
  const publicBase = app.cdnURL || app.baseURL;
  return path.length ? joinRelativeURL(publicBase, ...path) : publicBase;
}

function useNitroUrlResolvers(e) {
  const canonicalQuery = getQuery$1(e).canonical;
  const isShowingCanonical = typeof canonicalQuery !== "undefined" && canonicalQuery !== "false";
  const siteConfig = useSiteConfig(e);
  return {
    event: e,
    fixSlashes: (path) => fixSlashes(siteConfig.trailingSlash, path),
    // we need these as they depend on the nitro event
    canonicalUrlResolver: createSitePathResolver(e, {
      canonical: isShowingCanonical || false,
      absolute: true,
      withBase: true
    }),
    relativeBaseUrlResolver: createSitePathResolver(e, { absolute: false, withBase: true })
  };
}
async function buildSitemapXml(event, definition, resolvers, runtimeConfig) {
  const { sitemapName } = definition;
  const nitro = useNitroApp();
  const { urls: sitemapUrls, failedSources } = await buildSitemapUrls(definition, resolvers, runtimeConfig, nitro);
  const routeRuleMatcher = createNitroRouteRuleMatcher();
  const { autoI18n } = runtimeConfig;
  let validCount = 0;
  for (let i = 0; i < sitemapUrls.length; i++) {
    const u = sitemapUrls[i];
    const path = u._path?.pathname || u.loc;
    let routeRules = routeRuleMatcher(path);
    if (autoI18n?.locales && autoI18n?.strategy !== "no_prefix") {
      const match = splitForLocales(path, autoI18n.locales.map((l) => l.code));
      const pathWithoutPrefix = match[1];
      if (pathWithoutPrefix && pathWithoutPrefix !== path)
        routeRules = defu$1(routeRules, routeRuleMatcher(pathWithoutPrefix));
    }
    if (routeRules.sitemap === false)
      continue;
    if (typeof routeRules.robots !== "undefined" && !routeRules.robots)
      continue;
    const hasRobotsDisabled = Object.entries(routeRules.headers || {}).some(([name, value]) => name.toLowerCase() === "x-robots-tag" && value.toLowerCase().includes("noindex"));
    if (routeRules.redirect || hasRobotsDisabled)
      continue;
    sitemapUrls[validCount++] = routeRules.sitemap ? defu$1(u, routeRules.sitemap) : u;
  }
  sitemapUrls.length = validCount;
  const locSize = sitemapUrls.length;
  const resolvedCtx = {
    urls: sitemapUrls,
    sitemapName,
    event
  };
  await nitro.hooks.callHook("sitemap:resolved", resolvedCtx);
  if (resolvedCtx.urls.length !== locSize) {
    resolvedCtx.urls = resolvedCtx.urls.map((e) => preNormalizeEntry(e, resolvers));
  }
  const maybeSort = (urls2) => runtimeConfig.sortEntries ? sortInPlace(urls2) : urls2;
  const normalizedPreDedupe = resolvedCtx.urls.map((e) => normaliseEntry(e, definition.defaults, resolvers));
  const urls = maybeSort(mergeOnKey(normalizedPreDedupe, "_key").map((e) => normaliseEntry(e, definition.defaults, resolvers)));
  if (definition._isChunking && definition.sitemapName.includes("-")) {
    const parts = definition.sitemapName.split("-");
    const lastPart = parts.pop();
    if (!Number.isNaN(Number(lastPart))) {
      const chunkIndex = Number(lastPart);
      const baseSitemapName = parts.join("-");
      if (urls.length === 0 && chunkIndex > 0) {
        throw createError({
          statusCode: 404,
          message: `Sitemap chunk ${chunkIndex} for "${baseSitemapName}" does not exist.`
        });
      }
    }
  }
  const errorInfo = failedSources.length > 0 ? {
    messages: failedSources.map((f) => f.error),
    urls: failedSources.map((f) => f.url)
  } : void 0;
  const sitemap = urlsToXml(urls, resolvers, runtimeConfig, errorInfo);
  const ctx = { sitemap, sitemapName, event };
  await nitro.hooks.callHook("sitemap:output", ctx);
  return ctx.sitemap;
}
defineCachedFunction(
  buildSitemapXml,
  {
    name: "sitemap:xml",
    group: "sitemap",
    maxAge: 60 * 10,
    // Default 10 minutes
    base: "sitemap",
    // Use the sitemap storage
    getKey: (event, definition) => {
      const host = getHeader(event, "host") || getHeader(event, "x-forwarded-host") || "";
      const proto = getHeader(event, "x-forwarded-proto") || "https";
      const sitemapName = definition.sitemapName || "default";
      return `${sitemapName}-${proto}-${host}`;
    },
    swr: true
    // Enable stale-while-revalidate
  }
);
async function createSitemap(event, definition, runtimeConfig) {
  const resolvers = useNitroUrlResolvers(event);
  const xml = await buildSitemapXml(event, definition, resolvers, runtimeConfig);
  setHeader(event, "Content-Type", "text/xml; charset=UTF-8");
  if (runtimeConfig.cacheMaxAgeSeconds) {
    setHeader(event, "Cache-Control", `public, max-age=${runtimeConfig.cacheMaxAgeSeconds}, s-maxage=${runtimeConfig.cacheMaxAgeSeconds}, stale-while-revalidate=3600`);
    const now = /* @__PURE__ */ new Date();
    setHeader(event, "X-Sitemap-Generated", now.toISOString());
    setHeader(event, "X-Sitemap-Cache-Duration", `${runtimeConfig.cacheMaxAgeSeconds}s`);
    const expiryTime = new Date(now.getTime() + runtimeConfig.cacheMaxAgeSeconds * 1e3);
    setHeader(event, "X-Sitemap-Cache-Expires", expiryTime.toISOString());
    const remainingSeconds = Math.floor((expiryTime.getTime() - now.getTime()) / 1e3);
    setHeader(event, "X-Sitemap-Cache-Remaining", `${remainingSeconds}s`);
  } else {
    setHeader(event, "Cache-Control", `no-cache, no-store`);
  }
  event.context._isSitemap = true;
  return xml;
}

const _mK6bEA = defineEventHandler(async (e) => {
  const runtimeConfig = useSitemapRuntimeConfig();
  const { sitemaps } = runtimeConfig;
  if ("index" in sitemaps) {
    return sendRedirect(e, withBase("/sitemap_index.xml", useRuntimeConfig().app.baseURL), 302 );
  }
  return createSitemap(e, Object.values(sitemaps)[0], runtimeConfig);
});

const VueResolver = (_, value) => {
  return isRef(value) ? toValue(value) : value;
};

const headSymbol = "usehead";
function vueInstall(head) {
  const plugin = {
    install(app) {
      app.config.globalProperties.$unhead = head;
      app.config.globalProperties.$head = head;
      app.provide(headSymbol, head);
    }
  };
  return plugin.install;
}

function resolveUnrefHeadInput(input) {
  return walkResolver(input, VueResolver);
}

function createHead(options = {}) {
  const head = createHead$1({
    ...options,
    propResolvers: [VueResolver]
  });
  head.install = vueInstall(head);
  return head;
}

const unheadOptions = {
  disableDefaults: true,
  disableCapoSorting: false,
  plugins: [DeprecationsPlugin, PromisesPlugin, TemplateParamsPlugin, AliasSortingPlugin],
};

function createSSRContext(event) {
  const ssrContext = {
    url: event.path,
    event,
    runtimeConfig: useRuntimeConfig(event),
    noSSR: event.context.nuxt?.noSSR || (false),
    head: createHead(unheadOptions),
    error: false,
    nuxt: void 0,
    /* NuxtApp */
    payload: {},
    _payloadReducers: /* @__PURE__ */ Object.create(null),
    modules: /* @__PURE__ */ new Set()
  };
  return ssrContext;
}
function setSSRError(ssrContext, error) {
  ssrContext.error = true;
  ssrContext.payload = { error };
  ssrContext.url = error.url;
}

const APP_ROOT_OPEN_TAG = `<${appRootTag}${propsToString(appRootAttrs)}>`;
const APP_ROOT_CLOSE_TAG = `</${appRootTag}>`;
const getServerEntry = () => import('file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/.nuxt/dist/server/server.mjs').then((r) => r.default || r);
const getClientManifest = () => import('file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/.nuxt/dist/server/client.manifest.mjs').then((r) => r.default || r).then((r) => typeof r === "function" ? r() : r);
const getSSRRenderer = lazyCachedFunction(async () => {
  const manifest = await getClientManifest();
  if (!manifest) {
    throw new Error("client.manifest is not available");
  }
  const createSSRApp = await getServerEntry();
  if (!createSSRApp) {
    throw new Error("Server bundle is not available");
  }
  const options = {
    manifest,
    renderToString: renderToString$1,
    buildAssetsURL
  };
  const renderer = createRenderer(createSSRApp, options);
  async function renderToString$1(input, context) {
    const html = await renderToString(input, context);
    if (process.env.NUXT_VITE_NODE_OPTIONS) {
      renderer.rendererContext.updateManifest(await getClientManifest());
    }
    return APP_ROOT_OPEN_TAG + html + APP_ROOT_CLOSE_TAG;
  }
  return renderer;
});
const getSPARenderer = lazyCachedFunction(async () => {
  const manifest = await getClientManifest();
  const spaTemplate = await Promise.resolve().then(function () { return _virtual__spaTemplate; }).then((r) => r.template).catch(() => "").then((r) => {
    {
      return APP_ROOT_OPEN_TAG + r + APP_ROOT_CLOSE_TAG;
    }
  });
  const options = {
    manifest,
    renderToString: () => spaTemplate,
    buildAssetsURL
  };
  const renderer = createRenderer(() => () => {
  }, options);
  const result = await renderer.renderToString({});
  const renderToString = (ssrContext) => {
    const config = useRuntimeConfig(ssrContext.event);
    ssrContext.modules ||= /* @__PURE__ */ new Set();
    ssrContext.payload.serverRendered = false;
    ssrContext.config = {
      public: config.public,
      app: config.app
    };
    return Promise.resolve(result);
  };
  return {
    rendererContext: renderer.rendererContext,
    renderToString
  };
});
function lazyCachedFunction(fn) {
  let res = null;
  return () => {
    if (res === null) {
      res = fn().catch((err) => {
        res = null;
        throw err;
      });
    }
    return res;
  };
}
function getRenderer(ssrContext) {
  return ssrContext.noSSR ? getSPARenderer() : getSSRRenderer();
}
const getSSRStyles = lazyCachedFunction(() => Promise.resolve().then(function () { return styles$1; }).then((r) => r.default || r));

async function renderInlineStyles(usedModules) {
  const styleMap = await getSSRStyles();
  const inlinedStyles = /* @__PURE__ */ new Set();
  for (const mod of usedModules) {
    if (mod in styleMap && styleMap[mod]) {
      for (const style of await styleMap[mod]()) {
        inlinedStyles.add(style);
      }
    }
  }
  return Array.from(inlinedStyles).map((style) => ({ innerHTML: style }));
}

const ROOT_NODE_REGEX = new RegExp(`^<${appRootTag}[^>]*>([\\s\\S]*)<\\/${appRootTag}>$`);
function getServerComponentHTML(body) {
  const match = body.match(ROOT_NODE_REGEX);
  return match?.[1] || body;
}
const SSR_SLOT_TELEPORT_MARKER = /^uid=([^;]*);slot=(.*)$/;
const SSR_CLIENT_TELEPORT_MARKER = /^uid=([^;]*);client=(.*)$/;
const SSR_CLIENT_SLOT_MARKER = /^island-slot=([^;]*);(.*)$/;
function getSlotIslandResponse(ssrContext) {
  if (!ssrContext.islandContext || !Object.keys(ssrContext.islandContext.slots).length) {
    return void 0;
  }
  const response = {};
  for (const [name, slot] of Object.entries(ssrContext.islandContext.slots)) {
    response[name] = {
      ...slot,
      fallback: ssrContext.teleports?.[`island-fallback=${name}`]
    };
  }
  return response;
}
function getClientIslandResponse(ssrContext) {
  if (!ssrContext.islandContext || !Object.keys(ssrContext.islandContext.components).length) {
    return void 0;
  }
  const response = {};
  for (const [clientUid, component] of Object.entries(ssrContext.islandContext.components)) {
    const html = ssrContext.teleports?.[clientUid]?.replaceAll("<!--teleport start anchor-->", "") || "";
    response[clientUid] = {
      ...component,
      html,
      slots: getComponentSlotTeleport(clientUid, ssrContext.teleports ?? {})
    };
  }
  return response;
}
function getComponentSlotTeleport(clientUid, teleports) {
  const entries = Object.entries(teleports);
  const slots = {};
  for (const [key, value] of entries) {
    const match = key.match(SSR_CLIENT_SLOT_MARKER);
    if (match) {
      const [, id, slot] = match;
      if (!slot || clientUid !== id) {
        continue;
      }
      slots[slot] = value;
    }
  }
  return slots;
}
function replaceIslandTeleports(ssrContext, html) {
  const { teleports, islandContext } = ssrContext;
  if (islandContext || !teleports) {
    return html;
  }
  for (const key in teleports) {
    const matchClientComp = key.match(SSR_CLIENT_TELEPORT_MARKER);
    if (matchClientComp) {
      const [, uid, clientId] = matchClientComp;
      if (!uid || !clientId) {
        continue;
      }
      html = html.replace(new RegExp(` data-island-uid="${uid}" data-island-component="${clientId}"[^>]*>`), (full) => {
        return full + teleports[key];
      });
      continue;
    }
    const matchSlot = key.match(SSR_SLOT_TELEPORT_MARKER);
    if (matchSlot) {
      const [, uid, slot] = matchSlot;
      if (!uid || !slot) {
        continue;
      }
      html = html.replace(new RegExp(` data-island-uid="${uid}" data-island-slot="${slot}"[^>]*>`), (full) => {
        return full + teleports[key];
      });
    }
  }
  return html;
}

const ISLAND_SUFFIX_RE = /\.json(\?.*)?$/;
const _SxA8c9 = defineEventHandler(async (event) => {
  const nitroApp = useNitroApp();
  setResponseHeaders(event, {
    "content-type": "application/json;charset=utf-8",
    "x-powered-by": "Nuxt"
  });
  const islandContext = await getIslandContext(event);
  const ssrContext = {
    ...createSSRContext(event),
    islandContext,
    noSSR: false,
    url: islandContext.url
  };
  const renderer = await getSSRRenderer();
  const renderResult = await renderer.renderToString(ssrContext).catch(async (error) => {
    await ssrContext.nuxt?.hooks.callHook("app:error", error);
    throw error;
  });
  const inlinedStyles = await renderInlineStyles(ssrContext.modules ?? []);
  await ssrContext.nuxt?.hooks.callHook("app:rendered", { ssrContext, renderResult });
  if (inlinedStyles.length) {
    ssrContext.head.push({ style: inlinedStyles });
  }
  {
    const { styles } = getRequestDependencies(ssrContext, renderer.rendererContext);
    const link = [];
    for (const resource of Object.values(styles)) {
      if ("inline" in getQuery(resource.file)) {
        continue;
      }
      if (resource.file.includes("scoped") && !resource.file.includes("pages/")) {
        link.push({ rel: "stylesheet", href: renderer.rendererContext.buildAssetsURL(resource.file), crossorigin: "" });
      }
    }
    if (link.length) {
      ssrContext.head.push({ link }, { mode: "server" });
    }
  }
  const islandHead = {};
  for (const entry of ssrContext.head.entries.values()) {
    for (const [key, value] of Object.entries(resolveUnrefHeadInput(entry.input))) {
      const currentValue = islandHead[key];
      if (Array.isArray(currentValue)) {
        currentValue.push(...value);
      }
      islandHead[key] = value;
    }
  }
  islandHead.link ||= [];
  islandHead.style ||= [];
  const islandResponse = {
    id: islandContext.id,
    head: islandHead,
    html: getServerComponentHTML(renderResult.html),
    components: getClientIslandResponse(ssrContext),
    slots: getSlotIslandResponse(ssrContext)
  };
  await nitroApp.hooks.callHook("render:island", islandResponse, { event, islandContext });
  return islandResponse;
});
async function getIslandContext(event) {
  let url = event.path || "";
  const componentParts = url.substring("/__nuxt_island".length + 1).replace(ISLAND_SUFFIX_RE, "").split("_");
  const hashId = componentParts.length > 1 ? componentParts.pop() : void 0;
  const componentName = componentParts.join("_");
  const context = event.method === "GET" ? getQuery$1(event) : await readBody(event);
  const ctx = {
    url: "/",
    ...context,
    id: hashId,
    name: componentName,
    props: destr$1(context.props) || {},
    slots: {},
    components: {}
  };
  return ctx;
}

const _Mtl4zW = eventHandler(async (event) => {
  const collection = getRouterParam(event, "collection");
  setHeader(event, "Content-Type", "text/plain");
  const data = await useStorage().getItem(`build:content:database.compressed.mjs`) || "";
  if (data) {
    const lineStart = `export const ${collection} = "`;
    const content = String(data).split("\n").find((line) => line.startsWith(lineStart));
    if (content) {
      return content.substring(lineStart.length, content.length - 1);
    }
  }
  return await import('file://E:/1.web%20bisnis/bisnis-travel/website-tour-travel-nuxt/jogjaliburan/.nuxt/content/database.compressed.mjs').then((m) => m[collection]);
});

async function decompressSQLDump(base64Str, compressionType = "gzip") {
  const binaryData = Uint8Array.from(atob(base64Str), (c) => c.charCodeAt(0));
  const response = new Response(new Blob([binaryData]));
  const decompressedStream = response.body?.pipeThrough(new DecompressionStream(compressionType));
  const text = await new Response(decompressedStream).text();
  return JSON.parse(text);
}

function refineContentFields(sql, doc) {
  const fields = findCollectionFields(sql);
  const item = { ...doc };
  for (const key in item) {
    if (fields[key] === "json" && item[key] && item[key] !== "undefined") {
      item[key] = JSON.parse(item[key]);
    }
    if (fields[key] === "boolean" && item[key] !== "undefined") {
      item[key] = Boolean(item[key]);
    }
  }
  for (const key in item) {
    if (item[key] === "NULL") {
      item[key] = void 0;
    }
  }
  return item;
}
function findCollectionFields(sql) {
  const table = sql.match(/FROM\s+(\w+)/);
  if (!table) {
    return {};
  }
  const info = contentManifest[getCollectionName(table[1])];
  return info?.fields || {};
}
function getCollectionName(table) {
  return table.replace(/^_content_/, "");
}

let db;
function loadDatabaseAdapter(config) {
  const { database, localDatabase } = config;
  if (!db) {
    {
      db = localAdapter(refineDatabaseConfig(localDatabase));
    }
  }
  return {
    all: async (sql, params = []) => {
      return db.prepare(sql).all(...params).then((result) => (result || []).map((item) => refineContentFields(sql, item)));
    },
    first: async (sql, params = []) => {
      return db.prepare(sql).get(...params).then((item) => item ? refineContentFields(sql, item) : item);
    },
    exec: async (sql, params = []) => {
      return db.prepare(sql).run(...params);
    }
  };
}
const checkDatabaseIntegrity = {};
const integrityCheckPromise = {};
async function checkAndImportDatabaseIntegrity(event, collection, config) {
  if (checkDatabaseIntegrity[String(collection)] !== false) {
    checkDatabaseIntegrity[String(collection)] = false;
    integrityCheckPromise[String(collection)] = integrityCheckPromise[String(collection)] || _checkAndImportDatabaseIntegrity(event, collection, checksums[String(collection)], checksumsStructure[String(collection)], config).then((isValid) => {
      checkDatabaseIntegrity[String(collection)] = !isValid;
    }).catch((error) => {
      console.error("Database integrity check failed", error);
      checkDatabaseIntegrity[String(collection)] = true;
      integrityCheckPromise[String(collection)] = null;
    });
  }
  if (integrityCheckPromise[String(collection)]) {
    await integrityCheckPromise[String(collection)];
  }
}
async function _checkAndImportDatabaseIntegrity(event, collection, integrityVersion, structureIntegrityVersion, config) {
  const db2 = loadDatabaseAdapter(config);
  const before = await db2.first(`SELECT * FROM ${tables.info} WHERE id = ?`, [`checksum_${collection}`]).catch(() => null);
  if (before?.version && !String(before.version)?.startsWith(`${config.databaseVersion}--`)) {
    await db2.exec(`DROP TABLE IF EXISTS ${tables.info}`);
    before.version = "";
  }
  const unchangedStructure = before?.structureVersion === structureIntegrityVersion;
  if (before?.version) {
    if (before.version === integrityVersion) {
      if (before.ready) {
        return true;
      }
      await waitUntilDatabaseIsReady(db2, collection);
      return true;
    }
    await db2.exec(`DELETE FROM ${tables.info} WHERE id = ?`, [`checksum_${collection}`]);
    if (!unchangedStructure) {
      await db2.exec(`DROP TABLE IF EXISTS ${tables[collection]}`);
    }
  }
  const dump = await loadDatabaseDump(event, collection).then(decompressSQLDump);
  const dumpLinesHash = dump.map((row) => row.split(" -- ").pop());
  let hashesInDb = /* @__PURE__ */ new Set();
  if (unchangedStructure) {
    const hashListFromTheDump = new Set(dumpLinesHash);
    const hashesInDbRecords = await db2.all(`SELECT __hash__ FROM ${tables[collection]}`).catch(() => []);
    hashesInDb = new Set(hashesInDbRecords.map((r) => r.__hash__));
    const hashesToDelete = hashesInDb.difference(hashListFromTheDump);
    if (hashesToDelete.size) {
      await db2.exec(`DELETE FROM ${tables[collection]} WHERE __hash__ IN (${Array(hashesToDelete.size).fill("?").join(",")})`, Array.from(hashesToDelete));
    }
  }
  await dump.reduce(async (prev, sql, index) => {
    await prev;
    const hash = dumpLinesHash[index];
    const statement = sql.substring(0, sql.length - hash.length - 4);
    if (unchangedStructure) {
      if (hash === "structure") {
        return Promise.resolve();
      }
      if (hashesInDb.has(hash)) {
        return Promise.resolve();
      }
    }
    await db2.exec(statement).catch((err) => {
      const message = err.message || "Unknown error";
      console.error(`Failed to execute SQL ${sql}: ${message}`);
    });
  }, Promise.resolve());
  const after = await db2.first(`SELECT version FROM ${tables.info} WHERE id = ?`, [`checksum_${collection}`]).catch(() => ({ version: "" }));
  return after?.version === integrityVersion;
}
const REQUEST_TIMEOUT = 90;
async function waitUntilDatabaseIsReady(db2, collection) {
  let iterationCount = 0;
  let interval;
  await new Promise((resolve, reject) => {
    interval = setInterval(async () => {
      const row = await db2.first(`SELECT ready FROM ${tables.info} WHERE id = ?`, [`checksum_${collection}`]).catch(() => ({ ready: true }));
      if (row?.ready) {
        clearInterval(interval);
        resolve(0);
      }
      if (iterationCount++ > REQUEST_TIMEOUT) {
        clearInterval(interval);
        reject(new Error("Waiting for another database initialization timed out"));
      }
    }, 1e3);
  }).catch((e) => {
    throw e;
  }).finally(() => {
    if (interval) {
      clearInterval(interval);
    }
  });
}
async function loadDatabaseDump(event, collection) {
  return await fetchDatabase(event, String(collection)).catch((e) => {
    console.error("Failed to fetch compressed dump", e);
    return "";
  });
}
function refineDatabaseConfig(config) {
  if (config.type === "d1") {
    return { ...config, bindingName: config.bindingName || config.binding };
  }
  if (config.type === "sqlite") {
    const _config = { ...config };
    if (config.filename === ":memory:") {
      return { name: "memory" };
    }
    if ("filename" in config) {
      const filename = isAbsolute(config?.filename || "") || config?.filename === ":memory:" ? config?.filename : new URL(config.filename, globalThis._importMeta_.url).pathname;
      _config.path = process.platform === "win32" && filename.startsWith("/") ? filename.slice(1) : filename;
    }
    return _config;
  }
  return config;
}

const SQL_COMMANDS = /SELECT|INSERT|UPDATE|DELETE|DROP|ALTER|\$/i;
const SQL_COUNT_REGEX = /COUNT\((DISTINCT )?([a-z_]\w+|\*)\)/i;
const SQL_SELECT_REGEX = /^SELECT (.*) FROM (\w+)( WHERE .*)? ORDER BY (["\w,\s]+) (ASC|DESC)( LIMIT \d+)?( OFFSET \d+)?$/;
function assertSafeQuery(sql, collection) {
  if (!sql) {
    throw new Error("Invalid query");
  }
  const cleanedupQuery = cleanupQuery(sql);
  if (cleanedupQuery !== sql) {
    throw new Error("Invalid query");
  }
  const match = sql.match(SQL_SELECT_REGEX);
  if (!match) {
    throw new Error("Invalid query");
  }
  const [_, select, from, where, orderBy, order, limit, offset] = match;
  const columns = select.trim().split(", ");
  if (columns.length === 1) {
    if (columns[0] !== "*" && !columns[0].match(SQL_COUNT_REGEX) && !columns[0].match(/^"[a-z_]\w+"$/i)) {
      throw new Error("Invalid query");
    }
  } else if (!columns.every((column) => column.match(/^"[a-z_]\w+"$/i))) {
    throw new Error("Invalid query");
  }
  if (from !== `_content_${collection}`) {
    throw new Error("Invalid query");
  }
  if (where) {
    if (!where.startsWith(" WHERE (") || !where.endsWith(")")) {
      throw new Error("Invalid query");
    }
    const noString = cleanupQuery(where, { removeString: true });
    if (noString.match(SQL_COMMANDS)) {
      throw new Error("Invalid query");
    }
  }
  const _order = (orderBy + " " + order).split(", ");
  if (!_order.every((column) => column.match(/^("[a-zA-Z_]+"|[a-zA-Z_]+) (ASC|DESC)$/))) {
    throw new Error("Invalid query");
  }
  if (limit !== void 0 && !limit.match(/^ LIMIT \d+$/)) {
    throw new Error("Invalid query");
  }
  if (offset !== void 0 && !offset.match(/^ OFFSET \d+$/)) {
    throw new Error("Invalid query");
  }
  return true;
}
function cleanupQuery(query, options = { removeString: false }) {
  let inString = false;
  let stringFence = "";
  let result = "";
  for (let i = 0; i < query.length; i++) {
    const char = query[i];
    const prevChar = query[i - 1];
    const nextChar = query[i + 1];
    if (char === "'" || char === '"') {
      if (!options?.removeString) {
        result += char;
        continue;
      }
      if (inString) {
        if (char !== stringFence || nextChar === stringFence || prevChar === stringFence) {
          continue;
        }
        inString = false;
        stringFence = "";
        continue;
      } else {
        inString = true;
        stringFence = char;
        continue;
      }
    }
    if (!inString) {
      if (char === "-" && nextChar === "-") {
        return result;
      }
      if (char === "/" && nextChar === "*") {
        i += 2;
        while (i < query.length && !(query[i] === "*" && query[i + 1] === "/")) {
          i += 1;
        }
        i += 2;
        continue;
      }
      result += char;
    }
  }
  return result;
}

const _dzY6Vs = eventHandler(async (event) => {
  const { sql } = await readBody(event);
  const collection = getRouterParam(event, "collection");
  assertSafeQuery(sql, collection);
  const conf = useRuntimeConfig().content;
  if (conf.integrityCheck) {
    await checkAndImportDatabaseIntegrity(event, collection, conf);
  }
  return loadDatabaseAdapter(conf).all(sql);
});

const _XsIa7M = lazyEventHandler(() => {
  const opts = useRuntimeConfig().ipx || {};
  const fsDir = opts?.fs?.dir ? (Array.isArray(opts.fs.dir) ? opts.fs.dir : [opts.fs.dir]).map((dir) => isAbsolute(dir) ? dir : fileURLToPath(new URL(dir, globalThis._importMeta_.url))) : void 0;
  const fsStorage = opts.fs?.dir ? ipxFSStorage({ ...opts.fs, dir: fsDir }) : void 0;
  const httpStorage = opts.http?.domains ? ipxHttpStorage({ ...opts.http }) : void 0;
  if (!fsStorage && !httpStorage) {
    throw new Error("IPX storage is not configured!");
  }
  const ipxOptions = {
    ...opts,
    storage: fsStorage || httpStorage,
    httpStorage
  };
  const ipx = createIPX(ipxOptions);
  const ipxHandler = createIPXH3Handler(ipx);
  return useBase(opts.baseURL, ipxHandler);
});

const _lazy_XH92vF = () => Promise.resolve().then(function () { return _slug__get$1; });
const _lazy_EUrJFa = () => Promise.resolve().then(function () { return index_get$1; });
const _lazy_ZkA5zn = () => Promise.resolve().then(function () { return _slug_$3; });
const _lazy_rGA5RQ = () => Promise.resolve().then(function () { return _category_$1; });
const _lazy_6iLto9 = () => Promise.resolve().then(function () { return _slug_$1; });
const _lazy_S0RohX = () => Promise.resolve().then(function () { return _duration_$1; });
const _lazy_drmHnp = () => Promise.resolve().then(function () { return index$1; });
const _lazy_mnBSLV = () => Promise.resolve().then(function () { return paketGathering_get$1; });
const _lazy_zz20KP = () => Promise.resolve().then(function () { return paketHoneymoon_get$1; });
const _lazy_XHJMPH = () => Promise.resolve().then(function () { return paketWisata_get$1; });
const _lazy_RXUyqD = () => Promise.resolve().then(function () { return sewaMobil_get$1; });
const _lazy__Repau = () => Promise.resolve().then(function () { return renderer$1; });

const handlers = [
  { route: '/api/destinations/:slug', handler: _lazy_XH92vF, lazy: true, middleware: false, method: "get" },
  { route: '/api/destinations', handler: _lazy_EUrJFa, lazy: true, middleware: false, method: "get" },
  { route: '/api/paket-wisata/:slug', handler: _lazy_ZkA5zn, lazy: true, middleware: false, method: undefined },
  { route: '/api/paket-wisata/category/:category', handler: _lazy_rGA5RQ, lazy: true, middleware: false, method: undefined },
  { route: '/api/paket-wisata/detail/:slug', handler: _lazy_6iLto9, lazy: true, middleware: false, method: undefined },
  { route: '/api/paket-wisata/duration/:duration', handler: _lazy_S0RohX, lazy: true, middleware: false, method: undefined },
  { route: '/api/paket-wisata', handler: _lazy_drmHnp, lazy: true, middleware: false, method: undefined },
  { route: '/api/sitemap/paket-gathering', handler: _lazy_mnBSLV, lazy: true, middleware: false, method: "get" },
  { route: '/api/sitemap/paket-honeymoon', handler: _lazy_zz20KP, lazy: true, middleware: false, method: "get" },
  { route: '/api/sitemap/paket-wisata', handler: _lazy_XHJMPH, lazy: true, middleware: false, method: "get" },
  { route: '/api/sitemap/sewa-mobil', handler: _lazy_RXUyqD, lazy: true, middleware: false, method: "get" },
  { route: '/__nuxt_error', handler: _lazy__Repau, lazy: true, middleware: false, method: undefined },
  { route: '', handler: _6L9AR9, lazy: false, middleware: true, method: undefined },
  { route: '/__site-config__/debug.json', handler: _SdWPpK, lazy: false, middleware: false, method: undefined },
  { route: '/__sitemap__/nuxt-content-urls.json', handler: _heihII, lazy: false, middleware: false, method: undefined },
  { route: '/__sitemap__/debug.json', handler: _ME1LU5, lazy: false, middleware: false, method: undefined },
  { route: '/__sitemap__/style.xsl', handler: _pMKyjm, lazy: false, middleware: false, method: undefined },
  { route: '/sitemap.xml', handler: _mK6bEA, lazy: false, middleware: false, method: undefined },
  { route: '/__nuxt_island/**', handler: _SxA8c9, lazy: false, middleware: false, method: undefined },
  { route: '/__nuxt_content/:collection/sql_dump.txt', handler: _Mtl4zW, lazy: false, middleware: false, method: undefined },
  { route: '/__nuxt_content/:collection/query', handler: _dzY6Vs, lazy: false, middleware: false, method: undefined },
  { route: '/_ipx/**', handler: _XsIa7M, lazy: false, middleware: false, method: undefined },
  { route: '/api/**', handler: _lazy__Repau, lazy: true, middleware: false, method: undefined },
  { route: '/**', handler: _lazy__Repau, lazy: true, middleware: false, method: undefined }
];

function createNitroApp() {
  const config = useRuntimeConfig();
  const hooks = createHooks();
  const captureError = (error, context = {}) => {
    const promise = hooks.callHookParallel("error", error, context).catch((error_) => {
      console.error("Error while capturing another error", error_);
    });
    if (context.event && isEvent(context.event)) {
      const errors = context.event.context.nitro?.errors;
      if (errors) {
        errors.push({ error, context });
      }
      if (context.event.waitUntil) {
        context.event.waitUntil(promise);
      }
    }
  };
  const h3App = createApp({
    debug: destr(true),
    onError: (error, event) => {
      captureError(error, { event, tags: ["request"] });
      return errorHandler(error, event);
    },
    onRequest: async (event) => {
      event.context.nitro = event.context.nitro || { errors: [] };
      const fetchContext = event.node.req?.__unenv__;
      if (fetchContext?._platform) {
        event.context = {
          _platform: fetchContext?._platform,
          // #3335
          ...fetchContext._platform,
          ...event.context
        };
      }
      if (!event.context.waitUntil && fetchContext?.waitUntil) {
        event.context.waitUntil = fetchContext.waitUntil;
      }
      event.fetch = (req, init) => fetchWithEvent(event, req, init, { fetch: localFetch });
      event.$fetch = (req, init) => fetchWithEvent(event, req, init, {
        fetch: $fetch
      });
      event.waitUntil = (promise) => {
        if (!event.context.nitro._waitUntilPromises) {
          event.context.nitro._waitUntilPromises = [];
        }
        event.context.nitro._waitUntilPromises.push(promise);
        if (event.context.waitUntil) {
          event.context.waitUntil(promise);
        }
      };
      event.captureError = (error, context) => {
        captureError(error, { event, ...context });
      };
      await nitroApp$1.hooks.callHook("request", event).catch((error) => {
        captureError(error, { event, tags: ["request"] });
      });
    },
    onBeforeResponse: async (event, response) => {
      await nitroApp$1.hooks.callHook("beforeResponse", event, response).catch((error) => {
        captureError(error, { event, tags: ["request", "response"] });
      });
    },
    onAfterResponse: async (event, response) => {
      await nitroApp$1.hooks.callHook("afterResponse", event, response).catch((error) => {
        captureError(error, { event, tags: ["request", "response"] });
      });
    }
  });
  const router = createRouter$1({
    preemptive: true
  });
  const nodeHandler = toNodeListener(h3App);
  const localCall = (aRequest) => callNodeRequestHandler(nodeHandler, aRequest);
  const localFetch = (input, init) => {
    if (!input.toString().startsWith("/")) {
      return globalThis.fetch(input, init);
    }
    return fetchNodeRequestHandler(
      nodeHandler,
      input,
      init
    ).then((response) => normalizeFetchResponse(response));
  };
  const $fetch = createFetch({
    fetch: localFetch,
    Headers: Headers$1,
    defaults: { baseURL: config.app.baseURL }
  });
  globalThis.$fetch = $fetch;
  h3App.use(createRouteRulesHandler({ localFetch }));
  for (const h of handlers) {
    let handler = h.lazy ? lazyEventHandler(h.handler) : h.handler;
    if (h.middleware || !h.route) {
      const middlewareBase = (config.app.baseURL + (h.route || "/")).replace(
        /\/+/g,
        "/"
      );
      h3App.use(middlewareBase, handler);
    } else {
      const routeRules = getRouteRulesForPath(
        h.route.replace(/:\w+|\*\*/g, "_")
      );
      if (routeRules.cache) {
        handler = cachedEventHandler(handler, {
          group: "nitro/routes",
          ...routeRules.cache
        });
      }
      router.use(h.route, handler, h.method);
    }
  }
  h3App.use(config.app.baseURL, router.handler);
  const app = {
    hooks,
    h3App,
    router,
    localCall,
    localFetch,
    captureError
  };
  return app;
}
function runNitroPlugins(nitroApp2) {
  for (const plugin of plugins) {
    try {
      plugin(nitroApp2);
    } catch (error) {
      nitroApp2.captureError(error, { tags: ["plugin"] });
      throw error;
    }
  }
}
const nitroApp$1 = createNitroApp();
function useNitroApp() {
  return nitroApp$1;
}
runNitroPlugins(nitroApp$1);

if (!globalThis.crypto) {
  globalThis.crypto = nodeCrypto;
}
const { NITRO_NO_UNIX_SOCKET, NITRO_DEV_WORKER_ID } = process.env;
trapUnhandledNodeErrors();
parentPort?.on("message", (msg) => {
  if (msg && msg.event === "shutdown") {
    shutdown();
  }
});
const nitroApp = useNitroApp();
const server = new Server(toNodeListener(nitroApp.h3App));
let listener;
listen().catch(() => listen(
  true
  /* use random port */
)).catch((error) => {
  console.error("Dev worker failed to listen:", error);
  return shutdown();
});
nitroApp.router.get(
  "/_nitro/tasks",
  defineEventHandler(async (event) => {
    const _tasks = await Promise.all(
      Object.entries(tasks).map(async ([name, task]) => {
        const _task = await task.resolve?.();
        return [name, { description: _task?.meta?.description }];
      })
    );
    return {
      tasks: Object.fromEntries(_tasks),
      scheduledTasks
    };
  })
);
nitroApp.router.use(
  "/_nitro/tasks/:name",
  defineEventHandler(async (event) => {
    const name = getRouterParam(event, "name");
    const payload = {
      ...getQuery$1(event),
      ...await readBody(event).then((r) => r?.payload).catch(() => ({}))
    };
    return await runTask(name, { payload });
  })
);
function listen(useRandomPort = Boolean(
  NITRO_NO_UNIX_SOCKET || process.versions.webcontainer || "Bun" in globalThis && process.platform === "win32"
)) {
  return new Promise((resolve, reject) => {
    try {
      listener = server.listen(useRandomPort ? 0 : getSocketAddress(), () => {
        const address = server.address();
        parentPort?.postMessage({
          event: "listen",
          address: typeof address === "string" ? { socketPath: address } : { host: "localhost", port: address?.port }
        });
        resolve();
      });
    } catch (error) {
      reject(error);
    }
  });
}
function getSocketAddress() {
  const socketName = `nitro-worker-${process.pid}-${threadId}-${NITRO_DEV_WORKER_ID}-${Math.round(Math.random() * 1e4)}.sock`;
  if (process.platform === "win32") {
    return join(String.raw`\\.\pipe`, socketName);
  }
  if (process.platform === "linux") {
    const nodeMajor = Number.parseInt(process.versions.node.split(".")[0], 10);
    if (nodeMajor >= 20) {
      return `\0${socketName}`;
    }
  }
  return join(tmpdir(), socketName);
}
async function shutdown() {
  server.closeAllConnections?.();
  await Promise.all([
    new Promise((resolve) => listener?.close(resolve)),
    nitroApp.hooks.callHook("close").catch(console.error)
  ]);
  parentPort?.postMessage({ event: "exit" });
}

const _messages = { "appName": "Nuxt", "version": "", "statusCode": 500, "statusMessage": "Server error", "description": "An error occurred in the application and the page could not be served. If you are the application owner, check your server logs for details.", "stack": "" };
const template$1 = (messages) => {
  messages = { ..._messages, ...messages };
  return '<!DOCTYPE html><html lang="en"><head><title>' + escapeHtml(messages.statusCode) + " - " + escapeHtml(messages.statusMessage || "Internal Server Error") + `</title><meta charset="utf-8"><meta content="width=device-width,initial-scale=1.0,minimum-scale=1.0" name="viewport"><style>.spotlight{background:linear-gradient(45deg,#00dc82,#36e4da 50%,#0047e1);bottom:-40vh;filter:blur(30vh);height:60vh;opacity:.8}*,:after,:before{border-color:var(--un-default-border-color,#e5e7eb);border-style:solid;border-width:0;box-sizing:border-box}:after,:before{--un-content:""}html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:ui-sans-serif,system-ui,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-feature-settings:normal;font-variation-settings:normal;-moz-tab-size:4;tab-size:4;-webkit-tap-highlight-color:transparent}body{line-height:inherit;margin:0}h1{font-size:inherit;font-weight:inherit}h1,p{margin:0}*,:after,:before{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 transparent;--un-ring-shadow:0 0 transparent;--un-shadow-inset: ;--un-shadow:0 0 transparent;--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgba(147,197,253,.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: }.pointer-events-none{pointer-events:none}.fixed{position:fixed}.left-0{left:0}.right-0{right:0}.z-10{z-index:10}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.h-auto{height:auto}.min-h-screen{min-height:100vh}.flex{display:flex}.flex-1{flex:1 1 0%}.flex-col{flex-direction:column}.overflow-y-auto{overflow-y:auto}.rounded-t-md{border-top-left-radius:.375rem;border-top-right-radius:.375rem}.bg-black\\/5{background-color:#0000000d}.bg-white{--un-bg-opacity:1;background-color:rgb(255 255 255/var(--un-bg-opacity))}.p-8{padding:2rem}.px-10{padding-left:2.5rem;padding-right:2.5rem}.pt-14{padding-top:3.5rem}.text-6xl{font-size:3.75rem;line-height:1}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-black{--un-text-opacity:1;color:rgb(0 0 0/var(--un-text-opacity))}.font-light{font-weight:300}.font-medium{font-weight:500}.leading-tight{line-height:1.25}.font-sans{font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}@media (prefers-color-scheme:dark){.dark\\:bg-black{--un-bg-opacity:1;background-color:rgb(0 0 0/var(--un-bg-opacity))}.dark\\:bg-white\\/10{background-color:#ffffff1a}.dark\\:text-white{--un-text-opacity:1;color:rgb(255 255 255/var(--un-text-opacity))}}@media (min-width:640px){.sm\\:text-2xl{font-size:1.5rem;line-height:2rem}.sm\\:text-8xl{font-size:6rem;line-height:1}}</style><script>!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))r(e);new MutationObserver((e=>{for(const o of e)if("childList"===o.type)for(const e of o.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&r(e)})).observe(document,{childList:!0,subtree:!0})}function r(e){if(e.ep)return;e.ep=!0;const r=function(e){const r={};return e.integrity&&(r.integrity=e.integrity),e.referrerPolicy&&(r.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?r.credentials="include":"anonymous"===e.crossOrigin?r.credentials="omit":r.credentials="same-origin",r}(e);fetch(e.href,r)}}();<\/script></head><body class="antialiased bg-white dark:bg-black dark:text-white flex flex-col font-sans min-h-screen pt-14 px-10 text-black"><div class="fixed left-0 pointer-events-none right-0 spotlight"></div><h1 class="font-medium mb-6 sm:text-8xl text-6xl">` + escapeHtml(messages.statusCode) + '</h1><p class="font-light leading-tight mb-8 sm:text-2xl text-xl">' + escapeHtml(messages.description) + '</p><div class="bg-black/5 bg-white dark:bg-white/10 flex-1 h-auto overflow-y-auto rounded-t-md"><div class="font-light leading-tight p-8 text-xl z-10">' + escapeHtml(messages.stack) + "</div></div></body></html>";
};

const errorDev = /*#__PURE__*/Object.freeze({
  __proto__: null,
  template: template$1
});

const parser = new XMLParser({
  isArray: (tagName) => ["url", "image", "video", "link", "tag", "price"].includes(tagName),
  removeNSPrefix: true,
  parseAttributeValue: false,
  ignoreAttributes: false,
  attributeNamePrefix: "",
  trimValues: true
});
function isValidString(value) {
  return typeof value === "string" && value.trim().length > 0;
}
function parseNumber(value) {
  if (typeof value === "number") return value;
  if (typeof value === "string" && value.trim()) {
    const num = Number.parseFloat(value.trim());
    return Number.isNaN(num) ? void 0 : num;
  }
  return void 0;
}
function parseInteger(value) {
  if (typeof value === "number") return Math.floor(value);
  if (typeof value === "string" && value.trim()) {
    const num = Number.parseInt(value.trim(), 10);
    return Number.isNaN(num) ? void 0 : num;
  }
  return void 0;
}
function extractUrlFromParsedElement(urlElement, warnings) {
  if (!isValidString(urlElement.loc)) {
    warnings.push({
      type: "validation",
      message: "URL entry missing required loc element",
      context: { url: String(urlElement.loc || "undefined") }
    });
    return null;
  }
  const urlObj = { loc: urlElement.loc };
  if (isValidString(urlElement.lastmod)) {
    urlObj.lastmod = urlElement.lastmod;
  }
  if (isValidString(urlElement.changefreq)) {
    const validFreqs = ["always", "hourly", "daily", "weekly", "monthly", "yearly", "never"];
    if (validFreqs.includes(urlElement.changefreq)) {
      urlObj.changefreq = urlElement.changefreq;
    } else {
      warnings.push({
        type: "validation",
        message: "Invalid changefreq value",
        context: { url: urlElement.loc, field: "changefreq", value: urlElement.changefreq }
      });
    }
  }
  const priority = parseNumber(urlElement.priority);
  if (priority !== void 0 && !Number.isNaN(priority)) {
    if (priority < 0 || priority > 1) {
      warnings.push({
        type: "validation",
        message: "Priority value should be between 0.0 and 1.0, clamping to valid range",
        context: { url: urlElement.loc, field: "priority", value: priority }
      });
    }
    urlObj.priority = Math.max(0, Math.min(1, priority));
  } else if (urlElement.priority !== void 0) {
    warnings.push({
      type: "validation",
      message: "Invalid priority value",
      context: { url: urlElement.loc, field: "priority", value: urlElement.priority }
    });
  }
  if (urlElement.image) {
    const images = Array.isArray(urlElement.image) ? urlElement.image : [urlElement.image];
    const validImages = images.map((img) => {
      if (isValidString(img.loc)) {
        return { loc: img.loc };
      } else {
        warnings.push({
          type: "validation",
          message: "Image missing required loc element",
          context: { url: urlElement.loc, field: "image.loc" }
        });
        return null;
      }
    }).filter((img) => img !== null);
    if (validImages.length > 0) {
      urlObj.images = validImages;
    }
  }
  if (urlElement.video) {
    const videos = Array.isArray(urlElement.video) ? urlElement.video : [urlElement.video];
    const validVideos = videos.map((video) => {
      const missingFields = [];
      if (!isValidString(video.title)) missingFields.push("title");
      if (!isValidString(video.thumbnail_loc)) missingFields.push("thumbnail_loc");
      if (!isValidString(video.description)) missingFields.push("description");
      if (!isValidString(video.content_loc)) missingFields.push("content_loc");
      if (missingFields.length > 0) {
        warnings.push({
          type: "validation",
          message: `Video missing required fields: ${missingFields.join(", ")}`,
          context: { url: urlElement.loc, field: "video" }
        });
        return null;
      }
      const videoObj = {
        title: video.title,
        thumbnail_loc: video.thumbnail_loc,
        description: video.description,
        content_loc: video.content_loc
      };
      if (isValidString(video.player_loc)) {
        videoObj.player_loc = video.player_loc;
      }
      const duration = parseInteger(video.duration);
      if (duration !== void 0) {
        videoObj.duration = duration;
      } else if (video.duration !== void 0) {
        warnings.push({
          type: "validation",
          message: "Invalid video duration value",
          context: { url: urlElement.loc, field: "video.duration", value: video.duration }
        });
      }
      if (isValidString(video.expiration_date)) {
        videoObj.expiration_date = video.expiration_date;
      }
      const rating = parseNumber(video.rating);
      if (rating !== void 0) {
        if (rating < 0 || rating > 5) {
          warnings.push({
            type: "validation",
            message: "Video rating should be between 0.0 and 5.0",
            context: { url: urlElement.loc, field: "video.rating", value: rating }
          });
        }
        videoObj.rating = rating;
      } else if (video.rating !== void 0) {
        warnings.push({
          type: "validation",
          message: "Invalid video rating value",
          context: { url: urlElement.loc, field: "video.rating", value: video.rating }
        });
      }
      const viewCount = parseInteger(video.view_count);
      if (viewCount !== void 0) {
        videoObj.view_count = viewCount;
      } else if (video.view_count !== void 0) {
        warnings.push({
          type: "validation",
          message: "Invalid video view_count value",
          context: { url: urlElement.loc, field: "video.view_count", value: video.view_count }
        });
      }
      if (isValidString(video.publication_date)) {
        videoObj.publication_date = video.publication_date;
      }
      if (isValidString(video.family_friendly)) {
        const validValues = ["yes", "no"];
        if (validValues.includes(video.family_friendly)) {
          videoObj.family_friendly = video.family_friendly;
        } else {
          warnings.push({
            type: "validation",
            message: 'Invalid video family_friendly value, should be "yes" or "no"',
            context: { url: urlElement.loc, field: "video.family_friendly", value: video.family_friendly }
          });
        }
      }
      if (isValidString(video.requires_subscription)) {
        const validValues = ["yes", "no"];
        if (validValues.includes(video.requires_subscription)) {
          videoObj.requires_subscription = video.requires_subscription;
        } else {
          warnings.push({
            type: "validation",
            message: 'Invalid video requires_subscription value, should be "yes" or "no"',
            context: { url: urlElement.loc, field: "video.requires_subscription", value: video.requires_subscription }
          });
        }
      }
      if (isValidString(video.live)) {
        const validValues = ["yes", "no"];
        if (validValues.includes(video.live)) {
          videoObj.live = video.live;
        } else {
          warnings.push({
            type: "validation",
            message: 'Invalid video live value, should be "yes" or "no"',
            context: { url: urlElement.loc, field: "video.live", value: video.live }
          });
        }
      }
      if (video.restriction && typeof video.restriction === "object") {
        const restriction = video.restriction;
        if (isValidString(restriction.relationship) && isValidString(restriction["#text"])) {
          const validRelationships = ["allow", "deny"];
          if (validRelationships.includes(restriction.relationship)) {
            videoObj.restriction = {
              relationship: restriction.relationship,
              restriction: restriction["#text"]
            };
          } else {
            warnings.push({
              type: "validation",
              message: 'Invalid video restriction relationship, should be "allow" or "deny"',
              context: { url: urlElement.loc, field: "video.restriction.relationship", value: restriction.relationship }
            });
          }
        }
      }
      if (video.platform && typeof video.platform === "object") {
        const platform = video.platform;
        if (isValidString(platform.relationship) && isValidString(platform["#text"])) {
          const validRelationships = ["allow", "deny"];
          if (validRelationships.includes(platform.relationship)) {
            videoObj.platform = {
              relationship: platform.relationship,
              platform: platform["#text"]
            };
          } else {
            warnings.push({
              type: "validation",
              message: 'Invalid video platform relationship, should be "allow" or "deny"',
              context: { url: urlElement.loc, field: "video.platform.relationship", value: platform.relationship }
            });
          }
        }
      }
      if (video.price) {
        const prices = Array.isArray(video.price) ? video.price : [video.price];
        const validPrices = prices.map((price) => {
          const priceValue = price["#text"];
          if (priceValue == null || typeof priceValue !== "string" && typeof priceValue !== "number") {
            warnings.push({
              type: "validation",
              message: "Video price missing value",
              context: { url: urlElement.loc, field: "video.price" }
            });
            return null;
          }
          const validTypes = ["rent", "purchase", "package", "subscription"];
          if (price.type && !validTypes.includes(price.type)) {
            warnings.push({
              type: "validation",
              message: `Invalid video price type "${price.type}", should be one of: ${validTypes.join(", ")}`,
              context: { url: urlElement.loc, field: "video.price.type", value: price.type }
            });
          }
          return {
            price: String(priceValue),
            currency: price.currency,
            type: price.type
          };
        }).filter((p) => p !== null);
        if (validPrices.length > 0) {
          videoObj.price = validPrices;
        }
      }
      if (video.uploader && typeof video.uploader === "object") {
        const uploader = video.uploader;
        if (isValidString(uploader.info) && isValidString(uploader["#text"])) {
          videoObj.uploader = {
            uploader: uploader["#text"],
            info: uploader.info
          };
        } else {
          warnings.push({
            type: "validation",
            message: "Video uploader missing required info or name",
            context: { url: urlElement.loc, field: "video.uploader" }
          });
        }
      }
      if (video.tag) {
        const tags = Array.isArray(video.tag) ? video.tag : [video.tag];
        const validTags = tags.filter(isValidString);
        if (validTags.length > 0) {
          videoObj.tag = validTags;
        }
      }
      return videoObj;
    }).filter((video) => video !== null);
    if (validVideos.length > 0) {
      urlObj.videos = validVideos;
    }
  }
  if (urlElement.link) {
    const links = Array.isArray(urlElement.link) ? urlElement.link : [urlElement.link];
    const alternatives = links.map((link) => {
      if (link.rel === "alternate" && isValidString(link.hreflang) && isValidString(link.href)) {
        return {
          hreflang: link.hreflang,
          href: link.href
        };
      } else {
        warnings.push({
          type: "validation",
          message: 'Alternative link missing required rel="alternate", hreflang, or href',
          context: { url: urlElement.loc, field: "link" }
        });
        return null;
      }
    }).filter((alt) => alt !== null);
    if (alternatives.length > 0) {
      urlObj.alternatives = alternatives;
    }
  }
  if (urlElement.news && typeof urlElement.news === "object") {
    const news = urlElement.news;
    if (isValidString(news.title) && isValidString(news.publication_date) && news.publication && isValidString(news.publication.name) && isValidString(news.publication.language)) {
      urlObj.news = {
        title: news.title,
        publication_date: news.publication_date,
        publication: {
          name: news.publication.name,
          language: news.publication.language
        }
      };
    } else {
      warnings.push({
        type: "validation",
        message: "News entry missing required fields (title, publication_date, publication.name, publication.language)",
        context: { url: urlElement.loc, field: "news" }
      });
    }
  }
  const filteredUrlObj = Object.fromEntries(
    Object.entries(urlObj).filter(
      ([_, value]) => value != null && (!Array.isArray(value) || value.length > 0)
    )
  );
  return filteredUrlObj;
}
function parseSitemapXml(xml) {
  const warnings = [];
  if (!xml) {
    throw new Error("Empty XML input provided");
  }
  try {
    const parsed = parser.parse(xml);
    if (!parsed?.urlset) {
      throw new Error("XML does not contain a valid urlset element");
    }
    if (!parsed.urlset.url) {
      throw new Error("Sitemap contains no URL entries");
    }
    const urls = Array.isArray(parsed.urlset.url) ? parsed.urlset.url : [parsed.urlset.url];
    const validUrls = urls.map((url) => extractUrlFromParsedElement(url, warnings)).filter((url) => url !== null);
    if (validUrls.length === 0 && urls.length > 0) {
      warnings.push({
        type: "validation",
        message: "No valid URLs found in sitemap after validation"
      });
    }
    return { urls: validUrls, warnings };
  } catch (error) {
    if (error instanceof Error && (error.message === "Empty XML input provided" || error.message === "XML does not contain a valid urlset element" || error.message === "Sitemap contains no URL entries")) {
      throw error;
    }
    throw new Error(`Failed to parse XML: ${error instanceof Error ? error.message : String(error)}`);
  }
}

const utils = /*#__PURE__*/Object.freeze({
  __proto__: null,
  parseSitemapXml: parseSitemapXml
});

const sources$1 = [
    {
        "sourceType": "user",
        "fetch": "/api/sitemap/sewa-mobil"
    },
    {
        "sourceType": "user",
        "fetch": "/api/sitemap/paket-wisata"
    },
    {
        "sourceType": "user",
        "fetch": "/api/sitemap/paket-honeymoon"
    },
    {
        "sourceType": "user",
        "fetch": "/api/sitemap/paket-gathering"
    },
    {
        "context": {
            "name": "sitemap:urls",
            "description": "Set with the `sitemap.urls` config."
        },
        "urls": [],
        "sourceType": "user"
    },
    {
        "context": {
            "name": "@nuxt/content@v3:urls",
            "description": "Generated from your markdown files.",
            "tips": [
                "Parsing the following collections: "
            ]
        },
        "fetch": "/__sitemap__/nuxt-content-urls.json",
        "sourceType": "app"
    },
    {
        "context": {
            "name": "nuxt:pages",
            "description": "Generated from your static page files.",
            "tips": [
                "Can be disabled with `{ excludeAppSources: ['nuxt:pages'] }`."
            ]
        },
        "urls": [
            {
                "loc": "/"
            },
            {
                "loc": "/contact"
            },
            {
                "loc": "/blog"
            },
            {
                "loc": "/tags"
            },
            {
                "loc": "/tentang-kami"
            },
            {
                "loc": "/pelanggan-kami"
            },
            {
                "loc": "/privacy-policy"
            },
            {
                "loc": "/authors/roofel-team"
            },
            {
                "loc": "/legalitas-perusahaan"
            },
            {
                "loc": "/syarat-dan-ketentuan"
            },
            {
                "loc": "/paket-gathering"
            },
            {
                "loc": "/paket-honeymoon"
            },
            {
                "loc": "/sewa-mobil-jogja"
            },
            {
                "loc": "/paket-wisata-jogja"
            },
            {
                "loc": "/sewa-mobil-xpander/"
            },
            {
                "loc": "/sewa-mobil-all-new-innova/"
            },
            {
                "loc": "/sewa-mobil-hyundai-h1/"
            },
            {
                "loc": "/sewa-mobil-all-new-avanza/"
            },
            {
                "loc": "/sewa-mobil-hyundai-h1-royale/"
            },
            {
                "loc": "/sewa-mobil-all-new-innova-zenix/"
            },
            {
                "loc": "/sewa-mobil-elf-short-11-seats/"
            },
            {
                "loc": "/sewa-mobil-fortuner-vrz/"
            },
            {
                "loc": "/sewa-mobil-new-alphard-transformer/"
            },
            {
                "loc": "/sewa-mobil-hiace-commuter-14-seats/"
            },
            {
                "loc": "/sewa-hiace-premio-12-14-seats/"
            },
            {
                "loc": "/sewa-bigbus-40-50-seats/"
            },
            {
                "loc": "/sewa-mobil-grand-avanza/"
            },
            {
                "loc": "/sewa-hiace-premio-luxury/"
            },
            {
                "loc": "/sewa-medium-bus-31-33-seats/"
            },
            {
                "loc": "/paket-wisata-jogja-dan-hotel-brahmasta/"
            },
            {
                "loc": "/paket-wisata-jogja-dan-hotel-nagapasa/"
            },
            {
                "loc": "/paket-wisata-jogja-hotel-andanu/"
            },
            {
                "loc": "/paket-wisata-jogja-dan-hotel-kalimasada/"
            },
            {
                "loc": "/paket-wisata-jogja-dan-hotel-gandiwa/"
            },
            {
                "loc": "/paket-wisata-jogja-dan-hotel-rujapala/"
            },
            {
                "loc": "/paket-wisata-jogja-dan-hotel-pancanaka/"
            },
            {
                "loc": "/paket-wisata-jogja-dan-hotel-nenggala/"
            },
            {
                "loc": "/paket-wisata-jogja-dan-hotel-sudarsana/"
            },
            {
                "loc": "/paket-wisata-jogja-hotel-nagaloka/"
            },
            {
                "loc": "/paket-wisata-jogja-hotel-sempati/"
            },
            {
                "loc": "/paket-wisata-jogja-hotel-taksaka/"
            },
            {
                "loc": "/paket-wisata-jogja-hotel-bendana/"
            },
            {
                "loc": "/paket-wisata-jogja-hotel-antaboga/"
            },
            {
                "loc": "/paket-wisata-jogja-hotel-jatayu/"
            },
            {
                "loc": "/paket-wisata-jogja-hotel-bisma/"
            },
            {
                "loc": "/paket-wisata-jogja-hotel-4d-anoman/"
            },
            {
                "loc": "/paket-wisata-jogja-hotel-4d-arjuna/"
            },
            {
                "loc": "/paket-wisata-jogja-bisma/"
            },
            {
                "loc": "/paket-wisata-jogja-4d-anoman/"
            },
            {
                "loc": "/paket-wisata-jogja-4d-arjuna/"
            },
            {
                "loc": "/paket-wisata-jogja-nagaloka/"
            },
            {
                "loc": "/paket-wisata-jogja-abimanyu/"
            },
            {
                "loc": "/paket-wisata-jogja-andanu/"
            },
            {
                "loc": "/paket-wisata-jogja-anoman/"
            },
            {
                "loc": "/paket-wisata-jogja-antaboga/"
            },
            {
                "loc": "/paket-wisata-jogja-antasena/"
            },
            {
                "loc": "/paket-wisata-jogja-arjuna/"
            },
            {
                "loc": "/paket-wisata-jogja-bendana/"
            },
            {
                "loc": "/paket-wisata-jogja-brahmasta/"
            },
            {
                "loc": "/paket-wisata-jogja-bugisan/"
            },
            {
                "loc": "/paket-wisata-jogja-gandiwa/"
            },
            {
                "loc": "/paket-wisata-jogja-gatotkaca/"
            },
            {
                "loc": "/paket-wisata-jogja-jagakarya/"
            },
            {
                "loc": "/paket-wisata-jogja-jatayu/"
            },
            {
                "loc": "/paket-wisata-jogja-kalimasada/"
            },
            {
                "loc": "/paket-wisata-jogja-kresna/"
            },
            {
                "loc": "/paket-wisata-jogja-mantrijero/"
            },
            {
                "loc": "/paket-wisata-jogja-nagapasa/"
            },
            {
                "loc": "/paket-wisata-jogja-nenggala/"
            },
            {
                "loc": "/paket-wisata-jogja-pancanaka/"
            },
            {
                "loc": "/paket-wisata-jogja-rujapala/"
            },
            {
                "loc": "/paket-wisata-jogja-sempati/"
            },
            {
                "loc": "/paket-wisata-jogja-sudarsana/"
            },
            {
                "loc": "/paket-wisata-jogja-taksaka/"
            },
            {
                "loc": "/paket-wisata-tanpa-hotel/"
            },
            {
                "loc": "/paket-wisata-hotel/"
            },
            {
                "loc": "/paket-gathering-jogja-ananta/"
            },
            {
                "loc": "/paket-gathering-jogja-bhagawanta/"
            },
            {
                "loc": "/paket-gathering-jogja-jayantaka/"
            },
            {
                "loc": "/paket-gathering-jogja-padmana/"
            },
            {
                "loc": "/paket-honeymoon-ramayana/"
            },
            {
                "loc": "/paket-honeymoon-mahabarata/"
            },
            {
                "loc": "/paket-honeymoon-anjani/"
            },
            {
                "loc": "/paket-honeymoon-srikandi/"
            }
        ],
        "sourceType": "app"
    },
    {
        "context": {
            "name": "nuxt:prerender",
            "description": "Generated at build time when prerendering.",
            "tips": [
                "Can be disabled with `{ excludeAppSources: ['nuxt:prerender'] }`."
            ]
        },
        "urls": [
            "/",
            "/blog/",
            "/tags/",
            "/authors/",
            "/paket-wisata-jogja/",
            "/sewa-mobil-jogja/",
            "/paket-honeymoon/",
            "/paket-gathering/",
            "/404/"
        ],
        "sourceType": "app"
    }
];

const globalSources = /*#__PURE__*/Object.freeze({
  __proto__: null,
  sources: sources$1
});

const sources = {};

const childSources = /*#__PURE__*/Object.freeze({
  __proto__: null,
  sources: sources
});

const template = "";

const _virtual__spaTemplate = /*#__PURE__*/Object.freeze({
  __proto__: null,
  template: template
});

const styles = {};

const styles$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: styles
});

const _slug__get = defineEventHandler(async (event) => {
  try {
    const params2 = getRouterParams(event);
    const slug = params2 == null ? void 0 : params2.slug;
    if (!slug || typeof slug !== "string") {
      throw createError({
        statusCode: 400,
        statusMessage: "Invalid slug parameter"
      });
    }
    const data = await useStorage().getItem("server:data/destinations.json");
    if (!data || !data.destinations || !Array.isArray(data.destinations)) {
      console.error("No destinations data found in storage");
      throw createError({
        statusCode: 404,
        statusMessage: "Destinations data not found"
      });
    }
    const destination = data.destinations.find((d) => d.slug === slug);
    if (!destination) {
      console.error(`Destination with slug "${slug}" not found`);
      throw createError({
        statusCode: 404,
        statusMessage: "Destination not found"
      });
    }
    return destination;
  } catch (error) {
    console.error(`Error in destinations/${params == null ? void 0 : params.slug}.get:`, error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "Internal server error"
    });
  }
});

const _slug__get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _slug__get
});

const index_get = defineEventHandler(async (event) => {
  try {
    const destinations = await useStorage().getItem("server:data/destinations.json");
    console.log("Destinations data retrieved:", destinations);
    return destinations || { destinations: [] };
  } catch (error) {
    console.error("Error fetching destinations:", error);
    return { destinations: [] };
  }
});

const index_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: index_get
});

const paketWisataData = [
	{
		id: "pw-001",
		title: "Paket Wisata ABIMANYU 1 hari",
		slug: "abimanyu",
		description: "",
		price: "Rp 344,000 – Rp 593,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 593,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 377,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 344,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/tumpeng-Menoreh.webp",
		images: [
			"https://liburanjogja.b-cdn.net/obelix-Village.webp",
			"https://liburanjogja.b-cdn.net/Bhumi-Merapi.webp",
			"https://liburanjogja.b-cdn.net/studio-alam-Gamplong.webp",
			"https://liburanjogja.b-cdn.net/tumpeng-Menoreh.webp"
		],
		location: "Yogyakarta",
		duration: "1-hari",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "One Day Tour Abimayu",
				activities: [
					"Tumpeng Menoreh merupakan destinasi wisata yang terus menjadi terkenal di Kulon Progo, Yogyakarta. Terletak di perbukitan Menoreh, tempat ini menawarkan panorama alam alam yang luar biasa serta pengalaman wisata yang unik",
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia.",
					"Bhumi Merapi",
					"obelix Village Destinasi ini menjadi salah satu tempat wisata di Jogja yang cocok untuk keluarga karena memiliki banyak wahana bagi semua usia. Obelix Village menggabungkan konsep little zoo, mini farm, flower garden, river deck, dan resto dalam satu lokasi."
				]
			}
		}
	},
	{
		id: "pw-002",
		title: "Paket Wisata ANOMAN 1 hari",
		slug: "anoman",
		description: "",
		price: "Rp 261,000 – Rp 500,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 500,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 285,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 261,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/Pantai-Mesra.webp",
		images: [
			"https://liburanjogja.b-cdn.net/tempat-wisata-jalan-malioboro-yogyakarta-scaled.webp",
			"https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp",
			"https://liburanjogja.b-cdn.net/Pantai-Mesra.webp",
			"https://liburanjogja.b-cdn.net/Pusat-Oleh-oleh.webp"
		],
		location: "Yogyakarta",
		duration: "1-hari",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "One Day Tour ANOMAN",
				activities: [
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Pantai Mesra. Pantai Mesra merupakan salah satu pantai di Gunung Kidul yang memiliki hamparan rumput hijau yang tumbuh subur. Suasana pantai yang romantis sehingga pantai ini dinamakan pantai Mesra.",
					"Malioboro merupakan salah satu destinasi wisata sangat ikonik di Yogyakarta, Indonesia. Terletak di pusat kota, jalur ini membentang selama kurang lebih 2, 5 km dari Stasiun Tugu sampai ke titik Nol Km Yogyakarta",
					"Pusat Oleh-oleh."
				]
			}
		}
	},
	{
		id: "pw-003",
		title: "Paket Wisata ANTASENA 1 hari",
		slug: "antasena",
		description: "",
		price: "Rp 278,000 – Rp 518,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 518,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 302,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 278,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/Teras-Kaca.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Seribu-Batu-Songgo-Langit.webp",
			"https://liburanjogja.b-cdn.net/Air-Terjun-Sri-Gethuk.webp",
			"https://liburanjogja.b-cdn.net/Teras-Kaca.webp",
			"https://liburanjogja.b-cdn.net/Pantai-Mesra.webp"
		],
		location: "Yogyakarta",
		duration: "1-hari",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "One Day Tour ANTASENA",
				activities: [
					"Air Terjun Sri Gethuk. Air Terjun Sri Gethuk terletak di tepi Sungai Oyo yang mengalir deras di ketinggian 50 meter. Air Terjun ini dikenal sebagai air terjun yang tidak pernah kering.",
					"Seribu Batu Songgo Langit. Seribu Batu Songgo Langit menyuguhkan pemandangan alam hutan pinus yang asri dan sejuk. Sebelum dijadikan sebagai tempay wisata, di lokasi ini terdapat ribuan batu alam dan deretan hutan pinus..",
					"Pantai Mesra. Pantai Mesra merupakan salah satu pantai di Gunung Kidul yang memiliki hamparan rumput hijau yang tumbuh subur. Suasana pantai yang romantis sehingga pantai ini dinamakan pantai Mesra.",
					"Teras Kaca. Teras Kaca terletak di Gunung Kidul, Yogyakarta. Teras Kaca mengajak wisatawan untuk berdiri di atas teras kaca dengan view pemandangan laut yang begitu memukau."
				]
			}
		}
	},
	{
		id: "pw-004",
		title: "Paket Wisata ARJUNA 1 hari",
		slug: "arjuna",
		description: "",
		price: "Rp 443,000 – Rp 759,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 759,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 501,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 759,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/Lava-Tour.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Bhumi-Merapi.webp",
			"https://liburanjogja.b-cdn.net/Obelix-Hills.webp",
			"https://liburanjogja.b-cdn.net/obelix-Village.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp"
		],
		location: "Yogyakarta",
		duration: "1-hari",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "One Day Tour ARJUNA",
				activities: [
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Bhumi Merapi.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam.",
					"obelix Village Destinasi ini menjadi salah satu tempat wisata di Jogja yang cocok untuk keluarga karena memiliki banyak wahana bagi semua usia. Obelix Village menggabungkan konsep little zoo, mini farm, flower garden, river deck, dan resto dalam satu lokasi.."
				]
			}
		}
	},
	{
		id: "pw-005",
		title: "Paket Wisata BUGISAN 1 hari",
		slug: "bugisan",
		description: "",
		price: "Rp 301,000 – Rp 541,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 541,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 325,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 301,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Tebing-Breksi.webp",
			"https://liburanjogja.b-cdn.net/Obelix-Hills.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp",
			"https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp"
		],
		location: "Yogyakarta",
		duration: "1-hari",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "One Day Tour BUGISAN",
				activities: [
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam.",
					"Pusat Oleh-oleh"
				]
			}
		}
	},
	{
		id: "pw-006",
		title: "Paket Wisata GATOTKACA 1 hari",
		slug: "gatotkaca",
		description: "",
		price: "Rp 307,000 – Rp 547,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 547,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 331,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 307,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/Air-Terjun-Sri-Gethuk.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Heha-Ocean.webp",
			"https://liburanjogja.b-cdn.net/pantai-Sadranan.webp",
			"https://liburanjogja.b-cdn.net/Pusat-Oleh-oleh.webp",
			"https://liburanjogja.b-cdn.net/tempat-wisata-jalan-malioboro-yogyakarta-scaled.webp"
		],
		location: "Yogyakarta",
		duration: "1-hari",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "One Day Tour GATOTKACA",
				activities: [
					"Air Terjun Sri Gethuk. Air Terjun Sri Gethuk terletak di tepi Sungai Oyo yang mengalir deras di ketinggian 50 meter. Air Terjun ini dikenal sebagai air terjun yang tidak pernah kering.",
					"Heha Ocean View. Heha ocean View terletak di atas tebing dan wisatawan akan disuguhkan dengan pemandangan laut lepas dengan lebih leluasa. Selain menyuguhkan pemandangan laut yang canti, Heha Ocean View juga dilengkapi dengan spot-spot foto yang instagramable.",
					"Pantai Sadranan",
					"Malioboro merupakan salah satu destinasi wisata sangat ikonik di Yogyakarta, Indonesia. Terletak di pusat kota, jalur ini membentang selama kurang lebih 2, 5 km dari Stasiun Tugu sampai ke titik Nol Km Yogyakarta.",
					"Pusat Oleh-oleh"
				]
			}
		}
	},
	{
		id: "pw-007",
		title: "Paket Wisata JAGAKARYA 1 hari",
		slug: "jagakarya",
		description: "",
		price: "Rp 296,000 – Rp 535,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 535,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 320,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 296,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/Teras-Kaca.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Bukit-Panguk.webp",
			"https://liburanjogja.b-cdn.net/Bukit-Paralayang.webp",
			"https://liburanjogja.b-cdn.net/hutan-pinus-mangunan.webp",
			"https://liburanjogja.b-cdn.net/Rumah-Hobbit-Mangunan.webp"
		],
		location: "Yogyakarta",
		duration: "1-hari",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "One Day Tour JAGAKARYA",
				activities: [
					"Bukit Panguk. Yang menjadi daya tarik Bukit Panguk yaitu pemandangan alamnya yang dilihat dari ketinggian. Wisatawan dapat melihat pemandangan alam dari gardu yang terbuat dari kayu dan bamboo yang menjorok kek bibir tebing. Selain perbukitan, dari gardu ini wisatawan juga dapat melihat Sungai Oya yang membelah bukit yang terhampar hijau.",
					"Rumah Hobbit Mangunan. Disebut juga dengan Rumah Hobbit Batu Songgo Langit. Rumah Hobbit Mangunan terletak di Mangunan, Dlingo tepatnya di daerah Bantul. Design interior atau eksterior dari rumah Hobbit terkesan sangat unik, lucu, sederhana, dan alamiah. Wisatawan akan diajak mengelilingi layaknya masuk ke dunia dongeng..",
					"Hutan Pinus Mangunan. Daya Tarik Hutan Pinus Mangunan adalah banyaknya pohon pinus yang tinggi dan rindang, sehingga membuat suasanya menjadi sejuk dan nyaman. Udara yang bersih dan segar cocok untuk wisatawan yang penat akan bising dan panasnya suasana kota.",
					"Teras Kaca. Teras Kaca terletak di Gunung Kidul, Yogyakarta. Teras Kaca mengajak wisatawan untuk berdiri di atas teras kaca dengan view pemandangan laut yang begitu memukau.",
					"Bukit Paralayang. merupakan salah satu destinasi wisata alam yang menawarkan keelokan panorama dan pengalaman unik untuk para wisatawan. Terletak di kawasan Parangtritis, Bantul, Bukit Paralayang menyuguhkan panorama alam yang menawan dari ketinggian, dan peluang buat berupaya berolahraga paralayang yang mendebarkan."
				]
			}
		}
	},
	{
		id: "pw-008",
		title: "Paket Wisata KRESNA 1 hari",
		slug: "kresna",
		description: "",
		price: "Rp 278,000 – Rp 518,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 518,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 302,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 278,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/Candi_Ijo-jogja.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Obelix-Hills.webp",
			"https://liburanjogja.b-cdn.net/Candi_Ijo-jogja.webp",
			"https://liburanjogja.b-cdn.net/Pusat-Oleh-oleh.webp",
			"https://liburanjogja.b-cdn.net/tempat-wisata-jalan-malioboro-yogyakarta-scaled.webp"
		],
		location: "Yogyakarta",
		duration: "1-hari",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "One Day Tour KRESNA",
				activities: [
					"Umbul Ponggok",
					"Candi Ijo. Candi Ijo merupakan candi yang letaknya paling tinggi di Yogyakarta yang menyuguhkan alam dan budayanya.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam.",
					"Malioboro merupakan salah satu destinasi wisata sangat ikonik di Yogyakarta, Indonesia. Terletak di pusat kota, jalur ini membentang selama kurang lebih 2, 5 km dari Stasiun Tugu sampai ke titik Nol Km Yogyakarta.",
					"Pusat Oleh-oleh"
				]
			}
		}
	},
	{
		id: "pw-009",
		title: "Paket Wisata MANTRIJERO 1 hari",
		slug: "mantrijero",
		description: "",
		price: "Rp 472,000 – Rp 788,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 788,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 529,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 472,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
		images: [
			"https://liburanjogja.b-cdn.net/candi-borobudur-7.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/Tebing-Breksi.webp",
			"https://liburanjogja.b-cdn.net/Candi_Ijo-jogja.webp"
		],
		location: "Yogyakarta",
		duration: "1-hari",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "One Day Tour MANTRIJERO",
				activities: [
					"Puthuk Setumbu. Merupakan bukit yang menjadi salah satu spot terbaik untuk menyaksikan matahari terbit dengan view Gunung Merapi dan Merbabu, yang lebih menariknya adalah wisatawan juga bisa melihat megahnya Candi Borobudur di pagi hari yang diselimuti kabut.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Candi Ijo. Candi Ijo merupakan candi yang letaknya paling tinggi di Yogyakarta yang menyuguhkan alam dan budayanya."
				]
			}
		}
	},
	{
		id: "pw-011",
		title: "Paket Wisata SUDARSANA 2Hari 1Malam",
		slug: "sudarsana",
		description: "",
		price: "Rp 744,000 – Rp 1,300,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 1,300,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 897,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 744,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/keraton-jogja.webp",
		images: [
			"https://liburanjogja.b-cdn.net/keraton-jogja.webp",
			"https://liburanjogja.b-cdn.net/gambar-taman-sari-destinasi-liburan-budaya-di-jogja.webp",
			"https://liburanjogja.b-cdn.net/Heha-Ocean.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp"
		],
		location: "Yogyakarta",
		duration: "2-hari-1-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Pantai Parangtritis. Pantai Parangtritis merupakan pantai paling ikonik di Yogyakarta. Tidak hanya sebagai tempat untuk menikmati keindahan alam, pantai Parangtritis juga memiliki nilai sejarah dan kebudayaan yang kuat. Wisatawan dapat menikmati kegiatan yang beragam, mulai dari menaiki delman atau andong tradisional, berkuda di sepanjang pantai, mencoba makanan khas Yogyakarta di warung-warung pinggir pantai.",
					"Heha Ocean View. Heha ocean View terletak di atas tebing dan wisatawan akan disuguhkan dengan pemandangan laut lepas dengan lebih leluasa. Selain menyuguhkan pemandangan laut yang canti, Heha Ocean View juga dilengkapi dengan spot-spot foto yang instagramable."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam."
				]
			}
		}
	},
	{
		id: "pw-012",
		title: "Paket Wisata BRAHMASTA 2Hari 1Malam",
		slug: "brahmasta",
		description: "",
		price: "Rp 625,000 – Rp 1,104,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 1,104,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 673,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 625,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/Air-Terjun-Sri-Gethuk.webp",
			"https://liburanjogja.b-cdn.net/Pantai-Mesra.webp",
			"https://liburanjogja.b-cdn.net/hutan-pinus-mangunan.webp"
		],
		location: "Yogyakarta",
		duration: "2-hari-1-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Puthuk Setumbu. Merupakan bukit yang menjadi salah satu spot terbaik untuk menyaksikan matahari terbit dengan view Gunung Merapi dan Merbabu, yang lebih menariknya adalah wisatawan juga bisa melihat megahnya Candi Borobudur di pagi hari yang diselimuti kabut.",
					"Gereja Ayam. Gereja Ayam merupakan wisata religi dengan pemandangan alam yang Indah. Wisata religi yang berada di bukit Rhema ini dibangun di daerah Borobudur, Magelang. Selain keindahan alamnya yang memukau, arsitektur Gereja Ayam juga menarik, dimana bentuknya menyerupai kepala burung Merpati yang memiliki mahkota diatasnya.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Merapi Park. Merapi Park dilengkapi banyak spot foto yang menarik dan instagramable. Terdapat replica ikon terkenal dunia. Inilah yang menyebabkan tempat ini juga dikenal dengan sebutan The World Landmark. Merapi park juga dilengkapi atraksi dan wahana untuk anak-anak yang menarik."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Air Terjun Sri Gethuk. Air Terjun Sri Gethuk terletak di tepi Sungai Oyo yang mengalir deras di ketinggian 50 meter. Air Terjun ini dikenal sebagai air terjun yang tidak pernah kering.",
					"Pantai Mesra. Pantai Mesra merupakan salah satu pantai di Gunung Kidul yang memiliki hamparan rumput hijau yang tumbuh subur. Suasana pantai yang romantis sehingga pantai ini dinamakan pantai Mesra.",
					"Hutan Pinus Mangunan. Daya Tarik Hutan Pinus Mangunan adalah banyaknya pohon pinus yang tinggi dan rindang, sehingga membuat suasanya menjadi sejuk dan nyaman. Udara yang bersih dan segar cocok untuk wisatawan yang penat akan bising dan panasnya suasana kota.",
					"Seribu Batu Songgo Langit. Seribu Batu Songgo Langit menyuguhkan pemandangan alam hutan pinus yang asri dan sejuk. Sebelum dijadikan sebagai tempay wisata, di lokasi ini terdapat ribuan batu alam dan deretan hutan pinus."
				]
			}
		}
	},
	{
		id: "pw-013",
		title: "Paket Wisata GANDIWA 2Hari 1Malam",
		slug: "gandiwa",
		description: "",
		price: "Rp 597,000 – Rp 1,076,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 1,076,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 644,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 597,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp",
		images: [
			"https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp",
			"https://liburanjogja.b-cdn.net/Air-Terjun-Sri-Gethuk.webp",
			"https://liburanjogja.b-cdn.net/Heha-Ocean.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp",
			"https://liburanjogja.b-cdn.net/Tebing-Breksi.webp"
		],
		location: "Yogyakarta",
		duration: "2-hari-1-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Air Terjun Sri Gethuk. Air Terjun Sri Gethuk terletak di tepi Sungai Oyo yang mengalir deras di ketinggian 50 meter. Air Terjun ini dikenal sebagai air terjun yang tidak pernah kering.",
					"Pantai Ngobaran. Meskipun tidak begitu luas, Pantai Ngobaran memiliki daya tarik yang unik, yaitu bangunan pura dan patung-patung yang mirip di seperti pantai yang ada di Bali. Disana juga menyewakan pakaian Bali, seolah-olah membuat wisatawan seperti di Bali. Pantai ini juga biasa disebut Tanah Lot ala Yogyakarta.",
					"Heha Ocean View. Heha ocean View terletak di atas tebing dan wisatawan akan disuguhkan dengan pemandangan laut lepas dengan lebih leluasa. Selain menyuguhkan pemandangan laut yang canti, Heha Ocean View juga dilengkapi dengan spot-spot foto yang instagramable."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam."
				]
			}
		}
	},
	{
		id: "pw-014",
		title: "Paket Wisata KALIMASADA 2Hari 1Malam",
		slug: "kalimasada",
		description: "",
		price: "Rp 667,000 – Rp 1,156,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 1,156,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 725,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 667,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/studio-alam-Gamplong.webp",
		images: [
			"https://liburanjogja.b-cdn.net/studio-alam-Gamplong.webp",
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp",
			"https://liburanjogja.b-cdn.net/tumpeng-Menoreh.webp"
		],
		location: "Yogyakarta",
		duration: "2-hari-1-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia.",
					"Sungai Mudal. Sungai Mudal adalah Ekowisata yang berlokasi di Kulon Progo. Pengunjung akan dimanjakan dengan spot Kawasan yang hijau nan asri. Sungai Mudal menyuguhkan sungai dengan air yang jernih dan bisa dipakai untuk berenang. Tidak hanya menyuguhkan sungainya saja, disana juga dilengkapi dengan wahana outbound dan spot-spot yang cocok untuk bersantai.",
					"Pule Payung. Pule Payung merupakan destinasi wisata yang terletak di Kulonprogo. Letaknya yang di ketinggian sehingga Pule Payung memiliki panorama alam yang maraik bagi wisatawan. Pule Payung menyuguhkan pesona perbukitan Menoreh dan indahnya pemandangan Waduk Sermo yang dilihat dari ketinggian. Banyak Spot-spot foto yang tersedia di Pule Payung yang menjadi daya tariknya juga.",
					"Kebun Teh Nglinggo. Kebun Teh Nglinggo terletak di Kulon Progo. Kebun Teh Nglinggo menyuguhkan pemandangan alam dengan deretan kebun teh yang luas dan indah."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara.",
					"Ratu Boko. Ratu Boko terletak sekitar 3km kea rah selatan dari Candi Prambanan. Ratu Boko merupakan reruntuhan sebuah kerajaan. Ratu Boko merupakan salah satu spot terbaik untuk menikmati matahari terbenam."
				]
			}
		}
	},
	{
		id: "pw-015",
		title: "Paket Wisata NAGAPASA 2Hari 1Malam",
		slug: "nagapasa",
		description: "",
		price: "Rp 738,000 – Rp 1,294,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 1,294,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 829,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 738,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/Tebing-Breksi.webp",
			"https://liburanjogja.b-cdn.net/Candi_Ijo-jogja.webp"
		],
		location: "Yogyakarta",
		duration: "2-hari-1-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Puthuk Setumbu. Merupakan bukit yang menjadi salah satu spot terbaik untuk menyaksikan matahari terbit dengan view Gunung Merapi dan Merbabu, yang lebih menariknya adalah wisatawan juga bisa melihat megahnya Candi Borobudur di pagi hari yang diselimuti kabut.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Candi Ijo. Candi Ijo merupakan candi yang letaknya paling tinggi di Yogyakarta yang menyuguhkan alam dan budayanya.."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Bukit Panguk. Yang menjadi daya tarik Bukit Panguk yaitu pemandangan alamnya yang dilihat dari ketinggian. Wisatawan dapat melihat pemandangan alam dari gardu yang terbuat dari kayu dan bamboo yang menjorok kek bibir tebing. Selain perbukitan, dari gardu ini wisatawan juga dapat melihat Sungai Oya yang membelah bukit yang terhampar hijau.",
					"Hutan Pinus Mangunan. Daya Tarik Hutan Pinus Mangunan adalah banyaknya pohon pinus yang tinggi dan rindang, sehingga membuat suasanya menjadi sejuk dan nyaman. Udara yang bersih dan segar cocok untuk wisatawan yang penat akan bising dan panasnya suasana kota.",
					"Rumah Hobbit Mangunan. Disebut juga dengan Rumah Hobbit Batu Songgo Langit. Rumah Hobbit Mangunan terletak di Mangunan, Dlingo tepatnya di daerah Bantul. Design interior atau eksterior dari rumah Hobbit terkesan sangat unik, lucu, sederhana, dan alamiah. Wisatawan akan diajak mengelilingi layaknya masuk ke dunia dongeng.",
					"Teras Kaca. Teras Kaca terletak di Gunung Kidul, Yogyakarta. Teras Kaca mengajak wisatawan untuk berdiri di atas teras kaca dengan view pemandangan laut yang begitu memukau."
				]
			}
		}
	},
	{
		id: "pw-015",
		title: "Paket Wisata NENGGALA 2Hari 1Malam",
		slug: "nenggala",
		description: "",
		price: "Rp 687,000 – Rp 1,242,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 1,242,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 840,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 687,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp",
		images: [
			"https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp",
			"https://liburanjogja.b-cdn.net/Air-Terjun-Sri-Gethuk.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/Tebing-Breksi.webp"
		],
		location: "Yogyakarta",
		duration: "2-hari-1-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Kebun Buah Mangunan. Kebun Buah Mangunan ini terkenal sebagai salah satu spot Negeri di atas awan. Banyak Wisatawan yang berkunjung pagi hari karena landscape matahari terbit dari lokasi ini sangat menawan, ditambah pemandangan Sungai Oya yang membelah bukit menjadi wisatawan terpukau dengan pemandangannya.",
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Air Terjun Sri Gethuk. Air Terjun Sri Gethuk terletak di tepi Sungai Oyo yang mengalir deras di ketinggian 50 meter. Air Terjun ini dikenal sebagai air terjun yang tidak pernah kering."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Museum Gunung Api Merapi. Museum Gunung Merapi ini memiliki 2 lantai dengan Bentuk bangunannya yang unik. Ketika hati cerah dan Gunung Merapi tidak tertutup awan, maka keduanya akan menjadi panorama yang indah. Museum ini menceritakan tentang erupsi Gunung Merapi, display tipe letusan gunung api, batuan dari Gunung Merapi sejak tahun 1930, koleksi benda-benda sisa erupsi Gunung Merapi 2006 hingga koleksi foto-foto Gunung Merapi dari Zaman ke zaman.",
					"The Lost World Castle. Terletak di Jalan Petung Merapi, Petung, Kepuharjo, cangkringan, Kabupaten Sleman. Terdapat beberapa Daya tarik di The Lost World Castle diantaranya yaitu, Gledekan atau permainan tradisional sepeda dengan tiga roda dan dua pegangan tangan di tangan sisi kanan dan kiri. Selain itu ada Spot Landmark Dunia, kapal Black Pearl, Tembok China The Lost World Castle.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan."
				]
			}
		}
	},
	{
		id: "pw-017",
		title: "Paket Wisata PANCANAKA 2Hari 1Malam",
		slug: "pancanaka",
		description: "",
		price: "Rp 602,000 – Rp 1,082,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 1,082,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 794,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 602,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/keraton-jogja.webp",
			"https://liburanjogja.b-cdn.net/gambar-taman-sari-destinasi-liburan-budaya-di-jogja.webp",
			"https://liburanjogja.b-cdn.net/Heha-Sky-View.webp"
		],
		location: "Yogyakarta",
		duration: "2-hari-1-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Puthuk Setumbu. Merupakan bukit yang menjadi salah satu spot terbaik untuk menyaksikan matahari terbit dengan view Gunung Merapi dan Merbabu, yang lebih menariknya adalah wisatawan juga bisa melihat megahnya Candi Borobudur di pagi hari yang diselimuti kabut.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Svargabumi merupakan destinasi wisata yang berada di Kabupaten Magelang, Jawa tengah. Svargabumi adalah destinasi wisata yang menyuguhkan pemandangan persawahan yang dilengakapi spot-spot foto instagramable."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			}
		}
	},
	{
		id: "pw-018",
		title: "Paket Wisata RUJAPALA 2Hari 1Malam",
		slug: "rujapala",
		description: "",
		price: "Rp 706,000 – Rp 1,185,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 1,185,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 754,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 706,000",
				label: "6 Orang"
			}
		],
		rating: 4.7,
		reviews: 120,
		image: "https://liburanjogja.b-cdn.net/goa-pindul.webp",
		images: [
			"https://liburanjogja.b-cdn.net/goa-pindul.webp",
			"https://liburanjogja.b-cdn.net/Pantai-Mesra.webp",
			"https://liburanjogja.b-cdn.net/Heha-Sky-View.webp",
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/Candi_Ijo-jogja.webp"
		],
		location: "Yogyakarta",
		duration: "2-hari-1-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Goa Pindul. Goa Pindul merupakan salah satu Goa yang banyak diminati wisatawan karena atraksinya yang menarik. Wisatawan bisa menikmati keindahan alam dengan rafting, menyusuri goa atau cave tubing Pindul, menyusuri sungai di dalam goa, menyusuri goa kristal, menyusuri Kawasan Goa Pondul denagn mobil atau off-road, outbound, menyasikan kesenian dan budaya.",
					"Pantai Mesra. Pantai Mesra merupakan salah satu pantai di Gunung Kidul yang memiliki hamparan rumput hijau yang tumbuh subur. Suasana pantai yang romantis sehingga pantai ini dinamakan pantai Mesra.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"The Lost World Castle. Terletak di Jalan Petung Merapi, Petung, Kepuharjo, cangkringan, Kabupaten Sleman. Terdapat beberapa Daya tarik di The Lost World Castle diantaranya yaitu, Gledekan atau permainan tradisional sepeda dengan tiga roda dan dua pegangan tangan di tangan sisi kanan dan kiri. Selain itu ada Spot Landmark Dunia, kapal Black Pearl, Tembok China The Lost World Castle.",
					"Candi Ijo. Candi Ijo merupakan candi yang letaknya paling tinggi di Yogyakarta yang menyuguhkan alam dan budayanya.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam."
				]
			}
		}
	},
	{
		id: "pw-019",
		title: "Paket Wisata ANDANU 3Hari 2Malam",
		slug: "andanu",
		description: "",
		price: "Rp 943,000 – Rp 1,662,000",
		pricing: [
			{
				pax: 2,
				price: "1,662,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,015,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 943,000",
				label: "6 Orang"
			}
		],
		rating: 4.8,
		reviews: 65,
		image: "https://liburanjogja.b-cdn.net/goa-pindul.webp",
		images: [
			"https://liburanjogja.b-cdn.net/goa-pindul.webp",
			"https://liburanjogja.b-cdn.net/Pantai-Mesra.webp",
			"https://liburanjogja.b-cdn.net/Heha-Sky-View.webp",
			"https://liburanjogja.b-cdn.net/gambar-taman-sari-destinasi-liburan-budaya-di-jogja.webp",
			"https://liburanjogja.b-cdn.net/keraton-jogja.webp"
		],
		location: "Yogyakarta",
		duration: "3-hari-2-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Goa Pindul. Goa Pindul merupakan salah satu Goa yang banyak diminati wisatawan karena atraksinya yang menarik. Wisatawan bisa menikmati keindahan alam dengan rafting, menyusuri goa atau cave tubing Pindul, menyusuri sungai di dalam goa, menyusuri goa kristal, menyusuri Kawasan Goa Pondul denagn mobil atau off-road, outbound, menyasikan kesenian dan budaya.",
					"Pantai Mesra. Pantai Mesra merupakan salah satu pantai di Gunung Kidul yang memiliki hamparan rumput hijau yang tumbuh subur. Suasana pantai yang romantis sehingga pantai ini dinamakan pantai Mesra.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia.",
					"Pule Payung. Pule Payung merupakan destinasi wisata yang terletak di Kulonprogo. Letaknya yang di ketinggian sehingga Pule Payung memiliki panorama alam yang maraik bagi wisatawan. Pule Payung menyuguhkan pesona perbukitan Menoreh dan indahnya pemandangan Waduk Sermo yang dilihat dari ketinggian. Banyak Spot-spot foto yang tersedia di Pule Payung yang menjadi daya tariknya juga.",
					"Sungai Mudal. Sungai Mudal adalah Ekowisata yang berlokasi di Kulon Progo. Pengunjung akan dimanjakan dengan spot Kawasan yang hijau nan asri. Sungai Mudal menyuguhkan sungai dengan air yang jernih dan bisa dipakai untuk berenang. Tidak hanya menyuguhkan sungainya saja, disana juga dilengkapi dengan wahana outbound dan spot-spot yang cocok untuk bersantai.",
					"Kebun Teh Nglinggo. Kebun Teh Nglinggo terletak di Kulon Progo. Kebun Teh Nglinggo menyuguhkan pemandangan alam dengan deretan kebun teh yang luas dan indah."
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Museum Ullen Sentalu. Museum ini termasuk ke dalam wisata edukasi, jadi sangat cocok untuk mengenalkan sejarah ke anak-anak. Tidak hanya untuk wisata edukasi saja, di Ullen Sentalu juga banyak spot-spot foto yang instagramable dan cocok untuk remaja yang suka berswa foto di Museum. Museum Ullen Setalu terdapat beberapa peninggalan bangsawan Jawa yang menjadi koleksi seperti batik, karya intelektual berupa sait dan tulisan, alat musik tradisional, lukisan, patung, furniture Jawa, dll.",
					"The Lost World Castle. Terletak di Jalan Petung Merapi, Petung, Kepuharjo, cangkringan, Kabupaten Sleman. Terdapat beberapa Daya tarik di The Lost World Castle diantaranya yaitu, Gledekan atau permainan tradisional sepeda dengan tiga roda dan dua pegangan tangan di tangan sisi kanan dan kiri. Selain itu ada Spot Landmark Dunia, kapal Black Pearl, Tembok China The Lost World Castle."
				]
			}
		}
	},
	{
		id: "pw-020",
		title: "Paket Wisata ANTABOGA 3Hari 2Malam",
		slug: "antaboga",
		description: "",
		price: "Rp 1,127,000 – Rp 1,846,000",
		pricing: [
			{
				pax: 2,
				price: "1,846,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,199,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,127,000",
				label: "6 Orang"
			}
		],
		rating: 4.8,
		reviews: 65,
		image: "https://liburanjogja.b-cdn.net/Bukit-Panguk.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Bukit-Panguk.webp",
			"https://liburanjogja.b-cdn.net/Rumah-Hobbit-Mangunan.webp",
			"https://liburanjogja.b-cdn.net/Teras-Kaca.webp",
			"https://liburanjogja.b-cdn.net/goa-pindul.webp",
			"https://liburanjogja.b-cdn.net/keraton-jogja.webp"
		],
		location: "Yogyakarta",
		duration: "3-hari-2-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Bukit Panguk. Yang menjadi daya tarik Bukit Panguk yaitu pemandangan alamnya yang dilihat dari ketinggian. Wisatawan dapat melihat pemandangan alam dari gardu yang terbuat dari kayu dan bamboo yang menjorok kek bibir tebing. Selain perbukitan, dari gardu ini wisatawan juga dapat melihat Sungai Oya yang membelah bukit yang terhampar hijau.",
					"Rumah Hobbit Mangunan. Disebut juga dengan Rumah Hobbit Batu Songgo Langit. Rumah Hobbit Mangunan terletak di Mangunan, Dlingo tepatnya di daerah Bantul. Design interior atau eksterior dari rumah Hobbit terkesan sangat unik, lucu, sederhana, dan alamiah. Wisatawan akan diajak mengelilingi layaknya masuk ke dunia dongeng.",
					"Hutan Pinus Mangunan. Daya Tarik Hutan Pinus Mangunan adalah banyaknya pohon pinus yang tinggi dan rindang, sehingga membuat suasanya menjadi sejuk dan nyaman. Udara yang bersih dan segar cocok untuk wisatawan yang penat akan bising dan panasnya suasana kota.",
					"Teras Kaca. Teras Kaca terletak di Gunung Kidul, Yogyakarta. Teras Kaca mengajak wisatawan untuk berdiri di atas teras kaca dengan view pemandangan laut yang begitu memukau.",
					"Bukit Paralayang. merupakan salah satu destinasi wisata alam yang menawarkan keelokan panorama dan pengalaman unik untuk para wisatawan. Terletak di kawasan Parangtritis, Bantul, Bukit Paralayang menyuguhkan panorama alam yang menawan dari ketinggian, dan peluang buat berupaya berolahraga paralayang yang mendebarkan"
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Goa Pindul. Goa Pindul merupakan salah satu Goa yang banyak diminati wisatawan karena atraksinya yang menarik. Wisatawan bisa menikmati keindahan alam dengan rafting, menyusuri goa atau cave tubing Pindul, menyusuri sungai di dalam goa, menyusuri goa kristal, menyusuri Kawasan Goa Pondul denagn mobil atau off-road, outbound, menyasikan kesenian dan budaya.",
					"Pantai Mesra. Pantai Mesra merupakan salah satu pantai di Gunung Kidul yang memiliki hamparan rumput hijau yang tumbuh subur. Suasana pantai yang romantis sehingga pantai ini dinamakan pantai Mesra.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Belajar Membatik, Selain berbelanja batik, di Yogyakarta wisatawan juga bisa ke destinasi yang mengajak wisatawannya untuk belajar membatik. Wisatawan akan diajak mengenal bagaimana proses pembuatan batik, mulai dari menulis corak batiknya sampai diwarnai dan finishingnya. Hasil dari batik yang ditulis wisatawan juga bisa di bawa pulang untuk souvenir. ",
					"Belajar Membuat Wayang. Salah satu seni tradisional yang menjadi ikon kota Yogyakarta yaitu Wayang Kulit. Wisatawan akan diajak untuk mengetahui dan belajar membuat wayang kulit. Hal ini menjadi daya tarik yang unik bagi wisatawan."
				]
			}
		}
	},
	{
		id: "pw-021",
		title: "Paket Wisata BENDANA 3Hari 2Malam",
		slug: "bendana",
		description: "",
		price: "Rp 1,091,000 – Rp 1,886,000",
		pricing: [
			{
				pax: 2,
				price: "1,886,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,125,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,091,000 ",
				label: "6 Orang"
			}
		],
		rating: 4.8,
		reviews: 65,
		image: "https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/Tebing-Breksi.webp",
			"https://liburanjogja.b-cdn.net/Air-Terjun-Sri-Gethuk.webp"
		],
		location: "Yogyakarta",
		duration: "3-hari-2-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Puthuk Setumbu. Merupakan bukit yang menjadi salah satu spot terbaik untuk menyaksikan matahari terbit dengan view Gunung Merapi dan Merbabu, yang lebih menariknya adalah wisatawan juga bisa melihat megahnya Candi Borobudur di pagi hari yang diselimuti kabut.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Museum Gunung Api Merapi. Museum Gunung Merapi ini memiliki 2 lantai dengan Bentuk bangunannya yang unik. Ketika hati cerah dan Gunung Merapi tidak tertutup awan, maka keduanya akan menjadi panorama yang indah. Museum ini menceritakan tentang erupsi Gunung Merapi, display tipe letusan gunung api, batuan dari Gunung Merapi sejak tahun 1930, koleksi benda-benda sisa erupsi Gunung Merapi 2006 hingga koleksi foto-foto Gunung Merapi dari Zaman ke zaman.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Air Terjun Sri Gethuk. Air Terjun Sri Gethuk terletak di tepi Sungai Oyo yang mengalir deras di ketinggian 50 meter. Air Terjun ini dikenal sebagai air terjun yang tidak pernah kering.",
					"Pantai Ngobaran. Meskipun tidak begitu luas, Pantai Ngobaran memiliki daya tarik yang unik, yaitu bangunan pura dan patung-patung yang mirip di seperti pantai yang ada di Bali. Disana juga menyewakan pakaian Bali, seolah-olah membuat wisatawan seperti di Bali. Pantai ini juga biasa disebut Tanah Lot ala Yogyakarta.",
					"Heha Ocean View. Heha ocean View terletak di atas tebing dan wisatawan akan disuguhkan dengan pemandangan laut lepas dengan lebih leluasa. Selain menyuguhkan pemandangan laut yang canti, Heha Ocean View juga dilengkapi dengan spot-spot foto yang instagramable."
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia.",
					"Pule Payung. Pule Payung merupakan destinasi wisata yang terletak di Kulonprogo. Letaknya yang di ketinggian sehingga Pule Payung memiliki panorama alam yang maraik bagi wisatawan. Pule Payung menyuguhkan pesona perbukitan Menoreh dan indahnya pemandangan Waduk Sermo yang dilihat dari ketinggian. Banyak Spot-spot foto yang tersedia di Pule Payung yang menjadi daya tariknya juga.",
					"Sungai Mudal. Sungai Mudal adalah Ekowisata yang berlokasi di Kulon Progo. Pengunjung akan dimanjakan dengan spot Kawasan yang hijau nan asri. Sungai Mudal menyuguhkan sungai dengan air yang jernih dan bisa dipakai untuk berenang. Tidak hanya menyuguhkan sungainya saja, disana juga dilengkapi dengan wahana outbound dan spot-spot yang cocok untuk bersantai.",
					"Kebun Teh Nglinggo. Kebun Teh Nglinggo terletak di Kulon Progo. Kebun Teh Nglinggo menyuguhkan pemandangan alam dengan deretan kebun teh yang luas dan indah."
				]
			}
		}
	},
	{
		id: "pw-022",
		title: "Paket Wisata JATAYU 3Hari 2Malam",
		slug: "jatayu",
		description: "",
		price: "Rp 909,000 – Rp 1,628,000",
		pricing: [
			{
				pax: 2,
				price: "1,628,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 981,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 909,000",
				label: "6 Orang"
			}
		],
		rating: 4.8,
		reviews: 65,
		image: "https://liburanjogja.b-cdn.net/studio-alam-Gamplong.webp",
		images: [
			"https://liburanjogja.b-cdn.net/studio-alam-Gamplong.webp",
			"https://liburanjogja.b-cdn.net/Bukit-Panguk.webp",
			"https://liburanjogja.b-cdn.net/Rumah-Hobbit-Mangunan.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp",
			"https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp"
		],
		location: "Yogyakarta",
		duration: "3-hari-2-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia.",
					"Pule Payung. Pule Payung merupakan destinasi wisata yang terletak di Kulonprogo. Letaknya yang di ketinggian sehingga Pule Payung memiliki panorama alam yang maraik bagi wisatawan. Pule Payung menyuguhkan pesona perbukitan Menoreh dan indahnya pemandangan Waduk Sermo yang dilihat dari ketinggian. Banyak Spot-spot foto yang tersedia di Pule Payung yang menjadi daya tariknya juga.",
					"Sungai Mudal. Sungai Mudal adalah Ekowisata yang berlokasi di Kulon Progo. Pengunjung akan dimanjakan dengan spot Kawasan yang hijau nan asri. Sungai Mudal menyuguhkan sungai dengan air yang jernih dan bisa dipakai untuk berenang. Tidak hanya menyuguhkan sungainya saja, disana juga dilengkapi dengan wahana outbound dan spot-spot yang cocok untuk bersantai.",
					"Kebun Teh Nglinggo. Kebun Teh Nglinggo terletak di Kulon Progo. Kebun Teh Nglinggo menyuguhkan pemandangan alam dengan deretan kebun teh yang luas dan indah."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Bukit Panguk. Yang menjadi daya tarik Bukit Panguk yaitu pemandangan alamnya yang dilihat dari ketinggian. Wisatawan dapat melihat pemandangan alam dari gardu yang terbuat dari kayu dan bamboo yang menjorok kek bibir tebing. Selain perbukitan, dari gardu ini wisatawan juga dapat melihat Sungai Oya yang membelah bukit yang terhampar hijau.",
					"Rumah Hobbit Mangunan. Disebut juga dengan Rumah Hobbit Batu Songgo Langit. Rumah Hobbit Mangunan terletak di Mangunan, Dlingo tepatnya di daerah Bantul. Design interior atau eksterior dari rumah Hobbit terkesan sangat unik, lucu, sederhana, dan alamiah. Wisatawan akan diajak mengelilingi layaknya masuk ke dunia dongeng.",
					"Hutan Pinus Mangunan. Daya Tarik Hutan Pinus Mangunan adalah banyaknya pohon pinus yang tinggi dan rindang, sehingga membuat suasanya menjadi sejuk dan nyaman. Udara yang bersih dan segar cocok untuk wisatawan yang penat akan bising dan panasnya suasana kota.",
					"Teras Kaca. Teras Kaca terletak di Gunung Kidul, Yogyakarta. Teras Kaca mengajak wisatawan untuk berdiri di atas teras kaca dengan view pemandangan laut yang begitu memukau.",
					"Bukit Paralayang. merupakan salah satu destinasi wisata alam yang menawarkan keelokan panorama dan pengalaman unik untuk para wisatawan. Terletak di kawasan Parangtritis, Bantul, Bukit Paralayang menyuguhkan panorama alam yang menawan dari ketinggian, dan peluang buat berupaya berolahraga paralayang yang mendebarkan"
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara.",
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam."
				]
			}
		}
	},
	{
		id: "pw-023",
		title: "Paket Wisata SEMPATI 3Hari 2Malam",
		slug: "sempati",
		description: "",
		price: "Rp 972,000 – Rp 1,544,000",
		pricing: [
			{
				pax: 2,
				price: " 1,544,000  ",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,044,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp  972,000 ",
				label: "6 Orang"
			}
		],
		rating: 4.8,
		reviews: 65,
		image: "https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
		images: [
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/goa-pindul.webp",
			"https://liburanjogja.b-cdn.net/Heha-Sky-View.webp",
			"https://liburanjogja.b-cdn.net/tempat-wisata-jalan-malioboro-yogyakarta-scaled.webp",
			"https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp"
		],
		location: "Yogyakarta",
		duration: "3-hari-2-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Gereja Ayam. Gereja Ayam merupakan wisata religi dengan pemandangan alam yang Indah. Wisata religi yang berada di bukit Rhema ini dibangun di daerah Borobudur, Magelang. Selain keindahan alamnya yang memukau, arsitektur Gereja Ayam juga menarik, dimana bentuknya menyerupai kepala burung Merpati yang memiliki mahkota diatasnya.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"The Lost World Castle. Terletak di Jalan Petung Merapi, Petung, Kepuharjo, cangkringan, Kabupaten Sleman. Terdapat beberapa Daya tarik di The Lost World Castle diantaranya yaitu, Gledekan atau permainan tradisional sepeda dengan tiga roda dan dua pegangan tangan di tangan sisi kanan dan kiri. Selain itu ada Spot Landmark Dunia, kapal Black Pearl, Tembok China The Lost World Castle.",
					"Museum Gunung Api Merapi. Museum Gunung Merapi ini memiliki 2 lantai dengan Bentuk bangunannya yang unik. Ketika hati cerah dan Gunung Merapi tidak tertutup awan, maka keduanya akan menjadi panorama yang indah. Museum ini menceritakan tentang erupsi Gunung Merapi, display tipe letusan gunung api, batuan dari Gunung Merapi sejak tahun 1930, koleksi benda-benda sisa erupsi Gunung Merapi 2006 hingga koleksi foto-foto Gunung Merapi dari Zaman ke zaman."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Goa Pindul. Goa Pindul merupakan salah satu Goa yang banyak diminati wisatawan karena atraksinya yang menarik. Wisatawan bisa menikmati keindahan alam dengan rafting, menyusuri goa atau cave tubing Pindul, menyusuri sungai di dalam goa, menyusuri goa kristal, menyusuri Kawasan Goa Pondul denagn mobil atau off-road, outbound, menyasikan kesenian dan budaya.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View.",
					"Malioboro merupakan salah satu destinasi wisata sangat ikonik di Yogyakarta, Indonesia. Terletak di pusat kota, jalur ini membentang selama kurang lebih 2, 5 km dari Stasiun Tugu sampai ke titik Nol Km Yogyakarta"
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Bukit Panguk. Yang menjadi daya tarik Bukit Panguk yaitu pemandangan alamnya yang dilihat dari ketinggian. Wisatawan dapat melihat pemandangan alam dari gardu yang terbuat dari kayu dan bamboo yang menjorok kek bibir tebing. Selain perbukitan, dari gardu ini wisatawan juga dapat melihat Sungai Oya yang membelah bukit yang terhampar hijau.",
					"Rumah Hobbit Mangunan. Disebut juga dengan Rumah Hobbit Batu Songgo Langit. Rumah Hobbit Mangunan terletak di Mangunan, Dlingo tepatnya di daerah Bantul. Design interior atau eksterior dari rumah Hobbit terkesan sangat unik, lucu, sederhana, dan alamiah. Wisatawan akan diajak mengelilingi layaknya masuk ke dunia dongeng.",
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari."
				]
			}
		}
	},
	{
		id: "pw-024",
		title: "Paket Wisata TAKSAKA 3Hari 2Malam",
		slug: "taksaka",
		description: "",
		price: "Rp 1,025,000 – Rp 1,800,000",
		pricing: [
			{
				pax: 2,
				price: "1,800,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,180,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,025,000",
				label: "6 Orang"
			}
		],
		rating: 4.8,
		reviews: 65,
		image: "https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/Obelix-Hills.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp"
		],
		location: "Yogyakarta",
		duration: "3-hari-2-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Puthuk Setumbu. Merupakan bukit yang menjadi salah satu spot terbaik untuk menyaksikan matahari terbit dengan view Gunung Merapi dan Merbabu, yang lebih menariknya adalah wisatawan juga bisa melihat megahnya Candi Borobudur di pagi hari yang diselimuti kabut.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Pantai Ngobaran. Meskipun tidak begitu luas, Pantai Ngobaran memiliki daya tarik yang unik, yaitu bangunan pura dan patung-patung yang mirip di seperti pantai yang ada di Bali. Disana juga menyewakan pakaian Bali, seolah-olah membuat wisatawan seperti di Bali. Pantai ini juga biasa disebut Tanah Lot ala Yogyakarta.",
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara."
				]
			}
		}
	},
	{
		id: "pw-025",
		title: "Paket Wisata ANOMAN 4 Hari 3 Malam",
		slug: "4d-anoman",
		description: "",
		price: "Rp 1,398,000 – Rp 2,433,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 2,433,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,455,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,398,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 25,
		image: "https://liburanjogja.b-cdn.net/Lava-Tour.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp",
			"https://liburanjogja.b-cdn.net/Tebing-Breksi.webp",
			"https://liburanjogja.b-cdn.net/Obelix-Hills.webp",
			"https://liburanjogja.b-cdn.net/studio-alam-Gamplong.webp"
		],
		location: "Yogyakarta",
		duration: "4-hari-3-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia.",
					"Pule Payung. Pule Payung merupakan destinasi wisata yang terletak di Kulonprogo. Letaknya yang di ketinggian sehingga Pule Payung memiliki panorama alam yang maraik bagi wisatawan. Pule Payung menyuguhkan pesona perbukitan Menoreh dan indahnya pemandangan Waduk Sermo yang dilihat dari ketinggian. Banyak Spot-spot foto yang tersedia di Pule Payung yang menjadi daya tariknya juga.",
					"Sungai Mudal. Sungai Mudal adalah Ekowisata yang berlokasi di Kulon Progo. Pengunjung akan dimanjakan dengan spot Kawasan yang hijau nan asri. Sungai Mudal menyuguhkan sungai dengan air yang jernih dan bisa dipakai untuk berenang. Tidak hanya menyuguhkan sungainya saja, disana juga dilengkapi dengan wahana outbound dan spot-spot yang cocok untuk bersantai.",
					"Kebun Teh Nglinggo. Kebun Teh Nglinggo terletak di Kulon Progo. Kebun Teh Nglinggo menyuguhkan pemandangan alam dengan deretan kebun teh yang luas dan indah."
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Bukit Panguk. Yang menjadi daya tarik Bukit Panguk yaitu pemandangan alamnya yang dilihat dari ketinggian. Wisatawan dapat melihat pemandangan alam dari gardu yang terbuat dari kayu dan bamboo yang menjorok kek bibir tebing. Selain perbukitan, dari gardu ini wisatawan juga dapat melihat Sungai Oya yang membelah bukit yang terhampar hijau.",
					"Rumah Hobbit Mangunan. Disebut juga dengan Rumah Hobbit Batu Songgo Langit. Rumah Hobbit Mangunan terletak di Mangunan, Dlingo tepatnya di daerah Bantul. Design interior atau eksterior dari rumah Hobbit terkesan sangat unik, lucu, sederhana, dan alamiah. Wisatawan akan diajak mengelilingi layaknya masuk ke dunia dongeng.",
					"Teras Kaca. Teras Kaca terletak di Gunung Kidul, Yogyakarta. Teras Kaca mengajak wisatawan untuk berdiri di atas teras kaca dengan view pemandangan laut yang begitu memukau.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			day4: {
				title: "Hari Keempat",
				activities: [
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Belajar Membatik, Selain berbelanja batik, di Yogyakarta wisatawan juga bisa ke destinasi yang mengajak wisatawannya untuk belajar membatik. Wisatawan akan diajak mengenal bagaimana proses pembuatan batik, mulai dari menulis corak batiknya sampai diwarnai dan finishingnya. Hasil dari batik yang ditulis wisatawan juga bisa di bawa pulang untuk souvenir. "
				]
			}
		}
	},
	{
		id: "pw-026",
		title: "Paket Wisata ARJUNA 4Hari 3Malam",
		slug: "4d-arjuna",
		description: "",
		price: "Rp 1,317,000 – Rp 2,352,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 2,352,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,375,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,317,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 25,
		image: "https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/Tebing-Breksi.webp"
		],
		location: "Yogyakarta",
		duration: "4-hari-3-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Puthuk Setumbu. Merupakan bukit yang menjadi salah satu spot terbaik untuk menyaksikan matahari terbit dengan view Gunung Merapi dan Merbabu, yang lebih menariknya adalah wisatawan juga bisa melihat megahnya Candi Borobudur di pagi hari yang diselimuti kabut.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara.",
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam.",
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia.",
					"Pule Payung. Pule Payung merupakan destinasi wisata yang terletak di Kulonprogo. Letaknya yang di ketinggian sehingga Pule Payung memiliki panorama alam yang maraik bagi wisatawan. Pule Payung menyuguhkan pesona perbukitan Menoreh dan indahnya pemandangan Waduk Sermo yang dilihat dari ketinggian. Banyak Spot-spot foto yang tersedia di Pule Payung yang menjadi daya tariknya juga.",
					"Sungai Mudal. Sungai Mudal adalah Ekowisata yang berlokasi di Kulon Progo. Pengunjung akan dimanjakan dengan spot Kawasan yang hijau nan asri. Sungai Mudal menyuguhkan sungai dengan air yang jernih dan bisa dipakai untuk berenang. Tidak hanya menyuguhkan sungainya saja, disana juga dilengkapi dengan wahana outbound dan spot-spot yang cocok untuk bersantai.",
					"Kebun Teh Nglinggo. Kebun Teh Nglinggo terletak di Kulon Progo. Kebun Teh Nglinggo menyuguhkan pemandangan alam dengan deretan kebun teh yang luas dan indah."
				]
			},
			day4: {
				title: "Hari Keempat",
				activities: [
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara."
				]
			}
		}
	},
	{
		id: "pw-027",
		title: "Paket Wisata BISMA 4 Hari 3 Malam",
		slug: "4d-bisma",
		description: "",
		price: "Rp 1,363,000 – Rp 2,341,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 2,341,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,363,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,398,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 25,
		image: "https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
		images: [
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/Bukit-Panguk.webp",
			"https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp",
			"https://liburanjogja.b-cdn.net/Teras-Kaca.webp"
		],
		location: "Yogyakarta",
		duration: "4-hari-3-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Svargabumi merupakan destinasi wisata yang berada di Kabupaten Magelang, Jawa tengah. Svargabumi adalah destinasi wisata yang menyuguhkan pemandangan persawahan yang dilengakapi spot-spot foto instagramable.",
					"Museum Ullen Sentalu. Museum ini termasuk ke dalam wisata edukasi, jadi sangat cocok untuk mengenalkan sejarah ke anak-anak. Tidak hanya untuk wisata edukasi saja, di Ullen Sentalu juga banyak spot-spot foto yang instagramable dan cocok untuk remaja yang suka berswa foto di Museum. Museum Ullen Setalu terdapat beberapa peninggalan bangsawan Jawa yang menjadi koleksi seperti batik, karya intelektual berupa sait dan tulisan, alat musik tradisional, lukisan, patung, furniture Jawa, dll.",
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Bukit Panguk. Yang menjadi daya tarik Bukit Panguk yaitu pemandangan alamnya yang dilihat dari ketinggian. Wisatawan dapat melihat pemandangan alam dari gardu yang terbuat dari kayu dan bamboo yang menjorok kek bibir tebing. Selain perbukitan, dari gardu ini wisatawan juga dapat melihat Sungai Oya yang membelah bukit yang terhampar hijau.",
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Pantai Ngobaran. Meskipun tidak begitu luas, Pantai Ngobaran memiliki daya tarik yang unik, yaitu bangunan pura dan patung-patung yang mirip di seperti pantai yang ada di Bali. Disana juga menyewakan pakaian Bali, seolah-olah membuat wisatawan seperti di Bali. Pantai ini juga biasa disebut Tanah Lot ala Yogyakarta.",
					"Teras Kaca. Teras Kaca terletak di Gunung Kidul, Yogyakarta. Teras Kaca mengajak wisatawan untuk berdiri di atas teras kaca dengan view pemandangan laut yang begitu memukau.",
					"Bukit Paralayang. merupakan salah satu destinasi wisata alam yang menawarkan keelokan panorama dan pengalaman unik untuk para wisatawan. Terletak di kawasan Parangtritis, Bantul, Bukit Paralayang menyuguhkan panorama alam yang menawan dari ketinggian, dan peluang buat berupaya berolahraga paralayang yang mendebarkan"
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Candi Ijo. Candi Ijo merupakan candi yang letaknya paling tinggi di Yogyakarta yang menyuguhkan alam dan budayanya.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			day4: {
				title: "Hari Keempat",
				activities: [
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Malioboro merupakan salah satu destinasi wisata sangat ikonik di Yogyakarta, Indonesia. Terletak di pusat kota, jalur ini membentang selama kurang lebih 2, 5 km dari Stasiun Tugu sampai ke titik Nol Km Yogyakarta"
				]
			}
		}
	},
	{
		id: "pw-029",
		title: "Paket Wisata NAGALOKA 4 Hari 3 Malam",
		slug: "4d-nagaloka",
		description: "",
		price: "Rp 1,380,000 – Rp 2,415,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 2,415,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,438,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,380,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 25,
		image: "https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
		images: [
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp",
			"https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp",
			"https://liburanjogja.b-cdn.net/Air-Terjun-Sri-Gethuk.webp"
		],
		location: "Yogyakarta",
		duration: "4-hari-3-malam",
		category: "tanpa-hotel",
		highlights: [
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Gereja Ayam. Gereja Ayam merupakan wisata religi dengan pemandangan alam yang Indah. Wisata religi yang berada di bukit Rhema ini dibangun di daerah Borobudur, Magelang. Selain keindahan alamnya yang memukau, arsitektur Gereja Ayam juga menarik, dimana bentuknya menyerupai kepala burung Merpati yang memiliki mahkota diatasnya.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Air Terjun Sri Gethuk. Air Terjun Sri Gethuk terletak di tepi Sungai Oyo yang mengalir deras di ketinggian 50 meter. Air Terjun ini dikenal sebagai air terjun yang tidak pernah kering.",
					"Pantai Ngobaran. Meskipun tidak begitu luas, Pantai Ngobaran memiliki daya tarik yang unik, yaitu bangunan pura dan patung-patung yang mirip di seperti pantai yang ada di Bali. Disana juga menyewakan pakaian Bali, seolah-olah membuat wisatawan seperti di Bali. Pantai ini juga biasa disebut Tanah Lot ala Yogyakarta.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam."
				]
			},
			day4: {
				title: "Hari Keempat",
				activities: [
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia.",
					"Sungai Mudal. Sungai Mudal adalah Ekowisata yang berlokasi di Kulon Progo. Pengunjung akan dimanjakan dengan spot Kawasan yang hijau nan asri. Sungai Mudal menyuguhkan sungai dengan air yang jernih dan bisa dipakai untuk berenang. Tidak hanya menyuguhkan sungainya saja, disana juga dilengkapi dengan wahana outbound dan spot-spot yang cocok untuk bersantai.",
					"Kebun Teh Nglinggo. Kebun Teh Nglinggo terletak di Kulon Progo. Kebun Teh Nglinggo menyuguhkan pemandangan alam dengan deretan kebun teh yang luas dan indah.",
					"Pule Payung. Pule Payung merupakan destinasi wisata yang terletak di Kulonprogo. Letaknya yang di ketinggian sehingga Pule Payung memiliki panorama alam yang maraik bagi wisatawan. Pule Payung menyuguhkan pesona perbukitan Menoreh dan indahnya pemandangan Waduk Sermo yang dilihat dari ketinggian. Banyak Spot-spot foto yang tersedia di Pule Payung yang menjadi daya tariknya juga."
				]
			}
		}
	},
	{
		id: "pw-033",
		title: "Paket Wisata dengan Hotel GANDIWA",
		slug: "paket-gandiwa-dengan-hotel",
		description: "",
		price: "Rp 913,000 – Rp 1,392,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 1,392,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,119,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 913,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp",
		images: [
			"https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp",
			"https://liburanjogja.b-cdn.net/Air-Terjun-Sri-Gethuk.webp",
			"https://liburanjogja.b-cdn.net/Heha-Ocean.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp",
			"https://liburanjogja.b-cdn.net/Tebing-Breksi.webp"
		],
		location: "Yogyakarta",
		duration: "2-hari-1-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Air Terjun Sri Gethuk. Air Terjun Sri Gethuk terletak di tepi Sungai Oyo yang mengalir deras di ketinggian 50 meter. Air Terjun ini dikenal sebagai air terjun yang tidak pernah kering.",
					"Pantai Ngobaran. Meskipun tidak begitu luas, Pantai Ngobaran memiliki daya tarik yang unik, yaitu bangunan pura dan patung-patung yang mirip di seperti pantai yang ada di Bali. Disana juga menyewakan pakaian Bali, seolah-olah membuat wisatawan seperti di Bali. Pantai ini juga biasa disebut Tanah Lot ala Yogyakarta.",
					"Heha Ocean View. Heha ocean View terletak di atas tebing dan wisatawan akan disuguhkan dengan pemandangan laut lepas dengan lebih leluasa. Selain menyuguhkan pemandangan laut yang canti, Heha Ocean View juga dilengkapi dengan spot-spot foto yang instagramable."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam."
				]
			}
		}
	},
	{
		id: "pw-031",
		title: "Paket Wisata dengan Hotel KALIMASADA",
		slug: "paket-kalimasada-dengan-hotel",
		description: "",
		price: "Rp 993,000 – Rp 1,472,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 1,472,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,041,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 993,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/studio-alam-Gamplong.webp",
		images: [
			"https://liburanjogja.b-cdn.net/studio-alam-Gamplong.webp",
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp",
			"https://liburanjogja.b-cdn.net/tumpeng-Menoreh.webp"
		],
		location: "Yogyakarta",
		duration: "2-hari-1-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia.",
					"Sungai Mudal. Sungai Mudal adalah Ekowisata yang berlokasi di Kulon Progo. Pengunjung akan dimanjakan dengan spot Kawasan yang hijau nan asri. Sungai Mudal menyuguhkan sungai dengan air yang jernih dan bisa dipakai untuk berenang. Tidak hanya menyuguhkan sungainya saja, disana juga dilengkapi dengan wahana outbound dan spot-spot yang cocok untuk bersantai.",
					"Pule Payung. Pule Payung merupakan destinasi wisata yang terletak di Kulonprogo. Letaknya yang di ketinggian sehingga Pule Payung memiliki panorama alam yang maraik bagi wisatawan. Pule Payung menyuguhkan pesona perbukitan Menoreh dan indahnya pemandangan Waduk Sermo yang dilihat dari ketinggian. Banyak Spot-spot foto yang tersedia di Pule Payung yang menjadi daya tariknya juga.",
					"Kebun Teh Nglinggo. Kebun Teh Nglinggo terletak di Kulon Progo. Kebun Teh Nglinggo menyuguhkan pemandangan alam dengan deretan kebun teh yang luas dan indah."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara.",
					"Ratu Boko. Ratu Boko terletak sekitar 3km kea rah selatan dari Candi Prambanan. Ratu Boko merupakan reruntuhan sebuah kerajaan. Ratu Boko merupakan salah satu spot terbaik untuk menikmati matahari terbenam."
				]
			}
		}
	},
	{
		id: "pw-032",
		title: "Paket Wisata dengan Hotel NENGGALA",
		slug: "paket-nenggala-dengan-hotel",
		description: "",
		price: "Rp 1,003,000 – Rp 1,559,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 1,559,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,156,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,003,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp",
		images: [
			"https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp",
			"https://liburanjogja.b-cdn.net/Air-Terjun-Sri-Gethuk.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/Tebing-Breksi.webp"
		],
		location: "Yogyakarta",
		duration: "2-hari-1-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Kebun Buah Mangunan. Kebun Buah Mangunan ini terkenal sebagai salah satu spot Negeri di atas awan. Banyak Wisatawan yang berkunjung pagi hari karena landscape matahari terbit dari lokasi ini sangat menawan, ditambah pemandangan Sungai Oya yang membelah bukit menjadi wisatawan terpukau dengan pemandangannya.",
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Air Terjun Sri Gethuk. Air Terjun Sri Gethuk terletak di tepi Sungai Oyo yang mengalir deras di ketinggian 50 meter. Air Terjun ini dikenal sebagai air terjun yang tidak pernah kering."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Museum Gunung Api Merapi. Museum Gunung Merapi ini memiliki 2 lantai dengan Bentuk bangunannya yang unik. Ketika hati cerah dan Gunung Merapi tidak tertutup awan, maka keduanya akan menjadi panorama yang indah. Museum ini menceritakan tentang erupsi Gunung Merapi, display tipe letusan gunung api, batuan dari Gunung Merapi sejak tahun 1930, koleksi benda-benda sisa erupsi Gunung Merapi 2006 hingga koleksi foto-foto Gunung Merapi dari Zaman ke zaman.",
					"The Lost World Castle. Terletak di Jalan Petung Merapi, Petung, Kepuharjo, cangkringan, Kabupaten Sleman. Terdapat beberapa Daya tarik di The Lost World Castle diantaranya yaitu, Gledekan atau permainan tradisional sepeda dengan tiga roda dan dua pegangan tangan di tangan sisi kanan dan kiri. Selain itu ada Spot Landmark Dunia, kapal Black Pearl, Tembok China The Lost World Castle.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan."
				]
			}
		}
	},
	{
		id: "pw-034",
		title: "Paket Wisata dengan Hotel PANCANAKA",
		slug: "paket-pancanaka-dengan-hotel",
		description: "",
		price: "Rp 919,000 – Rp 1,398,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 1,398,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,110,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 919,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/keraton-jogja.webp",
			"https://liburanjogja.b-cdn.net/gambar-taman-sari-destinasi-liburan-budaya-di-jogja.webp",
			"https://liburanjogja.b-cdn.net/Heha-Sky-View.webp"
		],
		location: "Yogyakarta",
		duration: "2-hari-1-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Puthuk Setumbu. Merupakan bukit yang menjadi salah satu spot terbaik untuk menyaksikan matahari terbit dengan view Gunung Merapi dan Merbabu, yang lebih menariknya adalah wisatawan juga bisa melihat megahnya Candi Borobudur di pagi hari yang diselimuti kabut.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Svargabumi merupakan destinasi wisata yang berada di Kabupaten Magelang, Jawa tengah. Svargabumi adalah destinasi wisata yang menyuguhkan pemandangan persawahan yang dilengakapi spot-spot foto instagramable."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik.",
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			}
		}
	},
	{
		id: "pw-035",
		title: "Paket Wisata dengan Hotel RUJAPALA",
		slug: "paket-rujapala-dengan-hotel",
		description: "",
		price: "Rp 1,022,000 – Rp 1,500,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 1,500,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,214,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,022,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/goa-pindul.webp",
		images: [
			"https://liburanjogja.b-cdn.net/goa-pindul.webp",
			"https://liburanjogja.b-cdn.net/Pantai-Mesra.webp",
			"https://liburanjogja.b-cdn.net/Heha-Sky-View.webp",
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/Candi_Ijo-jogja.webp"
		],
		location: "Yogyakarta",
		duration: "2-hari-1-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Goa Pindul. Goa Pindul merupakan salah satu Goa yang banyak diminati wisatawan karena atraksinya yang menarik. Wisatawan bisa menikmati keindahan alam dengan rafting, menyusuri goa atau cave tubing Pindul, menyusuri sungai di dalam goa, menyusuri goa kristal, menyusuri Kawasan Goa Pondul denagn mobil atau off-road, outbound, menyasikan kesenian dan budaya.",
					"Pantai Mesra. Pantai Mesra merupakan salah satu pantai di Gunung Kidul yang memiliki hamparan rumput hijau yang tumbuh subur. Suasana pantai yang romantis sehingga pantai ini dinamakan pantai Mesra.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"The Lost World Castle. Terletak di Jalan Petung Merapi, Petung, Kepuharjo, cangkringan, Kabupaten Sleman. Terdapat beberapa Daya tarik di The Lost World Castle diantaranya yaitu, Gledekan atau permainan tradisional sepeda dengan tiga roda dan dua pegangan tangan di tangan sisi kanan dan kiri. Selain itu ada Spot Landmark Dunia, kapal Black Pearl, Tembok China The Lost World Castle.",
					"Candi Ijo. Candi Ijo merupakan candi yang letaknya paling tinggi di Yogyakarta yang menyuguhkan alam dan budayanya.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam."
				]
			}
		}
	},
	{
		id: "pw-036",
		title: "Paket Wisata dengan Hotel SUDARSANA",
		slug: "paket-sudarsana-dengan-hotel",
		description: "",
		price: "Rp 1,060,000 – Rp 1,616,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 1,616,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,214,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,060,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/keraton-jogja.webp",
		images: [
			"https://liburanjogja.b-cdn.net/keraton-jogja.webp",
			"https://liburanjogja.b-cdn.net/gambar-taman-sari-destinasi-liburan-budaya-di-jogja.webp",
			"https://liburanjogja.b-cdn.net/Heha-Ocean.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp"
		],
		location: "Yogyakarta",
		duration: "2-hari-1-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik",
					"Pantai Parangtritis. Pantai Parangtritis merupakan pantai paling ikonik di Yogyakarta. Tidak hanya sebagai tempat untuk menikmati keindahan alam, pantai Parangtritis juga memiliki nilai sejarah dan kebudayaan yang kuat.",
					"Heha Ocean View. Heha ocean View terletak di atas tebing dan wisatawan akan disuguhkan dengan pemandangan laut lepas dengan lebih leluasa. Selain menyuguhkan pemandangan laut yang canti, Heha Ocean View juga dilengkapi dengan spot-spot foto yang instagramable."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam."
				]
			}
		}
	},
	{
		id: "pw-037",
		title: "Paket Wisata dengan Hotel BRAHMASTA",
		slug: "paket-brahmasta-dengan-hotel",
		description: "",
		price: "Rp 942,000 – Rp 1,421,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 1,421,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,133,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 942,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/Air-Terjun-Sri-Gethuk.webp",
			"https://liburanjogja.b-cdn.net/Pantai-Mesra.webp",
			"https://liburanjogja.b-cdn.net/hutan-pinus-mangunan.webp"
		],
		location: "Yogyakarta",
		duration: "2-hari-1-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Puthuk Setumbu. Merupakan bukit yang menjadi salah satu spot terbaik untuk menyaksikan matahari terbit dengan view Gunung Merapi dan Merbabu, yang lebih menariknya adalah wisatawan juga bisa melihat megahnya Candi Borobudur di pagi hari yang diselimuti kabut.",
					"Gereja Ayam. Gereja Ayam merupakan wisata religi dengan pemandangan alam yang Indah. Wisata religi yang berada di bukit Rhema ini dibangun di daerah Borobudur, Magelang. Selain keindahan alamnya yang memukau, arsitektur Gereja Ayam juga menarik, dimana bentuknya menyerupai kepala burung Merpati yang memiliki mahkota diatasnya.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Merapi Park. Merapi Park dilengkapi banyak spot foto yang menarik dan instagramable. Terdapat replica ikon terkenal dunia. Inilah yang menyebabkan tempat ini juga dikenal dengan sebutan The World Landmark. Merapi park juga dilengkapi atraksi dan wahana untuk anak-anak yang menarik."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Air Terjun Sri Gethuk. Air Terjun Sri Gethuk terletak di tepi Sungai Oyo yang mengalir deras di ketinggian 50 meter. Air Terjun ini dikenal sebagai air terjun yang tidak pernah kering.",
					"Pantai Mesra. Pantai Mesra merupakan salah satu pantai di Gunung Kidul yang memiliki hamparan rumput hijau yang tumbuh subur. Suasana pantai yang romantis sehingga pantai ini dinamakan pantai Mesra.",
					"Hutan Pinus Mangunan. Daya Tarik Hutan Pinus Mangunan adalah banyaknya pohon pinus yang tinggi dan rindang, sehingga membuat suasanya menjadi sejuk dan nyaman. Udara yang bersih dan segar cocok untuk wisatawan yang penat akan bising dan panasnya suasana kota.",
					"Seribu Batu Songgo Langit. Seribu Batu Songgo Langit menyuguhkan pemandangan alam hutan pinus yang asri dan sejuk. Sebelum dijadikan sebagai tempay wisata, di lokasi ini terdapat ribuan batu alam dan deretan hutan pinus."
				]
			}
		}
	},
	{
		id: "pw-038",
		title: "Paket Wisata dengan Hotel NAGAPASA",
		slug: "paket-nagapasa-dengan-hotel",
		description: "",
		price: "Rp 1,056,000 – Rp 1,610,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 1,610,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,208,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,056,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/Tebing-Breksi.webp",
			"https://liburanjogja.b-cdn.net/Bukit-Panguk.webp"
		],
		location: "Yogyakarta",
		duration: "2-hari-1-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Puthuk Setumbu. Merupakan bukit yang menjadi salah satu spot terbaik untuk menyaksikan matahari terbit dengan view Gunung Merapi dan Merbabu, yang lebih menariknya adalah wisatawan juga bisa melihat megahnya Candi Borobudur di pagi hari yang diselimuti kabut.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Candi Ijo. Candi Ijo merupakan candi yang letaknya paling tinggi di Yogyakarta yang menyuguhkan alam dan budayanya."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Bukit Panguk. Yang menjadi daya tarik Bukit Panguk yaitu pemandangan alamnya yang dilihat dari ketinggian. Wisatawan dapat melihat pemandangan alam dari gardu yang terbuat dari kayu dan bamboo yang menjorok kek bibir tebing. Selain perbukitan, dari gardu ini wisatawan juga dapat melihat Sungai Oya yang membelah bukit yang terhampar hijau.",
					"Hutan Pinus Mangunan. Daya Tarik Hutan Pinus Mangunan adalah banyaknya pohon pinus yang tinggi dan rindang, sehingga membuat suasanya menjadi sejuk dan nyaman. Udara yang bersih dan segar cocok untuk wisatawan yang penat akan bising dan panasnya suasana kota.",
					"Rumah Hobbit Mangunan. Disebut juga dengan Rumah Hobbit Batu Songgo Langit. Rumah Hobbit Mangunan terletak di Mangunan, Dlingo tepatnya di daerah Bantul. Design interior atau eksterior dari rumah Hobbit terkesan sangat unik, lucu, sederhana, dan alamiah. Wisatawan akan diajak mengelilingi layaknya masuk ke dunia dongeng.",
					"Teras Kaca. Teras Kaca terletak di Gunung Kidul, Yogyakarta. Teras Kaca mengajak wisatawan untuk berdiri di atas teras kaca dengan view pemandangan laut yang begitu memukau."
				]
			}
		}
	},
	{
		id: "pw-040",
		title: "Paket Wisata dengan Hotel ANTABOGA",
		slug: "paket-antaboga-dengan-hotel",
		description: "",
		price: "Rp 1,760,000 – Rp 2,479,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 2,479,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,832,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,760,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/Rumah-Hobbit-Mangunan.webp",
		images: [
			"https://liburanjogja.b-cdn.net/hutan-pinus-mangunan.webp",
			"https://liburanjogja.b-cdn.net/Teras-Kaca.webp",
			"https://liburanjogja.b-cdn.net/Bukit-Paralayang.webp",
			"https://liburanjogja.b-cdn.net/Pantai-Mesra.webp"
		],
		location: "Yogyakarta",
		duration: "3-hari-2-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Bukit Panguk. Yang menjadi daya tarik Bukit Panguk yaitu pemandangan alamnya yang dilihat dari ketinggian. Wisatawan dapat melihat pemandangan alam dari gardu yang terbuat dari kayu dan bamboo yang menjorok kek bibir tebing. Selain perbukitan, dari gardu ini wisatawan juga dapat melihat Sungai Oya yang membelah bukit yang terhampar hijau.",
					"Rumah Hobbit Mangunan. Disebut juga dengan Rumah Hobbit Batu Songgo Langit. Rumah Hobbit Mangunan terletak di Mangunan, Dlingo tepatnya di daerah Bantul. Design interior atau eksterior dari rumah Hobbit terkesan sangat unik, lucu, sederhana, dan alamiah. Wisatawan akan diajak mengelilingi layaknya masuk ke dunia dongeng.",
					"Hutan Pinus Mangunan. Daya Tarik Hutan Pinus Mangunan adalah banyaknya pohon pinus yang tinggi dan rindang, sehingga membuat suasanya menjadi sejuk dan nyaman. Udara yang bersih dan segar cocok untuk wisatawan yang penat akan bising dan panasnya suasana kota.",
					"Teras Kaca. Teras Kaca terletak di Gunung Kidul, Yogyakarta. Teras Kaca mengajak wisatawan untuk berdiri di atas teras kaca dengan view pemandangan laut yang begitu memukau.",
					"Bukit Paralayang. merupakan salah satu destinasi wisata alam yang menawarkan keelokan panorama dan pengalaman unik untuk para wisatawan. Terletak di kawasan Parangtritis, Bantul, Bukit Paralayang menyuguhkan panorama alam yang menawan dari ketinggian, dan peluang buat berupaya berolahraga paralayang yang mendebarkan"
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Goa Pindul. Goa Pindul merupakan salah satu Goa yang banyak diminati wisatawan karena atraksinya yang menarik. Wisatawan bisa menikmati keindahan alam dengan rafting, menyusuri goa atau cave tubing Pindul, menyusuri sungai di dalam goa, menyusuri goa kristal, menyusuri Kawasan Goa Pondul denagn mobil atau off-road, outbound, menyasikan kesenian dan budaya.",
					"Pantai Mesra. Pantai Mesra merupakan salah satu pantai di Gunung Kidul yang memiliki hamparan rumput hijau yang tumbuh subur. Suasana pantai yang romantis sehingga pantai ini dinamakan pantai Mesra.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik",
					"Belajar Membatik, Selain berbelanja batik, di Yogyakarta wisatawan juga bisa ke destinasi yang mengajak wisatawannya untuk belajar membatik. Wisatawan akan diajak mengenal bagaimana proses pembuatan batik, mulai dari menulis corak batiknya sampai diwarnai dan finishingnya. Hasil dari batik yang ditulis wisatawan juga bisa di bawa pulang untuk souvenir. ",
					"Belajar Membuat Wayang. Salah satu seni tradisional yang menjadi ikon kota Yogyakarta yaitu Wayang Kulit. Wisatawan akan diajak untuk mengetahui dan belajar membuat wayang kulit. Hal ini menjadi daya tarik yang unik bagi wisatawan."
				]
			}
		}
	},
	{
		id: "pw-041",
		title: "Paket Wisata dengan Hotel BENDANA",
		slug: "paket-bendana-dengan-hotel",
		description: "",
		price: "Rp 1,724,000 – Rp 2,519,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 2,519,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,857,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,724,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
		images: [
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/Tebing-Breksi.webp",
			"https://liburanjogja.b-cdn.net/pantai-ngobaran-5.webp"
		],
		location: "Yogyakarta",
		duration: "3-hari-2-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Puthuk Setumbu. Merupakan bukit yang menjadi salah satu spot terbaik untuk menyaksikan matahari terbit dengan view Gunung Merapi dan Merbabu, yang lebih menariknya adalah wisatawan juga bisa melihat megahnya Candi Borobudur di pagi hari yang diselimuti kabut.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Museum Gunung Api Merapi. Museum Gunung Merapi ini memiliki 2 lantai dengan Bentuk bangunannya yang unik. Ketika hati cerah dan Gunung Merapi tidak tertutup awan, maka keduanya akan menjadi panorama yang indah. Museum ini menceritakan tentang erupsi Gunung Merapi, display tipe letusan gunung api, batuan dari Gunung Merapi sejak tahun 1930, koleksi benda-benda sisa erupsi Gunung Merapi 2006 hingga koleksi foto-foto Gunung Merapi dari Zaman ke zaman.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Air Terjun Sri Gethuk. Air Terjun Sri Gethuk terletak di tepi Sungai Oyo yang mengalir deras di ketinggian 50 meter. Air Terjun ini dikenal sebagai air terjun yang tidak pernah kering.",
					"Pantai Ngobaran. Meskipun tidak begitu luas, Pantai Ngobaran memiliki daya tarik yang unik, yaitu bangunan pura dan patung-patung yang mirip di seperti pantai yang ada di Bali. Disana juga menyewakan pakaian Bali, seolah-olah membuat wisatawan seperti di Bali. Pantai ini juga biasa disebut Tanah Lot ala Yogyakarta.",
					"Heha Ocean View. Heha ocean View terletak di atas tebing dan wisatawan akan disuguhkan dengan pemandangan laut lepas dengan lebih leluasa. Selain menyuguhkan pemandangan laut yang canti, Heha Ocean View juga dilengkapi dengan spot-spot foto yang instagramable."
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia.",
					"Pule Payung. Pule Payung merupakan destinasi wisata yang terletak di Kulonprogo. Letaknya yang di ketinggian sehingga Pule Payung memiliki panorama alam yang maraik bagi wisatawan. Pule Payung menyuguhkan pesona perbukitan Menoreh dan indahnya pemandangan Waduk Sermo yang dilihat dari ketinggian. Banyak Spot-spot foto yang tersedia di Pule Payung yang menjadi daya tariknya juga.",
					"Sungai Mudal. Sungai Mudal adalah Ekowisata yang berlokasi di Kulon Progo. Pengunjung akan dimanjakan dengan spot Kawasan yang hijau nan asri. Sungai Mudal menyuguhkan sungai dengan air yang jernih dan bisa dipakai untuk berenang. Tidak hanya menyuguhkan sungainya saja, disana juga dilengkapi dengan wahana outbound dan spot-spot yang cocok untuk bersantai.",
					"Kebun Teh Nglinggo. Kebun Teh Nglinggo terletak di Kulon Progo. Kebun Teh Nglinggo menyuguhkan pemandangan alam dengan deretan kebun teh yang luas dan indah."
				]
			}
		}
	},
	{
		id: "pw-0391",
		title: "Paket Wisata dengan Hotel JATAYU",
		slug: "paket-jatayu-dengan-hotel",
		description: "",
		price: "Rp 1,541,000 – Rp 2,260,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 2,260,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,613,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,541,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/studio-alam-Gamplong.webp",
		images: [
			"https://liburanjogja.b-cdn.net/studio-alam-Gamplong.webp",
			"https://liburanjogja.b-cdn.net/Bukit-Panguk.webp",
			"https://liburanjogja.b-cdn.net/Rumah-Hobbit-Mangunan.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp",
			"https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp"
		],
		location: "Yogyakarta",
		duration: "3-hari-2-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia.",
					"Pule Payung. Pule Payung merupakan destinasi wisata yang terletak di Kulonprogo. Letaknya yang di ketinggian sehingga Pule Payung memiliki panorama alam yang maraik bagi wisatawan. Pule Payung menyuguhkan pesona perbukitan Menoreh dan indahnya pemandangan Waduk Sermo yang dilihat dari ketinggian. Banyak Spot-spot foto yang tersedia di Pule Payung yang menjadi daya tariknya juga.",
					"Sungai Mudal. Sungai Mudal adalah Ekowisata yang berlokasi di Kulon Progo. Pengunjung akan dimanjakan dengan spot Kawasan yang hijau nan asri. Sungai Mudal menyuguhkan sungai dengan air yang jernih dan bisa dipakai untuk berenang. Tidak hanya menyuguhkan sungainya saja, disana juga dilengkapi dengan wahana outbound dan spot-spot yang cocok untuk bersantai.",
					"Kebun Teh Nglinggo. Kebun Teh Nglinggo terletak di Kulon Progo. Kebun Teh Nglinggo menyuguhkan pemandangan alam dengan deretan kebun teh yang luas dan indah."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Bukit Panguk. Yang menjadi daya tarik Bukit Panguk yaitu pemandangan alamnya yang dilihat dari ketinggian. Wisatawan dapat melihat pemandangan alam dari gardu yang terbuat dari kayu dan bamboo yang menjorok kek bibir tebing. Selain perbukitan, dari gardu ini wisatawan juga dapat melihat Sungai Oya yang membelah bukit yang terhampar hijau.",
					"Rumah Hobbit Mangunan. Disebut juga dengan Rumah Hobbit Batu Songgo Langit. Rumah Hobbit Mangunan terletak di Mangunan, Dlingo tepatnya di daerah Bantul. Design interior atau eksterior dari rumah Hobbit terkesan sangat unik, lucu, sederhana, dan alamiah. Wisatawan akan diajak mengelilingi layaknya masuk ke dunia dongeng.",
					"Hutan Pinus Mangunan. Daya Tarik Hutan Pinus Mangunan adalah banyaknya pohon pinus yang tinggi dan rindang, sehingga membuat suasanya menjadi sejuk dan nyaman. Udara yang bersih dan segar cocok untuk wisatawan yang penat akan bising dan panasnya suasana kota.",
					"Teras Kaca. Teras Kaca terletak di Gunung Kidul, Yogyakarta. Teras Kaca mengajak wisatawan untuk berdiri di atas teras kaca dengan view pemandangan laut yang begitu memukau.",
					"Bukit Paralayang. merupakan salah satu destinasi wisata alam yang menawarkan keelokan panorama dan pengalaman unik untuk para wisatawan. Terletak di kawasan Parangtritis, Bantul, Bukit Paralayang menyuguhkan panorama alam yang menawan dari ketinggian, dan peluang buat berupaya berolahraga paralayang yang mendebarkan"
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara.",
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam."
				]
			}
		}
	},
	{
		id: "pw-0393",
		title: "Paket Wisata dengan Hotel SEMPATI",
		slug: "paket-sempati-dengan-hotel",
		description: "",
		price: "Rp 1,615,000 – Rp 2,323,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 2,323,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,710,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,615,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/goa-pindul.webp",
		images: [
			"https://liburanjogja.b-cdn.net/goa-pindul.webp",
			"https://liburanjogja.b-cdn.net/Heha-Sky-View.webp",
			"https://liburanjogja.b-cdn.net/tempat-wisata-jalan-malioboro-yogyakarta-scaled.webp",
			"https://liburanjogja.b-cdn.net/Bukit-Panguk.webp",
			"https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp"
		],
		location: "Yogyakarta",
		duration: "3-hari-2-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Gereja Ayam. Gereja Ayam merupakan wisata religi dengan pemandangan alam yang Indah. Wisata religi yang berada di bukit Rhema ini dibangun di daerah Borobudur, Magelang. Selain keindahan alamnya yang memukau, arsitektur Gereja Ayam juga menarik, dimana bentuknya menyerupai kepala burung Merpati yang memiliki mahkota diatasnya.",
					"Merapi Park. Merapi Park dilengkapi banyak spot foto yang menarik dan instagramable. Terdapat replica ikon terkenal dunia. Inilah yang menyebabkan tempat ini juga dikenal dengan sebutan The World Landmark. Merapi park juga dilengkapi atraksi dan wahana untuk anak-anak yang menarik.",
					"The Lost World Castle. Terletak di Jalan Petung Merapi, Petung, Kepuharjo, cangkringan, Kabupaten Sleman. Terdapat beberapa Daya tarik di The Lost World Castle diantaranya yaitu, Gledekan atau permainan tradisional sepeda dengan tiga roda dan dua pegangan tangan di tangan sisi kanan dan kiri. Selain itu ada Spot Landmark Dunia, kapal Black Pearl, Tembok China The Lost World Castle.",
					"Museum Gunung Api Merapi. Museum Gunung Merapi ini memiliki 2 lantai dengan Bentuk bangunannya yang unik. Ketika hati cerah dan Gunung Merapi tidak tertutup awan, maka keduanya akan menjadi panorama yang indah. Museum ini menceritakan tentang erupsi Gunung Merapi, display tipe letusan gunung api, batuan dari Gunung Merapi sejak tahun 1930, koleksi benda-benda sisa erupsi Gunung Merapi 2006 hingga koleksi foto-foto Gunung Merapi dari Zaman ke zaman."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Goa Pindul. Goa Pindul merupakan salah satu Goa yang banyak diminati wisatawan karena atraksinya yang menarik. Wisatawan bisa menikmati keindahan alam dengan rafting, menyusuri goa atau cave tubing Pindul, menyusuri sungai di dalam goa, menyusuri goa kristal, menyusuri Kawasan Goa Pondul denagn mobil atau off-road, outbound, menyasikan kesenian dan budaya.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View.",
					"Malioboro merupakan salah satu destinasi wisata sangat ikonik di Yogyakarta, Indonesia. Terletak di pusat kota, jalur ini membentang selama kurang lebih 2, 5 km dari Stasiun Tugu sampai ke titik Nol Km Yogyakarta"
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"melihat pemandangan alam dari gardu yang terbuat dari kayu dan bamboo yang menjorok kek bibir tebing. Selain perbukitan, dari gardu ini wisatawan juga dapat melihat Sungai Oya yang membelah bukit yang terhampar hijau.",
					"Rumah Hobbit Mangunan. Disebut juga dengan Rumah Hobbit Batu Songgo Langit. Rumah Hobbit Mangunan terletak di Mangunan, Dlingo tepatnya di daerah Bantul. Design interior atau eksterior dari rumah Hobbit terkesan sangat unik, lucu, sederhana, dan alamiah. Wisatawan akan diajak mengelilingi layaknya masuk ke dunia dongeng.",
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari."
				]
			}
		}
	},
	{
		id: "pw-039",
		title: "Paket Wisata dengan Hotel TAKSAKA",
		slug: "paket-taksaka-dengan-hotel",
		description: "",
		price: "Rp 1,637,000 – Rp 2,433,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 2,433,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,720,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,637,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/Obelix-Hills.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp"
		],
		location: "Yogyakarta",
		duration: "3-hari-2-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Puthuk Setumbu. Merupakan bukit yang menjadi salah satu spot terbaik untuk menyaksikan matahari terbit dengan view Gunung Merapi dan Merbabu, yang lebih menariknya adalah wisatawan juga bisa melihat megahnya Candi Borobudur di pagi hari yang diselimuti kabut.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Pantai Ngobaran. Meskipun tidak begitu luas, Pantai Ngobaran memiliki daya tarik yang unik, yaitu bangunan pura dan patung-patung yang mirip di seperti pantai yang ada di Bali. Disana juga menyewakan pakaian Bali, seolah-olah membuat wisatawan seperti di Bali. Pantai ini juga biasa disebut Tanah Lot ala Yogyakarta.",
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara."
				]
			}
		}
	},
	{
		id: "pw-0395",
		title: "Paket Wisata dengan Hotel ANDANU",
		slug: "paket-andanu-dengan-hotel",
		description: "",
		price: "Rp 1,576,000 – Rp 2,295,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 2,295,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 1,613,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 1,576,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/goa-pindul.webp",
		images: [
			"https://liburanjogja.b-cdn.net/goa-pindul.webp",
			"https://liburanjogja.b-cdn.net/Pantai-Mesra.webp",
			"https://liburanjogja.b-cdn.net/Heha-Sky-View.webp",
			"https://liburanjogja.b-cdn.net/studio-alam-Gamplong.webp",
			"https://liburanjogja.b-cdn.net/gambar-taman-sari-destinasi-liburan-budaya-di-jogja.webp"
		],
		location: "Yogyakarta",
		duration: "3-hari-2-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Goa Pindul. Goa Pindul merupakan salah satu Goa yang banyak diminati wisatawan karena atraksinya yang menarik. Wisatawan bisa menikmati keindahan alam dengan rafting, menyusuri goa atau cave tubing Pindul, menyusuri sungai di dalam goa, menyusuri goa kristal, menyusuri Kawasan Goa Pondul denagn mobil atau off-road, outbound, menyasikan kesenian dan budaya.",
					"Pantai Mesra. Pantai Mesra merupakan salah satu pantai di Gunung Kidul yang memiliki hamparan rumput hijau yang tumbuh subur. Suasana pantai yang romantis sehingga pantai ini dinamakan pantai Mesra.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia.",
					"Pule Payung. Pule Payung merupakan destinasi wisata yang terletak di Kulonprogo. Letaknya yang di ketinggian sehingga Pule Payung memiliki panorama alam yang maraik bagi wisatawan. Pule Payung menyuguhkan pesona perbukitan Menoreh dan indahnya pemandangan Waduk Sermo yang dilihat dari ketinggian. Banyak Spot-spot foto yang tersedia di Pule Payung yang menjadi daya tariknya juga.",
					"Sungai Mudal. Sungai Mudal adalah Ekowisata yang berlokasi di Kulon Progo. Pengunjung akan dimanjakan dengan spot Kawasan yang hijau nan asri. Sungai Mudal menyuguhkan sungai dengan air yang jernih dan bisa dipakai untuk berenang. Tidak hanya menyuguhkan sungainya saja, disana juga dilengkapi dengan wahana outbound dan spot-spot yang cocok untuk bersantai.",
					"Kebun Teh Nglinggo. Kebun Teh Nglinggo terletak di Kulon Progo. Kebun Teh Nglinggo menyuguhkan pemandangan alam dengan deretan kebun teh yang luas dan indah."
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik.",
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Museum Ullen Sentalu. Museum ini termasuk ke dalam wisata edukasi, jadi sangat cocok untuk mengenalkan sejarah ke anak-anak. Tidak hanya untuk wisata edukasi saja, di Ullen Sentalu juga banyak spot-spot foto yang instagramable dan cocok untuk remaja yang suka berswa foto di Museum",
					"The Lost World Castle. Terletak di Jalan Petung Merapi, Petung, Kepuharjo, cangkringan, Kabupaten Sleman. Terdapat beberapa Daya tarik di The Lost World Castle diantaranya yaitu, Gledekan atau permainan tradisional sepeda dengan tiga roda dan dua pegangan tangan di tangan sisi kanan dan kiri. Selain itu ada Spot Landmark Dunia, kapal Black Pearl, Tembok China The Lost World Castle."
				]
			}
		}
	},
	{
		id: "pw-031",
		title: "Paket Wisata dengan Hotel ANOMAN",
		slug: "paket-anoman-dengan-hotel",
		description: "",
		price: "Rp 2,346,000 – Rp 3,381,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 3,381,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 2,404,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 2,346,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/Lava-Tour.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp",
			"https://liburanjogja.b-cdn.net/Tebing-Breksi.webp",
			"https://liburanjogja.b-cdn.net/Obelix-Hills.webp",
			"https://liburanjogja.b-cdn.net/studio-alam-Gamplong.webp"
		],
		location: "Yogyakarta",
		duration: "4-hari-3-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia.",
					"Pule Payung. Pule Payung merupakan destinasi wisata yang terletak di Kulonprogo. Letaknya yang di ketinggian sehingga Pule Payung memiliki panorama alam yang maraik bagi wisatawan. Pule Payung menyuguhkan pesona perbukitan Menoreh dan indahnya pemandangan Waduk Sermo yang dilihat dari ketinggian. Banyak Spot-spot foto yang tersedia di Pule Payung yang menjadi daya tariknya juga.",
					"Sungai Mudal. Sungai Mudal adalah Ekowisata yang berlokasi di Kulon Progo. Pengunjung akan dimanjakan dengan spot Kawasan yang hijau nan asri. Sungai Mudal menyuguhkan sungai dengan air yang jernih dan bisa dipakai untuk berenang. Tidak hanya menyuguhkan sungainya saja, disana juga dilengkapi dengan wahana outbound dan spot-spot yang cocok untuk bersantai.",
					"Kebun Teh Nglinggo. Kebun Teh Nglinggo terletak di Kulon Progo. Kebun Teh Nglinggo menyuguhkan pemandangan alam dengan deretan kebun teh yang luas dan indah."
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Bukit Panguk. Yang menjadi daya tarik Bukit Panguk yaitu pemandangan alamnya yang dilihat dari ketinggian. Wisatawan dapat melihat pemandangan alam dari gardu yang terbuat dari kayu dan bamboo yang menjorok kek bibir tebing. Selain perbukitan, dari gardu ini wisatawan juga dapat melihat Sungai Oya yang membelah bukit yang terhampar hijau.",
					"Rumah Hobbit Mangunan. Disebut juga dengan Rumah Hobbit Batu Songgo Langit. Rumah Hobbit Mangunan terletak di Mangunan, Dlingo tepatnya di daerah Bantul. Design interior atau eksterior dari rumah Hobbi",
					"Teras Kaca. Teras Kaca terletak di Gunung Kidul, Yogyakarta. Teras Kaca mengajak wisatawan untuk berdiri di atas teras kaca dengan view pemandangan laut yang begitu memukau.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			day4: {
				title: "Hari Keempat",
				activities: [
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Belajar Membatik, Selain berbelanja batik, di Yogyakarta wisatawan juga bisa ke destinasi yang mengajak wisatawannya untuk belajar membatik. Wisatawan akan diajak mengenal bagaimana proses pembuatan batik, mulai dari menulis corak batiknya sampai diwarnai dan finishingnya. Hasil dari batik yang ditulis wisatawan juga bisa di b"
				]
			}
		}
	},
	{
		id: "pw-03922",
		title: "Paket Wisata dengan Hotel ARJUNA",
		slug: "paket-arjuna-dengan-hotel",
		description: "",
		price: "Rp 2,266,000 – Rp 3,301,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 3,301,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 2,323,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 2,266,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Puthuk-Setumbu.webp",
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/Tebing-Breksi.webp"
		],
		location: "Yogyakarta",
		duration: "4-hari-3-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Puthuk Setumbu. Merupakan bukit yang menjadi salah satu spot terbaik untuk menyaksikan matahari terbit dengan view Gunung Merapi dan Merbabu, yang lebih menariknya adalah wisatawan juga bisa melihat megahnya Candi Borobudur di pagi hari yang diselimuti kabut.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara.",
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam.",
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia.",
					"Pule Payung. Pule Payung merupakan destinasi wisata yang terletak di Kulonprogo. Letaknya yang di ketinggian sehingga Pule Payung memiliki panorama alam yang maraik bagi wisatawan. Pule Payung menyuguhkan pesona perbukitan Menoreh dan indahnya pemandangan Waduk Sermo yang dilihat dari ketinggian. Banyak Spot-spot foto yang tersedia di Pule Payung yang menjadi daya tariknya juga.",
					"Sungai Mudal. Sungai Mudal adalah Ekowisata yang berlokasi di Kulon Progo. Pengunjung akan dimanjakan dengan spot Kawasan yang hijau nan asri. Sungai Mudal menyuguhkan sungai dengan air yang jernih dan bisa dipakai untuk berenang. Tidak hanya menyuguhkan sungainya saja, disana juga dilengkapi dengan wahana outbound dan spot-spot yang cocok untuk bersantai.",
					"Kebun Teh Nglinggo. Kebun Teh Nglinggo terletak di Kulon Progo. Kebun Teh Nglinggo menyuguhkan pemandangan alam dengan deretan kebun teh yang luas dan indah."
				]
			},
			day4: {
				title: "Hari Keempat",
				activities: [
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara."
				]
			}
		}
	},
	{
		id: "pw-039222",
		title: "Paket Wisata dengan Hotel BISMA",
		slug: "paket-bisma-dengan-hotel",
		description: "",
		price: "Rp 2,254,000 – Rp 3,289,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 3,289,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 2,312,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 2,254,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
		images: [
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/Bukit-Panguk.webp",
			"https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp",
			"https://liburanjogja.b-cdn.net/Teras-Kaca.webp"
		],
		location: "Yogyakarta",
		duration: "4-hari-3-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Svargabumi merupakan destinasi wisata yang berada di Kabupaten Magelang, Jawa tengah. Svargabumi adalah destinasi wisata yang menyuguhkan pemandangan persawahan yang dilengakapi spot-spot foto instagramable.",
					"Museum Ullen Sentalu. Museum ini termasuk ke dalam wisata edukasi, jadi sangat cocok untuk mengenalkan sejarah ke anak-anak. Tidak hanya untuk wisata edukasi saja, di Ullen Sentalu juga banyak spot-spot foto yang instagramable dan cocok untuk remaja yang suka berswa foto di Museum",
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Bukit Panguk. Yang menjadi daya tarik Bukit Panguk yaitu pemandangan alamnya yang dilihat dari ketinggian. Wisatawan dapat melihat pemandangan alam dari gardu yang terbuat dari kayu dan bamboo yang menjorok kek bibir tebing. Selain perbukitan, dari gardu ini wisatawan juga dapat melihat Sungai Oya yang membelah bukit yang terhampar hijau.",
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Pantai Ngobaran. Meskipun tidak begitu luas, Pantai Ngobaran memiliki daya tarik yang unik, yaitu bangunan pura dan patung-patung yang mirip di seperti pantai yang ada di Bali. Disana juga menyewakan pakaian Bali, seolah-olah membuat wisatawan seperti di Bali. Pantai ini juga biasa disebut Tanah Lot ala Yogyakarta.",
					"Teras Kaca. Teras Kaca terletak di Gunung Kidul, Yogyakarta. Teras Kaca mengajak wisatawan untuk berdiri di atas teras kaca dengan view pemandangan laut yang begitu memukau.",
					"Bukit Paralayang. merupakan salah satu destinasi wisata alam yang menawarkan keelokan panorama dan pengalaman unik untuk para wisatawan. Terletak di kawasan Parangtritis, Bantul, Bukit Paralayang menyuguhkan panorama alam yang menawan dari ketinggian, dan peluang buat berupaya berolahraga paralayang yang mendebarkan."
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Candi Ijo. Candi Ijo merupakan candi yang letaknya paling tinggi di Yogyakarta yang menyuguhkan alam dan budayanya.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			day4: {
				title: "Hari Keempat",
				activities: [
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Malioboro merupakan salah satu destinasi wisata sangat ikonik di Yogyakarta, Indonesia. Terletak di pusat kota, jalur ini membentang selama kurang lebih 2, 5 km dari Stasiun Tugu sampai ke titik Nol Km Yogyakarta"
				]
			}
		}
	},
	{
		id: "pw-03955",
		title: "Paket Wisata dengan Hotel NAGALOKA",
		slug: "paket-nagaloka-dengan-hotel",
		description: "",
		price: "Rp 2,329,000 – Rp 3,364,000",
		pricing: [
			{
				pax: 2,
				price: "Rp 3,364,000",
				label: "2 Orang"
			},
			{
				pax: 4,
				price: "Rp 2,674,000",
				label: "4 Orang"
			},
			{
				pax: 6,
				price: "Rp 2,329,000",
				label: "6 Orang"
			}
		],
		rating: 4.9,
		reviews: 110,
		image: "https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
		images: [
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp",
			"https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp",
			"https://liburanjogja.b-cdn.net/Air-Terjun-Sri-Gethuk.webp"
		],
		location: "Yogyakarta",
		duration: "4-hari-3-malam",
		category: "dengan-hotel",
		highlights: [
			"Hotel",
			"Mobil",
			"Driver",
			"BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		itinerary: {
			day1: {
				title: "Hari Pertama",
				activities: [
					"Gereja Ayam. Gereja Ayam merupakan wisata religi dengan pemandangan alam yang Indah. Wisata religi yang berada di bukit Rhema ini dibangun di daerah Borobudur, Magelang. Selain keindahan alamnya yang memukau, arsitektur Gereja Ayam juga menarik, dimana bentuknya menyerupai kepala burung Merpati yang memiliki mahkota diatasnya.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara."
				]
			},
			day2: {
				title: "Hari Kedua",
				activities: [
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Air Terjun Sri Gethuk. Air Terjun Sri Gethuk terletak di tepi Sungai Oyo yang mengalir deras di ketinggian 50 meter. Air Terjun ini dikenal sebagai air terjun yang tidak pernah kering.",
					"Pantai Ngobaran. Meskipun tidak begitu luas, Pantai Ngobaran memiliki daya tarik yang unik, yaitu bangunan pura dan patung-patung yang mirip di seperti pantai yang ada di Bali. Disana juga menyewakan pakaian Bali, seolah-olah membuat wisatawan seperti di Bali. Pantai ini juga biasa disebut Tanah Lot ala Yogyakarta.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			day3: {
				title: "Hari Ketiga",
				activities: [
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. ",
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam."
				]
			},
			day4: {
				title: "Hari Keempat",
				activities: [
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia.",
					"Sungai Mudal. Sungai Mudal adalah Ekowisata yang berlokasi di Kulon Progo. Pengunjung akan dimanjakan dengan spot Kawasan yang hijau nan asri. Sungai Mudal menyuguhkan sungai dengan air yang jernih dan bisa dipakai untuk berenang. Tidak hanya menyuguhkan sungainya saja, disana juga dilengkapi dengan wahana outbound dan spot-spot yang cocok untuk bersantai.",
					"Kebun Teh Nglinggo. Kebun Teh Nglinggo terletak di Kulon Progo. Kebun Teh Nglinggo menyuguhkan pemandangan alam dengan deretan kebun teh yang luas dan indah.",
					"Pule Payung. Pule Payung merupakan destinasi wisata yang terletak di Kulonprogo. Letaknya yang di ketinggian sehingga Pule Payung memiliki panorama alam yang maraik bagi wisatawan. Pule Payung menyuguhkan pesona perbukitan Menoreh dan indahnya pemandangan Waduk Sermo yang dilihat dari ketinggian. Banyak Spot-spot foto yang tersedia di Pule Payung yang menjadi daya tariknya juga."
				]
			}
		}
	}
];

const paketWisata = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: paketWisataData
});

const _slug_$2 = defineEventHandler((event) => {
  const slug = event.context.params.slug;
  if (!slug) {
    return { error: "Slug is required" };
  }
  const paketWisata = paketWisataData.find((item) => item.slug === slug);
  if (!paketWisata) {
    return { error: "Paket wisata not found" };
  }
  return paketWisata;
});

const _slug_$3 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _slug_$2
});

const _category_ = defineEventHandler((event) => {
  const category = event.context.params.category;
  if (!category) {
    return { error: "Category is required" };
  }
  const filteredData = paketWisataData.filter((item) => item.category === category);
  return filteredData;
});

const _category_$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _category_
});

const _slug_ = defineEventHandler((event) => {
  const slug = event.context.params.slug;
  if (!slug) {
    return { error: "Slug is required" };
  }
  const paketWisata = paketWisataData.find((item) => item.slug === slug);
  if (!paketWisata) {
    return { error: "Paket wisata not found" };
  }
  return paketWisata;
});

const _slug_$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _slug_
});

const _duration_ = defineEventHandler((event) => {
  const duration = event.context.params.duration;
  if (!duration) {
    return { error: "Duration is required" };
  }
  const filteredData = paketWisataData.filter((item) => item.duration === duration);
  return filteredData;
});

const _duration_$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: _duration_
});

const index = defineEventHandler((event) => {
  return paketWisataData;
});

const index$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: index
});

const paketGathering_get = defineEventHandler(async (event) => {
  try {
    const gatheringData = await Promise.resolve().then(function () { return gathering$1; });
    const { gatheringPackages } = gatheringData.default || gatheringData;
    const urls = gatheringPackages.map((paket) => ({
      loc: `/paket-gathering/${paket.slug}/`,
      lastmod: (/* @__PURE__ */ new Date()).toISOString(),
      changefreq: "weekly",
      priority: 0.8
    }));
    return urls;
  } catch (error) {
    console.error("Error generating paket gathering sitemap:", error);
    return [];
  }
});

const paketGathering_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: paketGathering_get
});

const paketHoneymoon_get = defineEventHandler(async (event) => {
  try {
    const honeymoonData = await Promise.resolve().then(function () { return honeymoon$1; });
    const { honeymoonPackages } = honeymoonData.default || honeymoonData;
    const urls = honeymoonPackages.map((paket) => ({
      loc: `/paket-honeymoon/${paket.slug}/`,
      lastmod: (/* @__PURE__ */ new Date()).toISOString(),
      changefreq: "weekly",
      priority: 0.8
    }));
    return urls;
  } catch (error) {
    console.error("Error generating paket honeymoon sitemap:", error);
    return [];
  }
});

const paketHoneymoon_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: paketHoneymoon_get
});

const paketWisata_get = defineEventHandler(async (event) => {
  try {
    const paketWisataData = await Promise.resolve().then(function () { return paketWisata; });
    const paketWisata$1 = paketWisataData.default || paketWisataData;
    const urls = paketWisata$1.map((paket) => ({
      loc: `/paket-wisata-jogja/${paket.slug}/`,
      lastmod: (/* @__PURE__ */ new Date()).toISOString(),
      changefreq: "weekly",
      priority: 0.8
    }));
    return urls;
  } catch (error) {
    console.error("Error generating paket wisata sitemap:", error);
    return [];
  }
});

const paketWisata_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: paketWisata_get
});

const sewaMobil_get = defineEventHandler(async (event) => {
  try {
    const sewaMobilData = await Promise.resolve().then(function () { return sewaMobil$2; });
    const { sewaMobil } = sewaMobilData.default || sewaMobilData;
    const urls = sewaMobil.map((mobil) => ({
      loc: `/sewa-mobil-jogja/${mobil.slug}/`,
      lastmod: (/* @__PURE__ */ new Date()).toISOString(),
      changefreq: "weekly",
      priority: 0.8
    }));
    return urls;
  } catch (error) {
    console.error("Error generating sewa mobil sitemap:", error);
    return [];
  }
});

const sewaMobil_get$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: sewaMobil_get
});

function renderPayloadResponse(ssrContext) {
  return {
    body: stringify(splitPayload(ssrContext).payload, ssrContext._payloadReducers) ,
    statusCode: getResponseStatus(ssrContext.event),
    statusMessage: getResponseStatusText(ssrContext.event),
    headers: {
      "content-type": "application/json;charset=utf-8" ,
      "x-powered-by": "Nuxt"
    }
  };
}
function renderPayloadJsonScript(opts) {
  const contents = opts.data ? stringify(opts.data, opts.ssrContext._payloadReducers) : "";
  const payload = {
    "type": "application/json",
    "innerHTML": contents,
    "data-nuxt-data": appId,
    "data-ssr": !(opts.ssrContext.noSSR)
  };
  {
    payload.id = "__NUXT_DATA__";
  }
  if (opts.src) {
    payload["data-src"] = opts.src;
  }
  const config = uneval(opts.ssrContext.config);
  return [
    payload,
    {
      innerHTML: `window.__NUXT__={};window.__NUXT__.config=${config}`
    }
  ];
}
function splitPayload(ssrContext) {
  const { data, prerenderedAt, ...initial } = ssrContext.payload;
  return {
    initial: { ...initial, prerenderedAt },
    payload: { data, prerenderedAt }
  };
}

const renderSSRHeadOptions = {"omitLineBreaks":false};

globalThis.__buildAssetsURL = buildAssetsURL;
globalThis.__publicAssetsURL = publicAssetsURL;
const HAS_APP_TELEPORTS = !!(appTeleportAttrs.id);
const APP_TELEPORT_OPEN_TAG = HAS_APP_TELEPORTS ? `<${appTeleportTag}${propsToString(appTeleportAttrs)}>` : "";
const APP_TELEPORT_CLOSE_TAG = HAS_APP_TELEPORTS ? `</${appTeleportTag}>` : "";
const PAYLOAD_URL_RE = /^[^?]*\/_payload.json(?:\?.*)?$/ ;
const renderer = defineRenderHandler(async (event) => {
  const nitroApp = useNitroApp();
  const ssrError = event.path.startsWith("/__nuxt_error") ? getQuery$1(event) : null;
  if (ssrError && !("__unenv__" in event.node.req)) {
    throw createError({
      statusCode: 404,
      statusMessage: "Page Not Found: /__nuxt_error"
    });
  }
  const ssrContext = createSSRContext(event);
  const headEntryOptions = { mode: "server" };
  ssrContext.head.push(appHead, headEntryOptions);
  if (ssrError) {
    ssrError.statusCode &&= Number.parseInt(ssrError.statusCode);
    setSSRError(ssrContext, ssrError);
  }
  const isRenderingPayload = PAYLOAD_URL_RE.test(ssrContext.url);
  if (isRenderingPayload) {
    const url = ssrContext.url.substring(0, ssrContext.url.lastIndexOf("/")) || "/";
    ssrContext.url = url;
    event._path = event.node.req.url = url;
  }
  const routeOptions = getRouteRules(event);
  if (routeOptions.ssr === false) {
    ssrContext.noSSR = true;
  }
  const renderer = await getRenderer(ssrContext);
  const _rendered = await renderer.renderToString(ssrContext).catch(async (error) => {
    if (ssrContext._renderResponse && error.message === "skipping render") {
      return {};
    }
    const _err = !ssrError && ssrContext.payload?.error || error;
    await ssrContext.nuxt?.hooks.callHook("app:error", _err);
    throw _err;
  });
  const inlinedStyles = [];
  await ssrContext.nuxt?.hooks.callHook("app:rendered", { ssrContext, renderResult: _rendered });
  if (ssrContext._renderResponse) {
    return ssrContext._renderResponse;
  }
  if (ssrContext.payload?.error && !ssrError) {
    throw ssrContext.payload.error;
  }
  if (isRenderingPayload) {
    const response = renderPayloadResponse(ssrContext);
    return response;
  }
  const NO_SCRIPTS = routeOptions.noScripts;
  const { styles, scripts } = getRequestDependencies(ssrContext, renderer.rendererContext);
  if (ssrContext._preloadManifest && !NO_SCRIPTS) {
    ssrContext.head.push({
      link: [
        { rel: "preload", as: "fetch", fetchpriority: "low", crossorigin: "anonymous", href: buildAssetsURL(`builds/meta/${ssrContext.runtimeConfig.app.buildId}.json`) }
      ]
    }, { ...headEntryOptions, tagPriority: "low" });
  }
  if (inlinedStyles.length) {
    ssrContext.head.push({ style: inlinedStyles });
  }
  const link = [];
  for (const resource of Object.values(styles)) {
    if ("inline" in getQuery(resource.file)) {
      continue;
    }
    link.push({ rel: "stylesheet", href: renderer.rendererContext.buildAssetsURL(resource.file), crossorigin: "" });
  }
  if (link.length) {
    ssrContext.head.push({ link }, headEntryOptions);
  }
  if (!NO_SCRIPTS) {
    ssrContext.head.push({
      link: getPreloadLinks(ssrContext, renderer.rendererContext)
    }, headEntryOptions);
    ssrContext.head.push({
      link: getPrefetchLinks(ssrContext, renderer.rendererContext)
    }, headEntryOptions);
    ssrContext.head.push({
      script: renderPayloadJsonScript({ ssrContext, data: ssrContext.payload }) 
    }, {
      ...headEntryOptions,
      // this should come before another end of body scripts
      tagPosition: "bodyClose",
      tagPriority: "high"
    });
  }
  if (!routeOptions.noScripts) {
    ssrContext.head.push({
      script: Object.values(scripts).map((resource) => ({
        type: resource.module ? "module" : null,
        src: renderer.rendererContext.buildAssetsURL(resource.file),
        defer: resource.module ? null : true,
        // if we are rendering script tag payloads that import an async payload
        // we need to ensure this resolves before executing the Nuxt entry
        tagPosition: "head",
        crossorigin: ""
      }))
    }, headEntryOptions);
  }
  const { headTags, bodyTags, bodyTagsOpen, htmlAttrs, bodyAttrs } = await renderSSRHead(ssrContext.head, renderSSRHeadOptions);
  const htmlContext = {
    htmlAttrs: htmlAttrs ? [htmlAttrs] : [],
    head: normalizeChunks([headTags]),
    bodyAttrs: bodyAttrs ? [bodyAttrs] : [],
    bodyPrepend: normalizeChunks([bodyTagsOpen, ssrContext.teleports?.body]),
    body: [
      replaceIslandTeleports(ssrContext, _rendered.html) ,
      APP_TELEPORT_OPEN_TAG + (HAS_APP_TELEPORTS ? joinTags([ssrContext.teleports?.[`#${appTeleportAttrs.id}`]]) : "") + APP_TELEPORT_CLOSE_TAG
    ],
    bodyAppend: [bodyTags]
  };
  await nitroApp.hooks.callHook("render:html", htmlContext, { event });
  return {
    body: renderHTMLDocument(htmlContext),
    statusCode: getResponseStatus(event),
    statusMessage: getResponseStatusText(event),
    headers: {
      "content-type": "text/html;charset=utf-8",
      "x-powered-by": "Nuxt"
    }
  };
});
function normalizeChunks(chunks) {
  return chunks.filter(Boolean).map((i) => i.trim());
}
function joinTags(tags) {
  return tags.join("");
}
function joinAttrs(chunks) {
  if (chunks.length === 0) {
    return "";
  }
  return " " + chunks.join(" ");
}
function renderHTMLDocument(html) {
  return `<!DOCTYPE html><html${joinAttrs(html.htmlAttrs)}><head>${joinTags(html.head)}</head><body${joinAttrs(html.bodyAttrs)}>${joinTags(html.bodyPrepend)}${joinTags(html.body)}${joinTags(html.bodyAppend)}</body></html>`;
}

const renderer$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: renderer
});

var gatheringPackages = [
	{
		id: "gt-001",
		title: "Paket Gathering Jogja JAYANTAKA",
		slug: "paket-gathering-jogja-jayantaka",
		description: "Paket Gathering Jogja 2Hari 1Malam.",
		price: "Rp 1.650.000 – Rp 1.760.000",
		pricing: [
			{
				pax: "20-25",
				price: "Rp 1,760,000",
				label: "20-25 Orang"
			},
			{
				pax: "30-40",
				price: "Rp 1,650,000",
				label: "30-40 Orang"
			}
		],
		originalPrice: "",
		rating: 4.7,
		reviews: 27,
		image: "https://liburanjogja.b-cdn.net/Lava-Tour.webp",
		images: [
			"https://liburanjogja.b-cdn.net/goa-pindul.webp",
			"https://liburanjogja.b-cdn.net/tempat-wisata-jalan-malioboro-yogyakarta-scaled.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/Heha-Sky-View.webp"
		],
		location: "Yogyakarta",
		duration: "2 Hari 1 Malam",
		category: "gathering",
		capacity: "20-50 orang",
		highlights: [
			"menyusuri sungai di dalam goa",
			"menyusuri Kawasan Goa",
			"off-road",
			"outbound",
			"menyasikan kesenian dan budaya",
			"Gala Dinner,"
		],
		itinerary: [
			{
				day: 1,
				title: "Hari Pertama",
				activities: [
					"Goa Pindul. Goa Pindul merupakan salah satu Goa yang banyak diminati wisatawan karena atraksinya yang menarik. Wisatawan bisa menikmati keindahan alam dengan rafting, menyusuri goa atau cave tubing Pindul, menyusuri sungai di dalam goa, menyusuri goa kristal, menyusuri Kawasan Goa Pondul denagn mobil atau off-road, outbound, menyasikan kesenian dan budaya.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View.",
					"Gala Dinner, Kegiatan ini bukan hanya makan malam biasa. Ini adalah acara mewah yang biasanya diadakan untuk tujuan merayakan momen istimewa, memperingati prestasi tertentu, atau memperkuat hubungan bisnis. Konsepnya melibatkan makan malam formal dengan suasana yang lebih eksklusif dan meriah dengan sentuhan hiburan dan pertunjukan khusus seperti live music."
				]
			},
			{
				day: 2,
				title: "Hari Kedua",
				activities: [
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara.",
					"Malioboro merupakan salah satu destinasi wisata sangat ikonik di Yogyakarta, Indonesia. Terletak di pusat kota, jalur ini membentang selama kurang lebih 2, 5 km dari Stasiun Tugu sampai ke titik Nol Km Yogyakarta."
				]
			}
		],
		inclusions: [
			"Transportasi",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Hotel",
			"Gala Dinner",
			"Dokumentasiicate",
			"Dokumentasi kegiatan"
		],
		exclusions: [
			"Pengeluaran Pribadi"
		],
		facilities: [
			"Transportasi",
			"Gala Dinner",
			"Dokumentasi kegiatan",
			"Hotel"
		]
	},
	{
		id: "gt-002",
		title: "Paket Gathering Jogja BHAGAWANTA",
		slug: "paket-gathering-jogja-bhagawanta",
		description: "Paket Gathering Jogja 2Hari 1Malam.",
		price: "Rp 1,690,000 – Rp 1,950,000",
		pricing: [
			{
				pax: "20-25",
				price: "Rp 1,950,000",
				label: "20-25 Orang"
			},
			{
				pax: "30-40",
				price: "Rp 1,690,000",
				label: "30-40 Orang"
			}
		],
		originalPrice: "",
		rating: 4.6,
		reviews: 63,
		image: "https://liburanjogja.b-cdn.net/Jeep%20Gumuk%20Pasir.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Heha-Sky-View.webp",
			"https://liburanjogja.b-cdn.net/goa-pindul.webp",
			"https://liburanjogja.b-cdn.net/Obelix-Hills.webp",
			"https://liburanjogja.b-cdn.net/pantai-parangtritis-1.webp",
			"https://liburanjogja.b-cdn.net/Jeep%20Gumuk%20Pasir.webp"
		],
		location: "Yogyakarta",
		duration: "2 Hari 1 Malam",
		category: "gathering",
		capacity: "20-50 orang",
		highlights: [
			"Outbound",
			"menyusuri Kawasan Goa",
			"off-road",
			"rafting",
			"menyusuri goa atau cave tubing Pindul",
			"Gala Dinner,"
		],
		itinerary: [
			{
				day: 1,
				title: "Hari Pertama",
				activities: [
					"Fun Games adalah serangkaian program dan kegiatan Outbound yang bersifat ceria, sebagai sarana mengasah keberanian, tanggung jawan, kerja keras, kekompakan, serta sebagai sarana penyegaran dan mampu mencairkan suasanan melalui serangkaian aktivitasnya. Fun games memiliki tujuan untuk meningkatkan produktivitas kerja seperti halnya membangun kekompakan, menjalin kedekatan dan menyatukan kekuatan.",
					"Goa Pindul. Goa Pindul merupakan salah satu Goa yang banyak diminati wisatawan karena atraksinya yang menarik. Wisatawan bisa menikmati keindahan alam dengan rafting, menyusuri goa atau cave tubing Pindul, menyusuri sungai di dalam goa, menyusuri goa kristal, menyusuri Kawasan Goa Pondul denagn mobil atau off-road, outbound, menyasikan kesenian dan budaya.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View.",
					"Gala Dinner, Kegiatan ini bukan hanya makan malam biasa. Ini adalah acara mewah yang biasanya diadakan untuk tujuan merayakan momen istimewa, memperingati prestasi tertentu, atau memperkuat hubungan bisnis. Konsepnya melibatkan makan malam formal dengan suasana yang lebih eksklusif dan meriah dengan sentuhan hiburan dan pertunjukan khusus seperti live music."
				]
			},
			{
				day: 2,
				title: "Hari Kedua",
				activities: [
					"Jeep Gumuk Pasir Destinasi Wisata Gumuk Pasir menyediakan aktivitas yang explore pantai-pantai sekitar Gumuk Pasir menggunakan jeep. Wisatawan akan diajak beraktivitas fun offroad yang tentunya sangat menantang namun masih tetap aman.",
					"Pantai Parangtritis. Pantai Parangtritis merupakan pantai paling ikonik di Yogyakarta. Tidak hanya sebagai tempat untuk menikmati keindahan alam, pantai Parangtritis juga memiliki nilai sejarah dan kebudayaan yang kuat. Wisatawan dapat menikmati kegiatan yang beragam, mulai dari menaiki delman atau andong tradisional, berkuda di sepanjang pantai, mencoba makanan khas Yogyakarta di warung-warung pinggir pantai.",
					"Obelix Sea View. Obelix Sea View. berada di dataran tinggi sehingga wisatawan akan disuguhkan pemandangan pantai berwarna biru. sealin itu, terdapat berbagai fasilitas yang dapat dinikmati wisatawan seperti spot foto menarik hingga kuliner ala Perancis."
				]
			}
		],
		inclusions: [
			"Transportasi",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Hotel",
			"Gala Dinner",
			"Dokumentasiicate",
			"Dokumentasi kegiatan"
		],
		exclusions: [
			"Pengeluaran Pribadi"
		],
		facilities: [
			"Transportasi",
			"Gala Dinner",
			"Dokumentasi kegiatan",
			"Hotel"
		]
	},
	{
		id: "gt-003",
		title: "Paket Gathering Jogja PADMANA",
		slug: "paket-gathering-jogja-padmana",
		description: "Paket Gathering Jogja 3Hari 2Malam.",
		price: "Rp 2,450,000 – Rp 2,650,000",
		pricing: [
			{
				pax: "20-25",
				price: "Rp 2,650,000",
				label: "20-25 Orang"
			},
			{
				pax: "30-40",
				price: "Rp 2,450,000",
				label: "30-40 Orang"
			}
		],
		originalPrice: "",
		rating: 4.9,
		reviews: 12,
		image: "https://liburanjogja.b-cdn.net/hutan-pinus-mangunan.webp",
		images: [
			"https://liburanjogja.b-cdn.net/pantai-ngandong.jpg",
			"https://liburanjogja.b-cdn.net/hutan-pinus-mangunan.webp",
			"https://liburanjogja.b-cdn.net/Heha-Sky-View.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/keraton-jogja.webp"
		],
		location: "Yogyakarta",
		duration: "3 Hari 2 Malam",
		category: "gathering",
		capacity: "20-40 orang",
		highlights: [
			"menyusuri sungai di dalam goa",
			"menyusuri Kawasan Goa",
			"off-road",
			"outbound",
			"menyasikan kesenian dan budaya",
			"Gala Dinner,"
		],
		itinerary: [
			{
				day: 1,
				title: "Hari Pertama",
				activities: [
					"Pantai Ngandong. Pantai Ngandong. Pantai Ngandong memiliki pemandangan yang sangat eksotis dengan air laut yang biru jernih. Pantai Ngandong adalah salah satu spot sunset di Gunung Kidul. Wisatawan juga dapat menikmati pantai dengan menyewa perahu yang disediakan oleh para nelayan. Pantai Ngandong juga menyediakan jetsky dan snorkelling.",
					"Hutan Pinus Mangunan. Daya Tarik Hutan Pinus Mangunan adalah banyaknya pohon pinus yang tinggi dan rindang, sehingga membuat suasanya menjadi sejuk dan nyaman. Udara yang bersih dan segar cocok untuk wisatawan yang penat akan bising dan panasnya suasana kota.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			},
			{
				day: 2,
				title: "Hari Kedua",
				activities: [
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Gala Dinner, Kegiatan ini bukan hanya makan malam biasa. Ini adalah acara mewah yang biasanya diadakan untuk tujuan merayakan momen istimewa, memperingati prestasi tertentu, atau memperkuat hubungan bisnis. Konsepnya melibatkan makan malam formal dengan suasana yang lebih eksklusif dan meriah dengan sentuhan hiburan dan pertunjukan khusus seperti live music."
				]
			},
			{
				day: 3,
				title: "Hari Ketiga",
				activities: [
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam."
				]
			}
		],
		inclusions: [
			"Transportasi",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Hotel",
			"Gala Dinner",
			"Dokumentasiicate",
			"Dokumentasi kegiatan"
		],
		exclusions: [
			"Pengeluaran Pribadi"
		],
		facilities: [
			"Transportasi",
			"Gala Dinner",
			"Dokumentasi kegiatan",
			"Hotel"
		]
	},
	{
		id: "gt-004",
		title: "Paket Gathering Jogja ANANTA",
		slug: "paket-gathering-jogja-ananta",
		description: "Paket Gathering Jogja 3Hari 2Malam.",
		price: "Rp 2,550,000 – Rp 2,799,000",
		pricing: [
			{
				pax: "20-25",
				price: "Rp 2,799,000",
				label: "20-25 Orang"
			},
			{
				pax: "30-40",
				price: "Rp 2,550,000",
				label: "30-40 Orang"
			}
		],
		originalPrice: "",
		rating: 4.8,
		reviews: 19,
		image: "https://liburanjogja.b-cdn.net/pantai-indrayanti-1.webp",
		images: [
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/pantai-indrayanti-1.webp",
			"https://liburanjogja.b-cdn.net/Heha-Sky-View.webp",
			"https://liburanjogja.b-cdn.net/studio-alam-Gamplong.webp",
			"https://liburanjogja.b-cdn.net/Rafting%20Sungai%20Elo.webp"
		],
		location: "Yogyakarta",
		duration: "3 Hari 2 Malam",
		category: "gathering",
		capacity: "20-40 orang",
		highlights: [
			"menyusuri sungai di dalam goa",
			"menyusuri Kawasan Goa",
			"off-road",
			"outbound",
			"menyasikan kesenian dan budaya",
			"Gala Dinner,"
		],
		itinerary: [
			{
				day: 1,
				title: "Hari Pertama",
				activities: [
					"Fun Games adalah serangkaian program dan kegiatan Outbound yang bersifat ceria, sebagai sarana mengasah keberanian, tanggung jawan, kerja keras, kekompakan, serta sebagai sarana penyegaran dan mampu mencairkan suasanan melalui serangkaian aktivitasnya. Fun games memiliki tujuan untuk meningkatkan produktivitas kerja seperti halnya membangun kekompakan, menjalin kedekatan dan menyatukan kekuatan.",
					"Rafting Sungai Elo. rafting Sungai elo merupakan destinasi wisata yang cocok bagi wisatawan yang sedang berlibur di Magelang atau pun di Jogja. Rafting Sungai Elo memiliki tingkat kesulitan II-III yang artinya memiliki arus yang tidak begitu deras tetapi cukup memacu adrenalin. menyusuri sungai Elo dengan perahu karet dan jarak tempuh 3-4 jam wisatawan akan menemukan pengalaman yang tak akan terlupakan.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"obelix Village Destinasi ini menjadi salah satu tempat wisata di Jogja yang cocok untuk keluarga karena memiliki banyak wahana bagi semua usia. Obelix Village menggabungkan konsep little zoo, mini farm, flower garden, river deck, dan resto dalam satu lokasi."
				]
			},
			{
				day: 2,
				title: "Hari Kedua",
				activities: [
					"Pantai Indrayanti. Pantai Indrayanti merupakan salah satu orimadona wisata di Gunung Kidul. pantai ini memiliki air yang jernih dengan pasirnya yang berwarna putih. pemandangannya begitu cantik. Pantai Indrayanti cocok untuk menikmati Sunrise dan Sunset. terdapat bukti-bukti di sepanjang garis pantai, kebersihan yang terjaga sehingga menambah panorama Pantai Indrayanti sangat menawan.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View.",
					"Gala Dinner, Kegiatan ini bukan hanya makan malam biasa. Ini adalah acara mewah yang biasanya diadakan untuk tujuan merayakan momen istimewa, memperingati prestasi tertentu, atau memperkuat hubungan bisnis. Konsepnya melibatkan makan malam formal dengan suasana yang lebih eksklusif dan meriah dengan sentuhan hiburan dan pertunjukan khusus seperti live music."
				]
			},
			{
				day: 3,
				title: "Hari Ketiga",
				activities: [
					"Sepedaan @KulonProgo. Kulon Progo menjadi salah satu destinasi yang menawarkan wisata sepeda. Dengan pemandangan hamparan sawah luas dengan latar belakang perbukitan Menoreh yang cantic, banyak wisatawan yang berminat mencoba atraksi bersepeda ini untuk melepas penat kehidupan Kota. Selain lokasinya yang asri, treknya juga cocok untuk bersepeda, kondisi jalan bagus dan tidak terlalu jauh, sehingga cocok untuk sekedar refreshing.",
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia."
				]
			}
		],
		inclusions: [
			"Transportasi",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Hotel",
			"Gala Dinner",
			"Dokumentasiicate",
			"Dokumentasi kegiatan"
		],
		exclusions: [
			"Pengeluaran Pribadi"
		],
		facilities: [
			"Transportasi",
			"Gala Dinner",
			"Dokumentasi kegiatan",
			"Hotel"
		]
	}
];
var gatheringDestinations = [
	{
		name: "Candi Borobudur",
		description: "Wisata edukasi sejarah dan budaya yang menginspirasi",
		image: "https://liburanjogja.b-cdn.net/candi-borobudur-7.webp"
	},
	{
		name: "Taman Sari",
		description: "Istana air bersejarah dengan arsitektur romantis",
		image: "https://liburanjogja.b-cdn.net/gambar-taman-sari-destinasi-liburan-budaya-di-jogja.webp"
	},
	{
		name: "Merapi Adventure",
		description: "Lokasi Gathering dan Outbound menantang",
		image: "https://liburanjogja.b-cdn.net/gunung-merapi-jogja.jpg"
	},
	{
		name: "Keraton Yogyakarta",
		description: "Wisata budaya untuk memahami nilai-nilai kepemimpinan",
		image: "https://liburanjogja.b-cdn.net/keraton-jogja.webp"
	}
];
var gatheringTestimonials = [
	{
		id: 1,
		name: "PT. Maju Bersama",
		location: "Jakarta",
		rating: 5,
		comment: "Program gathering terbaik yang pernah kami ikuti! Team building activities sangat menarik dan efektif untuk mempererat hubungan antar karyawan. Facilitator sangat profesional dan venue yang dipilih sangat mendukung.",
		image: "https://images.unsplash.com/photo-1511632765486-a01980e01a18?q=80&w=100&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
		date: "Desember 2024"
	},
	{
		id: 2,
		name: "CV. Sukses Mandiri",
		location: "Surabaya",
		rating: 5,
		comment: "Sangat puas dengan pelayanan dan program yang diberikan. Leadership training sangat bermanfaat untuk pengembangan tim. Destinasi wisata edukatif juga menambah wawasan karyawan. Highly recommended!",
		image: "https://images.unsplash.com/photo-1517486808906-6ca8b3f04846?q=80&w=100&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
		date: "November 2024"
	},
	{
		id: 3,
		name: "PT. Inovasi Digital",
		location: "Bandung",
		rating: 5,
		comment: "Program gathering yang sangat terorganisir dengan baik. Adventure team building di Merapi sangat menantang dan membangun kekompakan tim. Cultural immersion program juga memberikan pengalaman yang tak terlupakan!",
		image: "https://images.unsplash.com/photo-1552664730-d307ca884978?q=80&w=100&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
		date: "Oktober 2024"
	}
];
const gathering = {
	gatheringPackages: gatheringPackages,
	gatheringDestinations: gatheringDestinations,
	gatheringTestimonials: gatheringTestimonials
};

const gathering$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: gathering,
  gatheringDestinations: gatheringDestinations,
  gatheringPackages: gatheringPackages,
  gatheringTestimonials: gatheringTestimonials
});

var honeymoonPackages = [
	{
		id: "hm-001",
		title: "Paket Honeymoon Jogja Ramayana",
		slug: "paket-honeymoon-ramayana",
		description: "Rayakan momen spesial pernikahan Anda dengan paket honeymoon romantis di Jogja selama 2 hari 1 malam. Nikmati suasana romantis dengan berbagai fasilitas mewah dan destinasi eksotis yang tak terlupakan.",
		price: "Rp 6.072.000",
		originalPrice: "",
		rating: 5,
		reviews: 45,
		image: "https://liburanjogja.b-cdn.net/gambar-taman-sari-destinasi-liburan-budaya-di-jogja.webp",
		images: [
			"https://liburanjogja.b-cdn.net/candi-borobudur-6.webp",
			"https://liburanjogja.b-cdn.net/keraton-jogja.webp",
			"https://liburanjogja.b-cdn.net/gambar-taman-sari-destinasi-liburan-budaya-di-jogja.webp",
			"https://liburanjogja.b-cdn.net/Obelix-Hills.webp"
		],
		location: "Yogyakarta",
		duration: "2 Hari 1 Malam",
		category: "honeymoon",
		highlights: [
			"2Hari 1Malam",
			"Romantic dinner @Chandari Heaven"
		],
		itinerary: [
			{
				day: 1,
				title: "",
				activities: [
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"VW Tour Borobudur, Atraksi wisata yang menawarkan berkeliling di desa dan spot wisata di Sekitar Candi Borobudur dengan kendaraan Volk Wagen Klasik. ",
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Candi Ijo. Candi Ijo merupakan candi yang letaknya paling tinggi di Yogyakarta yang menyuguhkan alam dan budayanya.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam."
				]
			}
		],
		inclusions: [
			"Hotel",
			"Mobil, Driver dan BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		exclusions: [
			" Makan dan Minuman",
			"Pengeluaran Pribadi"
		],
		facilities: [
			"2Hari 1Malam",
			"Romantic dinner @Chandari Heaven"
		]
	},
	{
		id: "hm-002",
		title: "Paket Honeymoon Jogja Mahabarata",
		slug: "paket-honeymoon-mahabrata",
		description: "Paket Honeymoon Yogyakarta Untuk Couple 2Hari 1Malam Romantic dinner @Pasta Banget signature.",
		price: "Rp 4.266.000",
		originalPrice: "",
		rating: 4.8,
		reviews: 40,
		image: "https://liburanjogja.b-cdn.net/Heha-Sky-View.webp",
		images: [
			"https://liburanjogja.b-cdn.net/keraton-jogja.webp",
			"https://liburanjogja.b-cdn.net/gambar-taman-sari-destinasi-liburan-budaya-di-jogja.webp",
			"https://liburanjogja.b-cdn.net/Obelix-Hills.webp",
			"https://liburanjogja.b-cdn.net/Heha-Sky-View.webp"
		],
		location: "Yogyakarta",
		duration: "2 Hari 1 Malam",
		category: "honeymoon",
		highlights: [
			"2Hari 1Malam",
			"@Pasta Banget signature"
		],
		itinerary: [
			{
				day: 1,
				title: "",
				activities: [
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara.",
					"Obelix Hills. Obelix Hills memiliki pemandangan sunset yang memukai dan 30 spot foto. Obelix Hills juga terdapat resto yang menyajikan masakan nusantara. Tidak heran kalau tempat ini menjadi incaran wisatawan untuk menikmati suasana sore hari sambal menyaksikan matahari terbenam.",
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Heha Sky View. Menikmati pemandangan kota Yogyakarta dari ketinggian merupakan salah satu pengalaman wisata yang sayang dilewatkan, salah satu tempat terbaik menikmatinya adalah di Heha Sky View."
				]
			}
		],
		inclusions: [
			"Hotel",
			"Mobil, Driver dan BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		exclusions: [
			" Makan dan Minuman",
			"Pengeluaran Pribadi"
		],
		facilities: [
			"2Hari 1Malam",
			"@Pasta Banget signature"
		]
	},
	{
		id: "hm-003",
		title: "Paket Honeymoon Jogja Anjani",
		slug: "paket-honeymoon-anjani",
		description: "Paket Honeymoon di Jogja Untuk Couple 3Hari 2Malam Romantic Dinner @Abhayagiri.",
		price: "Rp 7.785.000",
		originalPrice: "",
		rating: 4.9,
		reviews: 25,
		image: "https://liburanjogja.b-cdn.net/Benteng-Vredebug.webp",
		images: [
			"https://liburanjogja.b-cdn.net/hutan-pinus-pengger-3.webp",
			"https://liburanjogja.b-cdn.net/Air-Terjun-Sri-Gethuk.webp",
			"https://liburanjogja.b-cdn.net/keraton-jogja.webp",
			"https://liburanjogja.b-cdn.net/gambar-taman-sari-destinasi-liburan-budaya-di-jogja.webp",
			"https://liburanjogja.b-cdn.net/Benteng-Vredebug.webp"
		],
		location: "Yogyakarta",
		duration: "3 Hari 2 Malam",
		category: "honeymoon",
		highlights: [
			"3Hari 2Malam",
			"Romantic Dinner @Abhayagiri"
		],
		itinerary: [
			{
				day: 1,
				title: "",
				activities: [
					"Hutan Pinus Pengger. Hutan Pinus Pengger tidak hanya menyuguhkan pemandangan alam yang indah, melainkan juga berbagai spot foto yang instagramable. Suasana di tempat ini tenang dan sejuk yang jauh dari hiruk pikuk kota. Ditambah lagi dengan pemandangan kerlap-kerlip lampu kota Yogyakarta yang tampak dari kejauhan saat malam hari.",
					"Air Terjun Sri Gethuk. Air Terjun Sri Gethuk terletak di tepi Sungai Oyo yang mengalir deras di ketinggian 50 meter. Air Terjun ini dikenal sebagai air terjun yang tidak pernah kering.",
					"Teras Kaca. Teras Kaca terletak di Gunung Kidul, Yogyakarta. Teras Kaca mengajak wisatawan untuk berdiri di atas teras kaca dengan view pemandangan laut yang begitu memukau.",
					"Gereja Ayam. Gereja Ayam merupakan wisata religi dengan pemandangan alam yang Indah. Wisata religi yang berada di bukit Rhema ini dibangun di daerah Borobudur, Magelang. Selain keindahan alamnya yang memukau, arsitektur Gereja Ayam juga menarik, dimana bentuknya menyerupai kepala burung Merpati yang memiliki mahkota diatasnya.",
					"Candi Borobudur. Merupakan Candi Buddha Terbesar di Indonesia yang terletak di Magelang. Jaraknya sekitar 40km dari Kota Yogyakarta. Candi Borobudur juga di tetapkan menjadi World Heritage oleh UNESCO.",
					"VW Tour Borobudur, Atraksi wisata yang menawarkan berkeliling di desa dan spot wisata di Sekitar Candi Borobudur dengan kendaraan Volk Wagen Klasik. ",
					"Keraton Yogyakarta. Keraton Yogyakarta merupakan peninggalan sejarah dan ikon Kesultanan Yogyakarta yang menjadi daya tarik bagi wisatawan yang berkunjung ke Kota Yogyakarta. Wisatawan akan menemukan banyak hal menarik mulai dari kekayaan budaya hingga nilai-nilai sejarah.",
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Benteng Vredebug, merupakan salah satu wisata sejarah yang ada di Yogyakarta. Letaknya berada di pusat kota. Tempat ini dulunya merupakan Bangunan bersejarah ketika masa penjajahan, dan kini diubah menjadi Museum yang sangat cantik dan banyak spot-spot foto yang aestetic."
				]
			}
		],
		inclusions: [
			"Hotel",
			"Mobil, Driver dan BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		exclusions: [
			" Makan dan Minuman",
			"Pengeluaran Pribadi"
		],
		facilities: [
			"3Hari 2Malam",
			"Romantic Dinner @Abhayagiri"
		]
	},
	{
		id: "hm-004",
		title: "Paket Honeymoon Jogja Srikandi",
		slug: "paket-honeymoon-srikandi",
		description: "Paket Honeymoon Jogja Untuk Couple 3Hari 2Malam Romantic dinner @Pasta Banget signature.",
		price: "Rp 6.279.000",
		originalPrice: "",
		rating: 4.8,
		reviews: 33,
		image: "https://liburanjogja.b-cdn.net/candi-prambanan-1.webp",
		images: [
			"https://liburanjogja.b-cdn.net/Heha-Ocean.webp",
			"https://liburanjogja.b-cdn.net/Lava-Tour.webp",
			"https://liburanjogja.b-cdn.net/Tebing-Breksi.webp",
			"https://liburanjogja.b-cdn.net/gambar-taman-sari-destinasi-liburan-budaya-di-jogja.webp",
			"https://liburanjogja.b-cdn.net/candi-prambanan-1.webp"
		],
		location: "Yogyakarta",
		duration: "3 Hari 2 Malam",
		category: "honeymoon",
		highlights: [
			"3Hari 2Malam",
			"@Pasta Banget signature"
		],
		itinerary: [
			{
				day: 1,
				title: "",
				activities: [
					"Pantai Mesra. Pantai Mesra merupakan salah satu pantai di Gunung Kidul yang memiliki hamparan rumput hijau yang tumbuh subur. Suasana pantai yang romantis sehingga pantai ini dinamakan pantai Mesra.",
					"Teras Kaca. Teras Kaca terletak di Gunung Kidul, Yogyakarta. Teras Kaca mengajak wisatawan untuk berdiri di atas teras kaca dengan view pemandangan laut yang begitu memukau.",
					"Heha Ocean View. Heha ocean View terletak di atas tebing dan wisatawan akan disuguhkan dengan pemandangan laut lepas dengan lebih leluasa. Selain menyuguhkan pemandangan laut yang canti, Heha Ocean View juga dilengkapi dengan spot-spot foto yang instagramable.",
					"Lava Tour wisata Gunung Merapi. Merupakan wisata petualangan gunung Merapi dengan mobil Jeep terbuka. Menikmati keindahan alam di sekitar Gunung Merapi dengan cara yang lebih menantang dan menyenangkan. Wisatawan juga disuguhkan beberapa destinasi yang disinggahi dalam rute jeepnya, seperti Rumah Mbah Maridjan, Bunker Kaliadem, The Lost World Park.",
					"Candi Prambanan. Candi Prambanana merupakan Candi Hindu terbesar di Indonesia yang juga termasuk Situs Warisan Dunia UNESCO. Candi Prmabanan adalah salah satu candi terindah di Asia Tenggara.",
					"Tebing Breksi. Tebing Breksi merupakan batuan kapur yang disulap menjadi destinasi wisata yang menawan. Tebing Breksi ditetapkan sebagai salah satu Geoheritage Yogyakarta karena ditemukan jenis batuan tufan yang langka. Menjulang tinggi sekitar 30 meter, batuan kapur raksasa berlapis-lapis ini sudah dipahat membentuk relief dan patung dari cerita pewayangan.",
					"Studio Alam Gamplong. Studio Alam Gamplong terletak di Dusun Gamplong, sleman. Tempat wisata ini sangat cocok untuk dijadikan spot foto yang instagramable. Studio Alam Gamplong juga sempat dijadikan lokasi syutung beberapa film layer lebar salah satunya yaitu Bumi Manusia.",
					"Taman Sari. Taman Sari berada dekat dengan Keraton Yogyakarta. Dulunya Taman Sari adalah taman istana Keraton Yogyakarta. Tempat ini dikenal sebagai pemandian raja dan para putri. Terdapat 3 buah kolam yang memiliki nama dan fungsinya masing-masing. Masjid di dalam kompleks Taman Sari dengan arsitektur Jawa-portugis sehingga terlihat sangat unik. Terdapat Gedung Kenongo yang merupakan bangunan paling tinggi di Taman Sari yang dahulu dijadikan tempat jamuan kerajaan, sekarang lokasi ini kerap digunakan oleh para pengunjung untuk menikmati Matahari Terbenam.",
					"Belajar Membatik, Selain berbelanja batik, di Yogyakarta wisatawan juga bisa ke destinasi yang mengajak wisatawannya untuk belajar membatik. Wisatawan akan diajak mengenal bagaimana proses pembuatan batik, mulai dari menulis corak batiknya sampai diwarnai dan finishingnya. Hasil dari batik yang ditulis wisatawan juga bisa di bawa pulang untuk souvenir. "
				]
			}
		],
		inclusions: [
			"Hotel",
			"Mobil, Driver dan BBM",
			"Tiket Wisata",
			"Parkir",
			"Air Mineral",
			"Dokumentasi"
		],
		exclusions: [
			" Makan dan Minuman",
			"Pengeluaran Pribadi"
		],
		facilities: [
			"3Hari 2Malam",
			"@Pasta Banget signature"
		]
	}
];
var honeymoonDestinations = [
	{
		name: "Candi Borobudur",
		description: "Candi Buddha terbesar di dunia yang menawarkan sunrise romantis",
		image: "https://liburanjogja.b-cdn.net/candi-borobudur-6.webp"
	},
	{
		name: "Pantai Parangtritis",
		description: "Pantai eksotis dengan sunset romantis dan suasana mistis",
		image: "https://liburanjogja.b-cdn.net/pantai-parangtritis-1.webp"
	},
	{
		name: "Taman Sari",
		description: "Istana air bersejarah dengan arsitektur romantis",
		image: "https://liburanjogja.b-cdn.net/gambar-taman-sari-destinasi-liburan-budaya-di-jogja.webp"
	},
	{
		name: "Malioboro Street",
		description: "Jalan legendaris untuk shopping dan kuliner romantis",
		image: "https://liburanjogja.b-cdn.net/tempat-wisata-jalan-malioboro-yogyakarta-1024x683.webp"
	}
];
var honeymoonTestimonials = [
	{
		id: 1,
		name: "Andi & Sari",
		location: "Jakarta",
		rating: 5,
		comment: "Paket honeymoon terbaik yang pernah kami alami! Setiap detail diperhatikan dengan sempurna. Hotel mewah, makanan lezat, dan pelayanan yang luar biasa. Terima kasih telah membuat honeymoon kami tak terlupakan!",
		image: "https://images.unsplash.com/photo-1522798514-97ceb8c4f1c8?q=80&w=100&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
		date: "Desember 2024"
	},
	{
		id: 2,
		name: "Budi & Maya",
		location: "Surabaya",
		rating: 5,
		comment: "Sangat romantis dan berkesan! Spa couple treatment-nya amazing, candle light dinner di pantai sangat indah. Guide dan driver juga sangat profesional. Highly recommended untuk pasangan yang baru menikah!",
		image: "https://images.unsplash.com/photo-1571896349842-33c89424de2d?q=80&w=100&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
		date: "November 2024"
	},
	{
		id: 3,
		name: "Dimas & Putri",
		location: "Bandung",
		rating: 5,
		comment: "Helicopter tour ke Borobudur benar-benar spektakuler! Villa dengan private pool membuat honeymoon kami semakin spesial. Photographer profesionalnya juga sangat baik, hasil fotonya bagus banget!",
		image: "https://images.unsplash.com/photo-1540541338287-41700207dee6?q=80&w=100&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
		date: "Oktober 2024"
	}
];
const honeymoon = {
	honeymoonPackages: honeymoonPackages,
	honeymoonDestinations: honeymoonDestinations,
	honeymoonTestimonials: honeymoonTestimonials
};

const honeymoon$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: honeymoon,
  honeymoonDestinations: honeymoonDestinations,
  honeymoonPackages: honeymoonPackages,
  honeymoonTestimonials: honeymoonTestimonials
});

var sewaMobil = [
	{
		id: "sm-001",
		name: "Sewa Mobil All New Avanza",
		slug: "sewa-mobil-avanza-jogja",
		category: "mpv-ekonomis",
		description: "MPV terpopuler untuk keluarga dengan kenyamanan dan efisiensi bahan bakar yang baik. Cocok untuk perjalanan wisata keluarga di Jogja.",
		image: "/sewa-mobil-all-new-avanza-yogyakarta.webp",
		capacity: "7 Orang",
		transmission: "Manual/Matic",
		year: "",
		rating: 4.8,
		reviews: 124,
		packages: {
			ms: {
				name: "Mobil + Driver (MS)",
				description: "Mobil dengan driver berpengalaman",
				pricing: {
					"12jam": 500000,
					"24jam": 600000
				}
			},
			msb: {
				name: "Mobil + Driver + BBM (MSB)",
				description: "Mobil dengan driver dan BBM sudah termasuk",
				pricing: {
					"12jam": 650000,
					"24jam": 750000
				}
			},
			aio: {
				name: "All In One (AIO)",
				description: "Mobil + driver + BBM + parkir + makan driver",
				pricing: {
					"12jam": 750000,
					"24jam": 850000
				}
			}
		},
		note: "Pilih paket sesuai kebutuhan perjalanan Anda"
	},
	{
		id: "sm-002",
		name: "Sewa Mobil Grand Avanza",
		slug: "sewa-mobil-grand-avanza-jogja",
		category: "mpv-ekonomis",
		description: "Versi upgrade dari Avanza dengan fitur lebih lengkap dan kenyamanan ekstra untuk perjalanan keluarga.",
		image: "/sewa-mobil-grand-avanza-jogja.webp",
		capacity: "7 Orang",
		transmission: "Manual/Matic",
		year: "",
		rating: 4.9,
		reviews: 98,
		packages: {
			ms: {
				name: "Mobil + Driver (MS)",
				description: "Mobil dengan driver berpengalaman",
				pricing: {
					"12jam": 400000,
					"24jam": 500000
				}
			},
			msb: {
				name: "Mobil + Driver + BBM (MSB)",
				description: "Mobil dengan driver dan BBM sudah termasuk",
				pricing: {
					"12jam": 550000,
					"24jam": 650000
				}
			},
			aio: {
				name: "All In One (AIO)",
				description: "Mobil + driver + BBM + parkir + makan driver",
				pricing: {
					"12jam": 650000,
					"24jam": 750000
				}
			}
		},
		note: "Pilih paket sesuai kebutuhan perjalanan Anda"
	},
	{
		id: "sm-003",
		name: "Sewa Mobil Xpander",
		slug: "sewa-mobil-xpander-jogja",
		category: "mpv-ekonomis",
		description: "MPV modern dengan desain sporty dan interior luas. Pilihan tepat untuk keluarga yang menginginkan kenyamanan dan gaya.",
		image: "/sewa-mobil-xpander-cross-jogja.webp",
		capacity: "7 Orang",
		transmission: "CVT",
		year: "",
		rating: 4.9,
		reviews: 156,
		packages: {
			ms: {
				name: "Mobil + Driver (MS)",
				description: "Mobil dengan driver berpengalaman",
				pricing: {
					"12jam": 600000,
					"24jam": 700000
				}
			},
			msb: {
				name: "Mobil + Driver + BBM (MSB)",
				description: "Mobil dengan driver dan BBM sudah termasuk",
				pricing: {
					"12jam": 700000,
					"24jam": 800000
				}
			},
			aio: {
				name: "All In One (AIO)",
				description: "Mobil + driver + BBM + parkir + makan driver",
				pricing: {
					"12jam": 800000,
					"24jam": 900000
				}
			}
		},
		note: "Pilih paket sesuai kebutuhan perjalanan Anda"
	},
	{
		id: "sm-004",
		name: "Sewa Mobil Hyundai H-1",
		slug: "sewa-mobil-hyundai-h-1-jogja",
		category: "mpv-ekonomis",
		description: "MPV dengan kapasitas besar dan kenyamanan untuk perjalanan keluarga besar atau grup kecil.",
		image: "/sewa-mobil-hyundai-h-1.webp",
		capacity: "8 Orang",
		transmission: "Manual",
		year: "",
		rating: 4.8,
		reviews: 89,
		packages: {
			ms: {
				name: "Mobil + Driver (MS)",
				description: "Mobil dengan driver berpengalaman",
				pricing: {
					"12jam": 600000,
					"24jam": 750000
				}
			},
			msb: {
				name: "Mobil + Driver + BBM (MSB)",
				description: "Mobil dengan driver dan BBM sudah termasuk",
				pricing: {
					"12jam": 800000,
					"24jam": 900000
				}
			},
			aio: {
				name: "All In One (AIO)",
				description: "Mobil + driver + BBM + parkir + makan driver",
				pricing: {
					"12jam": 900000,
					"24jam": 1000000
				}
			}
		},
		note: "Pilih paket sesuai kebutuhan perjalanan Anda"
	},
	{
		id: "sm-005",
		name: "Sewa Mobil All New Innova",
		slug: "sewa-mobil-all-new-innova-jogja",
		category: "mpv-ekonomis",
		description: "MPV legendaris Toyota dengan kenyamanan dan reliabilitas tinggi untuk perjalanan keluarga.",
		image: "/sewa-mobil-all-new-innova-yogyakarta.webp",
		capacity: "7 Orang",
		transmission: "Manual/Matic",
		year: "",
		rating: 4.9,
		reviews: 201,
		packages: {
			ms: {
				name: "Mobil + Driver (MS)",
				description: "Mobil dengan driver berpengalaman",
				pricing: {
					"12jam": 600000,
					"24jam": 700000
				}
			},
			msb: {
				name: "Mobil + Driver + BBM (MSB)",
				description: "Mobil dengan driver dan BBM sudah termasuk",
				pricing: {
					"12jam": 800000,
					"24jam": 900000
				}
			},
			aio: {
				name: "All In One (AIO)",
				description: "Mobil + driver + BBM + parkir + makan driver",
				pricing: {
					"12jam": 900000,
					"24jam": 1000000
				}
			}
		},
		note: "Pilih paket sesuai kebutuhan perjalanan Anda"
	},
	{
		id: "sm-006",
		name: "Sewa Mobil Hyundai H1 Royale",
		slug: "sewa-mobil-hyundai-h1-royale",
		category: "mpv-premium",
		description: "MPV premium dengan interior mewah dan kenyamanan maksimal untuk perjalanan VIP.",
		image: "/sewa-mobil-hyundai-h-royale.webp",
		capacity: "9 Orang",
		transmission: "Automatic",
		year: "",
		rating: 4.9,
		reviews: 156,
		packages: {
			msb: {
				name: "Mobil + Driver + BBM (MSB)",
				description: "Mobil dengan driver dan BBM sudah termasuk",
				pricing: {
					"12jam": 1300000,
					"24jam": 1500000
				}
			},
			aio: {
				name: "All In One (AIO)",
				description: "Mobil + driver + BBM + parkir + makan driver",
				pricing: {
					"12jam": 1500000,
					"24jam": 1700000
				}
			}
		},
		note: "Pilih paket sesuai kebutuhan perjalanan Anda"
	},
	{
		id: "sm-007",
		name: "Sewa Mobil All New Innova Zenix",
		slug: "sewa-mobil-all-new-innova-zenix",
		category: "mpv-premium",
		description: "MPV premium terbaru dengan teknologi hybrid dan kenyamanan maksimal untuk perjalanan jauh",
		image: "/sewa-mobil-all-new-innova-zenix.webp",
		capacity: "7 Orang",
		transmission: "CVT Hybrid",
		year: "",
		rating: 4.9,
		reviews: 156,
		packages: {
			msb: {
				name: "Mobil + Driver + BBM (MSB)",
				description: "Mobil dengan driver dan BBM sudah termasuk",
				pricing: {
					"12jam": 1500000,
					"24jam": 1700000
				}
			},
			aio: {
				name: "All In One (AIO)",
				description: "Mobil + driver + BBM + parkir + makan driver",
				pricing: {
					"12jam": 1600000,
					"24jam": 1800000
				}
			}
		},
		note: "Pilih paket sesuai kebutuhan perjalanan Anda"
	},
	{
		id: "sm-008",
		name: "Sewa Mobil Elf Short 11 Seats",
		slug: "sewa-mobil-elf-short-11-seats",
		category: "minibus",
		description: "Minibus dengan kapasitas 11 orang, cocok untuk grup kecil atau keluarga besar.",
		image: "/sewa-mobil-elf-short-11-seats.webp",
		capacity: "11 Orang",
		transmission: "Manual",
		year: "",
		rating: 4.7,
		reviews: 88,
		packages: {
			msb: {
				name: "Mobil + Driver + BBM (MSB)",
				description: "Mobil dengan driver dan BBM sudah termasuk",
				pricing: {
					"12jam": 900000,
					"24jam": 1000000
				}
			},
			aio: {
				name: "All In One (AIO)",
				description: "Mobil + driver + BBM + parkir + makan driver",
				pricing: {
					"12jam": 1000000,
					"24jam": 1100000
				}
			}
		},
		note: "Pilih paket sesuai kebutuhan perjalanan Anda"
	},
	{
		id: "sm-009",
		name: "Sewa Mobil Elf Long 19 Seats",
		slug: "sewa-mobil-elf-long-19-seats",
		category: "minibus",
		description: "Minibus dengan kapasitas besar untuk rombongan keluarga atau grup wisata hingga 19 orang.",
		image: "/sewa-mobil-elf-long-19-seats-di-jogja.webp",
		capacity: "19 Orang",
		transmission: "Manual",
		year: "",
		rating: 4.8,
		reviews: 112,
		packages: {
			msb: {
				name: "Mobil + Driver + BBM (MSB)",
				description: "Mobil dengan driver dan BBM sudah termasuk",
				pricing: {
					"12jam": 1300000,
					"24jam": 1400000
				}
			},
			aio: {
				name: "All In One (AIO)",
				description: "Mobil + driver + BBM + parkir + makan driver",
				pricing: {
					"12jam": 1400000,
					"24jam": 1500000
				}
			}
		},
		note: "Pilih paket sesuai kebutuhan perjalanan Anda"
	},
	{
		id: "sm-010",
		name: "Sewa Mobil Fortuner VRZ",
		slug: "sewa-mobil-fortuner-vrz",
		category: "mpv-premium",
		description: "SUV premium dengan kemewahan dan kekuatan mesin untuk segala medan perjalanan.",
		image: "/sewa-mobil-fortuner-vrz.webp",
		capacity: "7 Orang",
		transmission: "Automatic",
		year: "",
		rating: 4.9,
		reviews: 143,
		packages: {
			msb: {
				name: "Mobil + Driver + BBM (MSB)",
				description: "Mobil dengan driver dan BBM sudah termasuk",
				pricing: {
					"12jam": 1600000,
					"24jam": 1800000
				}
			},
			aio: {
				name: "All In One (AIO)",
				description: "Mobil + driver + BBM + parkir + makan driver",
				pricing: {
					"12jam": 1700000,
					"24jam": 1900000
				}
			}
		},
		note: "Pilih paket sesuai kebutuhan perjalanan Anda"
	},
	{
		id: "sm-011",
		name: "Sewa Mobil New Alphard Transformer",
		slug: "sewa-mobil-new-alphard-transformer",
		category: "mpv-premium",
		description: "MPV mewah dengan kursi captain seat dan fasilitas premium untuk kenyamanan maksimal.",
		image: "/sewa-mobil-new-alphard-transformer-jogja.webp",
		capacity: "7 Orang",
		transmission: "CVT",
		year: "",
		rating: 4.9,
		reviews: 95,
		packages: {
			msb: {
				name: "Mobil + Driver + BBM (MSB)",
				description: "Mobil dengan driver dan BBM sudah termasuk",
				pricing: {
					"12jam": 3300000,
					"24jam": 3500000
				}
			},
			aio: {
				name: "All In One (AIO)",
				description: "Mobil + driver + BBM + parkir + makan driver",
				pricing: {
					"12jam": 3500000,
					"24jam": 3700000
				}
			}
		},
		note: "Pilih paket sesuai kebutuhan perjalanan Anda"
	},
	{
		id: "sm-012",
		name: "Sewa Mobil Hiace Commuter 14 Seats",
		slug: "sewa-mobil-hiace-commuter-14-seats",
		category: "minibus",
		description: "Minibus nyaman untuk perjalanan grup dengan fasilitas AC dan sound system untuk 14 penumpang.",
		image: "/sewa-mobil-hiace-commuter-14-seats.webp",
		capacity: "14 Orang",
		transmission: "Manual",
		year: "",
		rating: 4.8,
		reviews: 76,
		packages: {
			msb: {
				name: "Mobil + Driver + BBM (MSB)",
				description: "Mobil dengan driver dan BBM sudah termasuk",
				pricing: {
					"12jam": 1200000,
					"24jam": 1300000
				}
			},
			aio: {
				name: "All In One (AIO)",
				description: "Mobil + driver + BBM + parkir + makan driver",
				pricing: {
					"12jam": 1300000,
					"24jam": 1400000
				}
			}
		},
		note: "Pilih paket sesuai kebutuhan perjalanan Anda"
	},
	{
		id: "sm-013",
		name: "Sewa Mobil Hiace Premio 12-14 Seats",
		slug: "sewa-mobil-hiace-premio-12-14-seats",
		category: "minibus",
		description: "Minibus dengan kapasitas 12-14 orang, cocok untuk rombongan keluarga atau grup wisata.",
		image: "/sewa-mobil-hiace-premio-12-14-seats.webp",
		capacity: "12-14 Orang",
		transmission: "Manual",
		year: "",
		rating: 4.8,
		reviews: 92,
		packages: {
			msb: {
				name: "Mobil + Driver + BBM (MSB)",
				description: "Mobil dengan driver dan BBM sudah termasuk",
				pricing: {
					"12jam": 1400000,
					"24jam": 1500000
				}
			},
			aio: {
				name: "All In One (AIO)",
				description: "Mobil + driver + BBM + parkir + makan driver",
				pricing: {
					"12jam": 1500000,
					"24jam": 1600000
				}
			}
		},
		note: "Pilih paket sesuai kebutuhan perjalanan Anda"
	},
	{
		id: "sm-014",
		name: "Sewa Mobil Hiace Premio Luxury",
		slug: "sewa-mobil-hiace-premio-luxury",
		category: "minibus",
		description: "Minibus premium dengan interior mewah dan fasilitas lengkap untuk kenyamanan maksimal.",
		image: "/sewa-mobil-hiace-premio-luxury.webp",
		capacity: "12 Orang",
		transmission: "Automatic",
		year: "",
		rating: 4.9,
		reviews: 67,
		packages: {
			msb: {
				name: "Mobil + Driver + BBM (MSB)",
				description: "Mobil dengan driver dan BBM sudah termasuk",
				pricing: {
					"12jam": 2200000,
					"24jam": 2500000
				}
			},
			aio: {
				name: "All In One (AIO)",
				description: "Mobil + driver + BBM + parkir + makan driver",
				pricing: {
					"12jam": 2500000,
					"24jam": 2700000
				}
			}
		},
		note: "Pilih paket sesuai kebutuhan perjalanan Anda"
	},
	{
		id: "sm-015",
		name: "Sewa Mobil Medium Bus 31-33 Seats",
		slug: "sewa-mobil-medium-bus-31-33-seats",
		category: "minibus",
		description: "Bus sedang dengan kapasitas 31-33 penumpang, ideal untuk grup besar atau rombongan wisata.",
		image: "/sewa-mobil-medium-bus-31-33-seats.webp",
		capacity: "31-33 Orang",
		transmission: "Manual",
		year: "",
		rating: 4.7,
		reviews: 54,
		packages: {
			msb: {
				name: "Mobil + Driver + BBM (MSB)",
				description: "Mobil dengan driver dan BBM sudah termasuk",
				pricing: {
					"12jam": 2000000,
					"24jam": 2300000
				}
			},
			aio: {
				name: "All In One (AIO)",
				description: "Mobil + driver + BBM + parkir + makan driver",
				pricing: {
					"12jam": 2300000,
					"24jam": 2500000
				}
			}
		},
		note: "Pilih paket sesuai kebutuhan perjalanan Anda"
	},
	{
		id: "sm-016",
		name: "Sewa Mobil Bigbus 40-50 Seats",
		slug: "sewa-mobil-big-bus-40-50-seats",
		category: "minibus",
		description: "Bus besar dengan kapasitas 40-50 penumpang, cocok untuk rombongan besar atau tour group.",
		image: "/sewa-mobil-big-bus-40-50-seats.webp",
		capacity: "40-50 Orang",
		transmission: "Manual",
		year: "",
		rating: 4.6,
		reviews: 38,
		packages: {
			msb: {
				name: "Mobil + Driver + BBM (MSB)",
				description: "Mobil dengan driver dan BBM sudah termasuk",
				pricing: {
					"12jam": 3000000,
					"24jam": 3300000
				}
			},
			aio: {
				name: "All In One (AIO)",
				description: "Mobil + driver + BBM + parkir + makan driver",
				pricing: {
					"12jam": 3500000,
					"24jam": 3700000
				}
			}
		},
		note: "Pilih paket sesuai kebutuhan perjalanan Anda"
	}
];
var categories = [
	{
		id: "semua-mobil",
		name: "Semua Mobil",
		slug: "semua-mobil"
	},
	{
		id: "mpv-ekonomis",
		name: "MPV Ekonomis",
		slug: "mpv-ekonomis"
	},
	{
		id: "mpv-premium",
		name: "MPV Premium",
		slug: "mpv-premium"
	},
	{
		id: "minibus",
		name: "Minibus",
		slug: "minibus"
	}
];
const sewaMobil$1 = {
	sewaMobil: sewaMobil,
	categories: categories
};

const sewaMobil$2 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  categories: categories,
  default: sewaMobil$1,
  sewaMobil: sewaMobil
});
//# sourceMappingURL=index.mjs.map
