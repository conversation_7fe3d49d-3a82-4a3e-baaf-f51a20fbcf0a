<script setup lang="ts">
defineProps({
  tags: {
    type: Array,
    required: true
  },
  limit: {
    type: Number,
    default: 0
  },
  size: {
    type: String,
    default: 'sm', // 'sm' or 'md'
    validator: (value) => ['sm', 'md'].includes(value)
  }
});

// Compute the tags to display based on limit
const displayTags = computed(() => {
  if (!props.limit || props.limit >= props.tags.length) {
    return props.tags;
  }
  return props.tags.slice(0, props.limit);
});

// Compute the number of hidden tags
const hiddenCount = computed(() => {
  if (!props.limit || props.limit >= props.tags.length) {
    return 0;
  }
  return props.tags.length - props.limit;
});
</script>

<template>
  <div v-if="tags && tags.length" class="flex flex-wrap gap-2">
    <a 
      v-for="tag in displayTags" 
      :key="tag"
      :href="`/blog/tags/${tag.toLowerCase()}`"
      :class="{
        'text-xs px-2 py-1': size === 'sm',
        'text-sm px-3 py-1.5': size === 'md'
      }"
      class="bg-gray-100 text-gray-800 rounded-full hover:bg-blue-100 hover:text-blue-800 transition-colors"
    >
      #{{ tag }}
    </a>
    <span 
      v-if="hiddenCount > 0" 
      class="text-xs text-gray-500 self-center"
    >
      +{{ hiddenCount }} more
    </span>
  </div>
</template>
