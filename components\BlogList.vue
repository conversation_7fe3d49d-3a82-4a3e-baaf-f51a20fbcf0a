<script setup>
import { toRefs } from 'vue';
import BlogCard from './BlogCard.vue';

const props = defineProps({
  articles: {
    type: Array,
    default: () => []
  },
  showPagination: {
    type: Boolean,
    default: false
  }
})

const { articles } = toRefs(props)
</script>

<template>
  <div>
    <div v-if="!articles || articles.length === 0" class="no-articles">
      <p>Tidak ada artikel yang ditemukan.</p>
    </div>
    
    <div v-else class="blog-grid">
      <BlogCard 
        v-for="article in articles" 
        :key="article._path || article._id" 
        :article="article" 
      />
    </div>
  </div>
</template>

<style scoped>
.blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.no-articles {
  text-align: center;
  padding: 3rem;
  background-color: #f7fafc;
  border-radius: 8px;
}
</style>