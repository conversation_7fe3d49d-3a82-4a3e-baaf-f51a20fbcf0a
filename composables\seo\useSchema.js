// composables/seo/useSchema.js
export const useSchema = () => {
  const baseUrl = 'https://www.jogjaliburan.com'
  
  // Organization Schema - Consistent across all pages
  const getOrganizationSchema = () => ({
    '@type': 'Organization',
    '@id': `${baseUrl}/#organization`,
    'name': '3J Tour - Jogja <PERSON>',
    'alternateName': ['3J Transport', '3J Tour Yogyakarta', 'Jogja Jalan Jalan'],
    'description': 'Sewa mobil & paket wisata Jogja terpercaya dengan layanan 24 jam. Melayani tour, sewa mobil, dan paket honeymoon di Yogyakarta dan sekitarnya',
    'url': baseUrl,
    'logo': {
      '@type': 'ImageObject',
      'url': `${baseUrl}/3j-tour-logo.webp`,
      'width': 300,
      'height': 100,
      'caption': '3J Tour - Jogja <PERSON>'
    },
    'image': [
      `${baseUrl}/images/3j-tour-1.jpg`,
      `${baseUrl}/images/3j-tour-2.jpg`,
      `${baseUrl}/images/3j-tour-fleet.jpg`
    ],
    'founder': {
      '@type': 'Person',
      'name': '3J Tour Founder',
      'description': 'Pendiri 3J Tour dengan pengalaman puluhan tahun di industri pariwisata Yogyakarta'
    },
    'foundingDate': '2015-01-01',
    'contactPoint': [
      {
        '@type': 'ContactPoint',
        'telephone': '+62-851-8688-8837',
        'contactType': 'Customer Service',
        'areaServed': 'ID',
        'availableLanguage': ['Indonesian', 'English'],
        'contactOption': 'TollFree',
        'hoursAvailable': {
          '@type': 'OpeningHoursSpecification',
          'dayOfWeek': ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
          'opens': '00:00',
          'closes': '23:59'
        }
      },
      {
        '@type': 'ContactPoint',
        'telephone': '+62-851-8688-8837',
        'contactType': 'WhatsApp',
        'areaServed': 'ID',
        'availableLanguage': ['Indonesian'],
        'contactOption': 'TollFree'
      }
    ],
    'address': {
      '@type': 'PostalAddress',
      'streetAddress': 'Jl. Merpati, Modalan, Banguntapan',
      'addressLocality': 'Banguntapan',
      'addressRegion': 'Kabupaten Bantul, Daerah Istimewa Yogyakarta',
      'postalCode': '55198',
      'addressCountry': 'ID'
    },
    'geo': {
      '@type': 'GeoCoordinates',
      'latitude': -7.***************,
      'longitude': 110.**************
    },
    'areaServed': [
      {
        '@type': 'Place',
        'name': 'Yogyakarta'
      },
      {
        '@type': 'Place', 
        'name': 'Bantul'
      },
      {
        '@type': 'Place',
        'name': 'Sleman'
      },
      {
        '@type': 'Place',
        'name': 'Gunungkidul'
      },
      {
        '@type': 'Place',
        'name': 'Kulonprogo'
      }
    ],
    'serviceArea': {
      '@type': 'GeoCircle',
      'geoMidpoint': {
        '@type': 'GeoCoordinates',
        'latitude': -7.***************,
        'longitude': 110.**************
      },
      'geoRadius': '100'
    },
    'sameAs': [
      'https://www.facebook.com/3JTransport/',
      'https://www.instagram.com/jogja_jalan_jalan.id/',
      'https://www.youtube.com/channel/UCOyHPyXxPW3uK1C_snW8ggA',
      'https://wa.me/6285186888837',
      'https://www.google.com/maps/place/Sewa+Mobil+%26+Paket+Wisata+Jogja+%7C+3J+Transport/@-7.***************,110.**************'
    ],
    'knowsAbout': [
      'Wisata Yogyakarta',
      'Candi Borobudur',
      'Candi Prambanan', 
      'Pantai Gunungkidul',
      'Kuliner Yogyakarta',
      'Budaya Jawa',
      'Tour Guide Yogyakarta',
      'Sewa Mobil Jogja',
      'Paket Wisata Jogja',
      'Honeymoon Jogja',
      'Gathering Jogja'
    ],
    'offers': [
      {
        '@type': 'Offer',
        'name': 'Paket Wisata Jogja',
        'description': 'Paket wisata lengkap Yogyakarta dengan berbagai pilihan destinasi terbaik',
        'category': 'Travel Package',
        'availableAtOrFrom': {
          '@type': 'Place',
          'name': 'Yogyakarta'
        }
      },
      {
        '@type': 'Offer',
        'name': 'Sewa Mobil Jogja',
        'description': 'Layanan sewa mobil 24 jam dengan atau tanpa driver untuk wisata Yogyakarta',
        'category': 'Car Rental',
        'availableAtOrFrom': {
          '@type': 'Place',
          'name': 'Yogyakarta'
        }
      },
      {
        '@type': 'Offer',
        'name': 'Paket Honeymoon',
        'description': 'Paket bulan madu romantis di Yogyakarta dan destinasi sekitarnya',
        'category': 'Honeymoon Package',
        'availableAtOrFrom': {
          '@type': 'Place',
          'name': 'Yogyakarta'
        }
      },
      {
        '@type': 'Offer',
        'name': 'Paket Gathering',
        'description': 'Paket gathering untuk perusahaan, sekolah, dan komunitas',
        'category': 'Group Package',
        'availableAtOrFrom': {
          '@type': 'Place',
          'name': 'Yogyakarta'
        }
      }
    ],
    'openingHours': 'Mo-Su 00:00-23:59',
    'telephone': '+62-851-8688-8837',
    'email': '<EMAIL>'
  })

  // Website Schema
  const getWebsiteSchema = () => ({
    '@type': 'WebSite',
    '@id': `${baseUrl}/#website`,
    'name': '3J Tour - Jogja Liburan',
    'alternateName': 'Jogja Liburan',
    'description': 'Portal wisata terlengkap untuk liburan di Yogyakarta dengan panduan destinasi, kuliner, dan budaya',
    'url': baseUrl,
    'inLanguage': 'id-ID',
    'publisher': {
      '@id': `${baseUrl}/#organization`
    },
    'copyrightHolder': {
      '@id': `${baseUrl}/#organization`
    },
    'copyrightYear': '2024',
    'potentialAction': [
      {
        '@type': 'SearchAction',
        'target': {
          '@type': 'EntryPoint',
          'urlTemplate': `${baseUrl}/blog/?q={search_term_string}`
        },
        'query-input': 'required name=search_term_string'
      }
    ],
    'mainEntity': {
      '@type': 'WebPage',
      'name': 'Beranda - 3J Tour Jogja Liburan',
      'url': baseUrl
    }
  })

  // Breadcrumb Schema Generator
  const getBreadcrumbSchema = (items) => ({
    '@type': 'BreadcrumbList',
    'itemListElement': items.map((item, index) => ({
      '@type': 'ListItem',
      'position': index + 1,
      'name': item.name,
      'item': item.url
    }))
  })

  // Article Schema Generator
  const getArticleSchema = (article, url) => ({
    '@type': 'Article',
    '@id': url,
    'headline': article.title,
    'description': article.description,
    'image': article.image?.src ? {
      '@type': 'ImageObject',
      'url': article.image.src,
      'width': 1200,
      'height': 630,
      'caption': article.title
    } : undefined,
    'datePublished': article.date,
    'dateModified': article.date,
    'author': {
      '@type': 'Person',
      'name': article.authors?.[0]?.name || '3J Tour Team',
      'description': 'Travel expert dan pemandu wisata Yogyakarta',
      'url': `${baseUrl}/authors/${(article.authors?.[0]?.name || 'team').toLowerCase().replace(/\s+/g, '-')}/`
    },
    'publisher': {
      '@id': `${baseUrl}/#organization`
    },
    'mainEntityOfPage': {
      '@type': 'WebPage',
      '@id': url
    },
    'articleSection': 'Travel Guide',
    'keywords': article.tags?.join(', ') || 'wisata jogja, travel yogyakarta, destinasi wisata',
    'wordCount': 1500,
    'inLanguage': 'id-ID',
    'about': {
      '@type': 'Thing',
      'name': 'Wisata Yogyakarta',
      'description': 'Panduan lengkap wisata dan liburan di Yogyakarta'
    },
    'mentions': article.tags?.map(tag => ({
      '@type': 'Thing',
      'name': tag,
      'url': `${baseUrl}/tags/${tag.toLowerCase()}/`,
      'description': `Kategori wisata ${tag} di Yogyakarta`
    })) || [],
    'isAccessibleForFree': true,
    'hasPart': [
      {
        '@type': 'WebPageElement',
        'isAccessibleForFree': true,
        'cssSelector': '.prose'
      }
    ]
  })

  // Local Business Schema (untuk contact/about pages)
  const getLocalBusinessSchema = () => ({
    '@type': 'TravelAgency',
    '@id': `${baseUrl}/#business`,
    'name': '3J Tour - Jogja Liburan',
    'alternateName': '3J Transport',
    'description': 'Travel agency terpercaya di Yogyakarta dengan layanan 24 jam',
    'url': baseUrl,
    'telephone': '+62-851-8688-8837',
    'email': '<EMAIL>',
    'address': {
      '@type': 'PostalAddress',
      'streetAddress': 'Jl. Merpati, Modalan, Banguntapan',
      'addressLocality': 'Banguntapan',
      'addressRegion': 'Kabupaten Bantul, Daerah Istimewa Yogyakarta',
      'postalCode': '55198',
      'addressCountry': 'ID'
    },
    'geo': {
      '@type': 'GeoCoordinates',
      'latitude': -7.***************,
      'longitude': 110.**************
    },
    'openingHoursSpecification': [
      {
        '@type': 'OpeningHoursSpecification',
        'dayOfWeek': ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
        'opens': '00:00',
        'closes': '23:59'
      }
    ],
    'priceRange': '$$',
    'paymentAccepted': ['Cash', 'Credit Card', 'Bank Transfer', 'Digital Payment'],
    'currenciesAccepted': 'IDR',
    'aggregateRating': {
      '@type': 'AggregateRating',
      'ratingValue': '4.8',
      'reviewCount': '127',
      'bestRating': '5',
      'worstRating': '1'
    },
    'hasMap': 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3952.************!2d110.**************!3d-7.***************!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x2e7a5740c4c1868f%3A0x4307f4831fe33316!2sSewa%20Mobil%20%26%20Paket%20Wisata%20Jogja%20%7C%203J%20Transport!5e0!3m2!1sen!2sid!4v1750299191775!5m2!1sen!2sid'
  })

  return {
    baseUrl,
    getOrganizationSchema,
    getWebsiteSchema,
    getBreadcrumbSchema,
    getArticleSchema,
    getLocalBusinessSchema
  }
}
