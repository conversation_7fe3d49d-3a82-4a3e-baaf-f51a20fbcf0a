// Generated by nitro
import type { Serialize, Simplify } from "nitropack/types";
declare module "nitropack/types" {
  type Awaited<T> = T extends PromiseLike<infer U> ? Awaited<U> : T
  interface InternalApi {
    '/api/destinations/:slug': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/destinations/[slug].get').default>>>>
    }
    '/api/destinations': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/destinations/index.get').default>>>>
    }
    '/api/paket-wisata/:slug': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/paket-wisata/[slug]').default>>>>
    }
    '/api/paket-wisata/category/:category': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/paket-wisata/category/[category]').default>>>>
    }
    '/api/paket-wisata/detail/:slug': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/paket-wisata/detail/[slug]').default>>>>
    }
    '/api/paket-wisata/duration/:duration': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/paket-wisata/duration/[duration]').default>>>>
    }
    '/api/paket-wisata': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/paket-wisata/index').default>>>>
    }
    '/api/sitemap/paket-gathering': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/sitemap/paket-gathering.get').default>>>>
    }
    '/api/sitemap/paket-honeymoon': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/sitemap/paket-honeymoon.get').default>>>>
    }
    '/api/sitemap/paket-wisata': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/sitemap/paket-wisata.get').default>>>>
    }
    '/api/sitemap/sewa-mobil': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/sitemap/sewa-mobil.get').default>>>>
    }
    '/__nuxt_error': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/nuxt/dist/core/runtime/nitro/handlers/renderer').default>>>>
    }
    '/__site-config__/debug.json': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/nuxt-site-config/dist/runtime/server/routes/__site-config__/debug').default>>>>
    }
    '/__sitemap__/nuxt-content-urls.json': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/@nuxtjs/sitemap/dist/runtime/server/routes/__sitemap__/nuxt-content-urls-v3').default>>>>
    }
    '/__sitemap__/debug.json': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/@nuxtjs/sitemap/dist/runtime/server/routes/__sitemap__/debug').default>>>>
    }
    '/__sitemap__/style.xsl': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/@nuxtjs/sitemap/dist/runtime/server/routes/sitemap.xsl').default>>>>
    }
    '/sitemap.xml': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/@nuxtjs/sitemap/dist/runtime/server/routes/sitemap.xml').default>>>>
    }
    '/__nuxt_island/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/#internal/nuxt/island-renderer').default>>>>
    }
    '/__nuxt_content/:collection/sql_dump.txt': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/@nuxt/content/dist/runtime/presets/node/database-handler').default>>>>
    }
    '/__nuxt_content/:collection/query': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/@nuxt/content/dist/runtime/api/query.post').default>>>>
    }
    '/_ipx/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/@nuxt/image/dist/runtime/ipx').default>>>>
    }
  }
}
export {}