<script setup lang="ts">
const props = defineProps({
  prev: {
    type: Object,
    default: null
  },
  next: {
    type: Object,
    default: null
  }
});

// Helper function untuk mendapatkan URL post
function getPostUrl(post) {
  if (!post) return '';
  
  // Coba semua kem<PERSON>an properti path
  if (post.path) return post.path;
  if (post._path) return post._path;
  
  // Fallback ke slug dari _path atau _file
  const slug = post._path?.split('/').pop() || 
               post._file?.split('/').pop()?.replace('.md', '') ||
               post._id?.split(':').pop();
  
  return `/blog/${slug}`;
}
</script>

<template>
  <nav class="border-t border-gray-200 pt-8 mt-8" aria-label="Navigasi Artikel">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
      <!-- Previous Article -->
      <div class="md:w-1/2">
        <a 
          v-if="prev" 
          :href="getPostUrl(prev)" 
          class="flex items-center text-blue-600 hover:text-blue-800 transition"
          rel="prev"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          <div>
            <div class="text-xs text-gray-500 mb-1">Artikel Sebelumnya</div>
            <div class="font-medium">{{ prev.title }}</div>
          </div>
        </a>
        <div v-else class="invisible"></div>
      </div>
      
      <!-- Next Article -->
      <div class="md:w-1/2 ml-auto">
        <a 
          v-if="next" 
          :href="getPostUrl(next)" 
          class="flex items-center text-blue-600 hover:text-blue-800 transition ml-auto"
          rel="next"
        >
          <div class="text-right">
            <div class="text-xs text-gray-500 mb-1">Artikel Selanjutnya</div>
            <div class="font-medium">{{ next.title }}</div>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
        <div v-else class="invisible"></div>
      </div>
    </div>
  </nav>
</template>
