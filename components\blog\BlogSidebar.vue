<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps({
  // Path artikel saat ini (untuk mengecualikan dari daftar terbaru)
  currentArticlePath: {
    type: String,
    default: ''
  },
  // Tag yang sedang aktif (untuk highlight di daftar tag)
  activeTag: {
    type: String,
    default: ''
  }
});

// Mendapatkan artikel terbaru
const { data: recentArticles } = await useAsyncData('recent-articles', () => {
  return queryContent('/blog')
    .where({ _path: { $ne: props.currentArticlePath } })
    .sort({ date: -1 })
    .limit(5)
    .find();
});

// Mendapatkan semua tag unik
const { data: allTags } = await useAsyncData('all-tags', async () => {
  const articles = await queryContent('/blog').find();
  
  // Ekstrak semua tag dari semua artikel
  const tagMap = {};
  articles.forEach(article => {
    if (article.tags && Array.isArray(article.tags)) {
      article.tags.forEach(tag => {
        if (tag) {
          const normalizedTag = tag.toLowerCase();
          tagMap[normalizedTag] = (tagMap[normalizedTag] || 0) + 1;
        }
      });
    }
  });
  
  // Konversi ke array dan urutkan berdasarkan jumlah
  return Object.entries(tagMap)
    .map(([tag, count]) => ({ tag, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10); // Ambil 10 tag teratas
});

// Format tanggal
function formatDate(date) {
  if (!date) return '';
  return new Date(date).toLocaleDateString('id-ID', { year: 'numeric', month: 'long', day: 'numeric' });
}
</script>

<template>
  <div class="blog-sidebar">
    <!-- Pencarian -->
    <div class="bg-white dark:bg-gray-900 rounded-xl shadow-md p-6 mb-8">
      <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Cari Artikel</h3>
      <div class="relative">
        <input 
          type="text" 
          placeholder="Cari artikel..." 
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white"
          @keyup.enter="$emit('search', $event.target.value)"
        >
        <button 
          class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-primary"
          @click="$emit('search', $event.target.previousElementSibling.value)"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Artikel Terbaru -->
    <div class="bg-white dark:bg-gray-900 rounded-xl shadow-md p-6 mb-8">
      <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Artikel Terbaru</h3>
      <div v-if="recentArticles && recentArticles.length" class="space-y-4">
        <div 
          v-for="article in recentArticles" 
          :key="article._path"
          class="border-b border-gray-200 dark:border-gray-700 pb-4 last:border-0 last:pb-0"
        >
          <NuxtLink 
            :to="article._path" 
            class="block hover:bg-gray-50 dark:hover:bg-gray-800 -mx-2 p-2 rounded-lg transition-colors"
          >
            <h4 class="font-medium text-gray-900 dark:text-white mb-1 line-clamp-2">{{ article.title }}</h4>
            <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
              <time :datetime="article.date">{{ formatDate(article.date) }}</time>
              <span v-if="article.badge" class="ml-2 bg-primary/10 text-primary px-2 py-0.5 rounded-full text-xs">
                {{ article.badge.label }}
              </span>
            </div>
          </NuxtLink>
        </div>
      </div>
      <div v-else class="text-gray-500 dark:text-gray-400 text-sm">
        Tidak ada artikel terbaru.
      </div>
    </div>
    
    <!-- Tag Populer -->
    <div class="bg-white dark:bg-gray-900 rounded-xl shadow-md p-6">
      <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">Tag Populer</h3>
      <div v-if="allTags && allTags.length" class="flex flex-wrap gap-2">
        <NuxtLink 
          v-for="tagItem in allTags" 
          :key="tagItem.tag"
          :to="`/blog/tags/${encodeURIComponent(tagItem.tag)}`"
          class="tag-link"
          :class="{
            'bg-primary/10 text-primary': activeTag.toLowerCase() === tagItem.tag,
            'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700': activeTag.toLowerCase() !== tagItem.tag
          }"
        >
          #{{ tagItem.tag }}
          <span class="ml-1 text-xs">({{ tagItem.count }})</span>
        </NuxtLink>
      </div>
      <div v-else class="text-gray-500 dark:text-gray-400 text-sm">
        Tidak ada tag tersedia.
      </div>
    </div>
  </div>
</template>

<style scoped>
.tag-link {
  @apply px-3 py-1 rounded-full text-sm font-medium transition-colors;
}
</style>
