export const useSchemaRental = () => {
  // Base Organization Schema (same as travel)
  const getOrganizationSchema = () => {
    return {
      "@type": "Organization",
      "@id": "https://www.jogjaliburan.com/#organization",
      name: "3J Tour - Jog<PERSON>",
      alternateName: "3J Tour",
      url: "https://www.jogjaliburan.com",
      logo: {
        "@type": "ImageObject",
        url: "https://www.jogjaliburan.com/3j-tour-logo.webp",
        width: 300,
        height: 100
      },
      contactPoint: {
        "@type": "ContactPoint",
        telephone: "+62-812-3456-7890",
        contactType: "customer service",
        areaServed: "ID",
        availableLanguage: ["Indonesian"]
      },
      address: {
        "@type": "PostalAddress",
        addressLocality: "Yogyakarta",
        addressRegion: "DI Yogyakarta",
        addressCountry: "ID"
      },
      sameAs: [
        "https://www.instagram.com/3jtour",
        "https://wa.me/6285186888837"
      ]
    }
  }

  // Vehicle Schema for individual cars
  const getVehicleSchema = (mobil) => {
    if (!mobil) return null

    // Handle different pricing formats
    const pricing = mobil.packages 
      ? Object.values(mobil.packages).flatMap(pkg => 
          pkg.pricing ? Object.entries(pkg.pricing).map(([duration, price]) => ({
            "@type": "Offer",
            name: `${pkg.name} - ${duration}`,
            price: typeof price === 'string' ? price.replace(/[^\d]/g, '') : price.toString(),
            priceCurrency: "IDR",
            availability: "https://schema.org/InStock",
            validFrom: new Date().toISOString(),
            description: `${pkg.description} - ${duration}`,
            seller: {
              "@id": "https://www.jogjaliburan.com/#organization"
            }
          })) : []
        )
      : mobil.pricing ? Object.entries(mobil.pricing).map(([key, price]) => ({
          "@type": "Offer",
          name: key,
          price: typeof price === 'string' ? price.replace(/[^\d]/g, '') : price.toString(),
          priceCurrency: "IDR",
          availability: "https://schema.org/InStock",
          validFrom: new Date().toISOString(),
          seller: {
            "@id": "https://www.jogjaliburan.com/#organization"
          }
        })) : []

    return {
      "@type": "Vehicle",
      "@id": `https://www.jogjaliburan.com/sewa-mobil-jogja/${mobil.slug}/`,
      name: mobil.name,
      description: mobil.description,
      image: mobil.image,
      url: `https://www.jogjaliburan.com/sewa-mobil-jogja/${mobil.slug}/`,
      manufacturer: {
        "@type": "Organization",
        name: mobil.name.split(' ')[0] || "Unknown"
      },
      model: mobil.name,
      vehicleSeatingCapacity: mobil.capacity?.replace(/[^\d]/g, '') || "7",
      vehicleTransmission: mobil.transmission || "Manual",
      productionDate: mobil.year || "2023",
      provider: {
        "@id": "https://www.jogjaliburan.com/#organization"
      },
      offers: pricing,
      aggregateRating: mobil.rating ? {
        "@type": "AggregateRating",
        ratingValue: mobil.rating,
        reviewCount: mobil.reviews || 50,
        bestRating: 5,
        worstRating: 1
      } : {
        "@type": "AggregateRating",
        ratingValue: 4.5,
        reviewCount: 25,
        bestRating: 5,
        worstRating: 1
      }
    }
  }

  // Service Schema for rental services (non-ecommerce)
  const getRentalServiceItemSchema = (mobil) => {
    if (!mobil) return null

    // Get price range
    let lowPrice = 190000
    let highPrice = 900000

    if (mobil.packages) {
      const allPrices = Object.values(mobil.packages).flatMap(pkg =>
        pkg.pricing ? Object.values(pkg.pricing).map(p =>
          typeof p === 'string' ? parseInt(p.replace(/[^\d]/g, '')) : p
        ) : []
      )
      if (allPrices.length > 0) {
        lowPrice = Math.min(...allPrices)
        highPrice = Math.max(...allPrices)
      }
    } else if (mobil.pricing) {
      const prices = Object.values(mobil.pricing)
        .map(p => typeof p === 'string' ? parseInt(p.replace(/[^\d]/g, '')) : p)
        .filter(p => p > 0)
      if (prices.length > 0) {
        lowPrice = Math.min(...prices)
        highPrice = Math.max(...prices)
      }
    }

    return {
      "@type": "Service",
      "@id": `https://www.jogjaliburan.com/sewa-mobil-jogja/${mobil.slug}/#service`,
      name: `Sewa ${mobil.name}`,
      description: `Layanan rental ${mobil.name} di Jogja - ${mobil.description}`,
      provider: {
        "@id": "https://www.jogjaliburan.com/#organization"
      },
      areaServed: {
        "@type": "State",
        name: "DI Yogyakarta"
      },
      hasOfferCatalog: {
        "@type": "OfferCatalog",
        name: `Paket Sewa ${mobil.name}`,
        itemListElement: mobil.packages ? Object.entries(mobil.packages).flatMap(([packageKey, pkg]) =>
          pkg.pricing ? Object.entries(pkg.pricing).map(([duration, price]) => ({
            "@type": "Offer",
            name: `${pkg.name} - ${duration}`,
            price: typeof price === 'string' ? price.replace(/[^\d]/g, '') : price.toString(),
            priceCurrency: "IDR",
            availability: "https://schema.org/InStock",
            validFrom: new Date().toISOString().split('T')[0],
            seller: {
              "@id": "https://www.jogjaliburan.com/#organization"
            }
          })) : []
        ) : []
      },
      aggregateRating: {
        "@type": "AggregateRating",
        ratingValue: mobil.rating || 4.5,
        reviewCount: mobil.reviews || 25,
        bestRating: 5,
        worstRating: 1
      }
    }
  }

  // RentalService Schema for business context
  const getRentalServiceSchema = () => {
    return {
      "@type": "RentalCarAgency",
      "@id": "https://www.jogjaliburan.com/sewa-mobil-jogja/#rental-service",
      name: "3J Tour - Sewa Mobil Jogja",
      description: "Layanan rental mobil terpercaya di Yogyakarta dengan berbagai pilihan kendaraan",
      provider: {
        "@id": "https://www.jogjaliburan.com/#organization"
      },
      areaServed: {
        "@type": "State",
        name: "DI Yogyakarta"
      },
      availableVehicle: [
        {
          "@type": "Vehicle",
          name: "Toyota Avanza",
          category: "MPV Ekonomis",
          offers: {
            "@type": "Offer",
            price: "500000",
            priceCurrency: "IDR",
            availability: "https://schema.org/InStock",
            seller: {
              "@id": "https://www.jogjaliburan.com/#organization"
            }
          },
          aggregateRating: {
            "@type": "AggregateRating",
            ratingValue: 4.8,
            reviewCount: 124,
            bestRating: 5,
            worstRating: 1
          }
        },
        {
          "@type": "Vehicle",
          name: "Toyota Innova",
          category: "MPV Premium",
          offers: {
            "@type": "Offer",
            price: "600000",
            priceCurrency: "IDR",
            availability: "https://schema.org/InStock",
            seller: {
              "@id": "https://www.jogjaliburan.com/#organization"
            }
          },
          aggregateRating: {
            "@type": "AggregateRating",
            ratingValue: 4.9,
            reviewCount: 201,
            bestRating: 5,
            worstRating: 1
          }
        },
        {
          "@type": "Vehicle",
          name: "Daihatsu Agya",
          category: "City Car",
          offers: {
            "@type": "Offer",
            price: "350000",
            priceCurrency: "IDR",
            availability: "https://schema.org/InStock",
            seller: {
              "@id": "https://www.jogjaliburan.com/#organization"
            }
          },
          aggregateRating: {
            "@type": "AggregateRating",
            ratingValue: 4.6,
            reviewCount: 85,
            bestRating: 5,
            worstRating: 1
          }
        }
      ],
      priceRange: "IDR 190,000 - IDR 900,000",
      url: "https://www.jogjaliburan.com/sewa-mobil-jogja/"
    }
  }

  // Collection Page Schema for category/index pages
  const getCollectionPageSchema = (title, description, items, pageType, currentSlug) => {
    const baseUrl = "https://www.jogjaliburan.com/sewa-mobil-jogja"
    const fullUrl = currentSlug ? `${baseUrl}/${currentSlug}/` : `${baseUrl}/`

    return {
      "@type": "CollectionPage",
      "@id": `${fullUrl}#collection`,
      name: title,
      description: description,
      url: fullUrl,
      mainEntity: {
        "@type": "ItemList",
        numberOfItems: items?.length || 0,
        itemListElement: items?.slice(0, 10).map((item, index) => ({
          "@type": "ListItem",
          position: index + 1,
          item: {
            "@type": "Vehicle",
            "@id": `${baseUrl}/${item.slug}/`,
            name: item.name,
            description: item.description,
            image: item.image,
            url: `${baseUrl}/${item.slug}/`,
            offers: {
              "@type": "Offer",
              price: getLowestPrice(item),
              priceCurrency: "IDR",
              availability: "https://schema.org/InStock",
              validFrom: new Date().toISOString(),
              seller: {
                "@id": "https://www.jogjaliburan.com/#organization"
              }
            },
            aggregateRating: item.rating ? {
              "@type": "AggregateRating",
              ratingValue: item.rating,
              reviewCount: item.reviews || 50,
              bestRating: 5,
              worstRating: 1
            } : {
              "@type": "AggregateRating",
              ratingValue: 4.5,
              reviewCount: 25,
              bestRating: 5,
              worstRating: 1
            }
          }
        })) || []
      },
      breadcrumb: getBreadcrumbSchema(pageType, currentSlug),
      provider: {
        "@id": "https://www.jogjaliburan.com/#organization"
      }
    }
  }

  // Breadcrumb Schema for rental pages
  const getBreadcrumbSchema = (pageType, currentSlug = '', mobilTitle = '') => {
    const breadcrumbs = [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: "https://www.jogjaliburan.com"
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Sewa Mobil Jogja",
        item: "https://www.jogjaliburan.com/sewa-mobil-jogja/"
      }
    ]

    // For detail pages, add the specific car as level 3
    if (pageType === 'detail' && currentSlug && mobilTitle) {
      breadcrumbs.push({
        "@type": "ListItem",
        position: 3,
        name: mobilTitle,
        item: `https://www.jogjaliburan.com/sewa-mobil-jogja/${currentSlug}/`
      })
    }
    // For category pages, add the category as level 3
    else if (pageType === 'category' && currentSlug) {
      const categoryNames = {
        'city-car': 'Sewa City Car Jogja',
        'mpv-ekonomis': 'Sewa MPV Ekonomis Jogja',
        'mpv-premium': 'Sewa MPV Premium Jogja',
        'hatchback': 'Sewa Hatchback Jogja',
        'minibus': 'Sewa Minibus Jogja'
      }

      if (categoryNames[currentSlug]) {
        breadcrumbs.push({
          "@type": "ListItem",
          position: 3,
          name: categoryNames[currentSlug],
          item: `https://www.jogjaliburan.com/sewa-mobil-jogja/${currentSlug}/`
        })
      }
    }

    return {
      "@type": "BreadcrumbList",
      itemListElement: breadcrumbs
    }
  }

  // FAQ Schema for rental services
  const getFAQSchema = (faqs) => {
    if (!faqs || faqs.length === 0) return null

    return {
      "@type": "FAQPage",
      mainEntity: faqs.map(faq => ({
        "@type": "Question",
        name: faq.question,
        acceptedAnswer: {
          "@type": "Answer",
          text: faq.answer
        }
      }))
    }
  }

  // Helper function to get lowest price
  const getLowestPrice = (mobil) => {
    if (!mobil) return "190000"
    
    // Handle packages format
    if (mobil.packages) {
      const allPrices = Object.values(mobil.packages).flatMap(pkg => 
        pkg.pricing ? Object.values(pkg.pricing) : []
      )
      if (allPrices.length > 0) {
        const minPrice = Math.min(...allPrices.map(p => 
          typeof p === 'string' ? parseInt(p.replace(/[^\d]/g, '')) : p
        ))
        return minPrice.toString()
      }
    }
    
    // Handle pricing format  
    if (mobil.pricing) {
      const prices = Object.values(mobil.pricing)
        .map(p => typeof p === 'string' ? parseInt(p.replace(/[^\d]/g, '')) : p)
        .filter(p => p > 0)
      
      if (prices.length > 0) {
        return Math.min(...prices).toString()
      }
    }
    
    return "190000"
  }

  // Main Schema Generator for rental pages
  const generateRentalSchema = (pageData) => {
    const { pageType, mobil, items, title, description, currentSlug, faqs } = pageData
    
    const schemas = []

    // Always include organization
    schemas.push(getOrganizationSchema())

    switch (pageType) {
      case 'detail':
        if (mobil) {
          schemas.push(getVehicleSchema(mobil))
          schemas.push(getRentalServiceItemSchema(mobil))
          schemas.push(getBreadcrumbSchema('detail', mobil.slug, mobil.name))
        }
        break

      case 'index':
        schemas.push(getCollectionPageSchema(
          title || "Sewa Mobil Jogja Murah & Terpercaya",
          description || "Rental mobil Jogja dengan pilihan lengkap: city car, MPV, minibus. Harga mulai 190rb/12jam. Driver berpengalaman, kondisi prima.",
          items,
          'index'
        ))
        schemas.push(getRentalServiceSchema())
        // Add FAQ only for index page
        if (faqs && faqs.length > 0) {
          schemas.push(getFAQSchema(faqs))
        }
        break

      case 'category':
        schemas.push(getCollectionPageSchema(title, description, items, pageType, currentSlug))
        break

      default:
        schemas.push(getCollectionPageSchema(title, description, items, 'default', currentSlug))
    }

    return {
      "@context": "https://schema.org",
      "@graph": schemas.filter(Boolean)
    }
  }

  return {
    generateRentalSchema,
    getOrganizationSchema,
    getVehicleSchema,
    getRentalServiceItemSchema,
    getRentalServiceSchema,
    getCollectionPageSchema,
    getBreadcrumbSchema,
    getFAQSchema,
    getLowestPrice
  }
} 