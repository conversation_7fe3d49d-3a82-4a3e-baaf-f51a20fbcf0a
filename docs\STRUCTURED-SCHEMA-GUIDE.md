# 🏗️ Structured Schema Markup Guide

Dokumentasi lengkap implementasi **JSON-LD Structured Data** untuk website travel & rental dengan hierarki halaman yang kompleks.

## 📋 Overview

Sistem schema ini dirancang untuk:
- ✅ **Multi-page Structure** - Index, detail, category, duration pages  
- ✅ **Dynamic Content** - Otomatis generate berdasarkan data
- ✅ **SEO Optimization** - Rich snippets & AI preview ready
- ✅ **Manual Implementation** - Tidak menggunakan plugin berisiko
- ✅ **Crawl Friendly** - Struktur yang mudah dipahami search engine
- ✅ **Dual Service Support** - Travel packages & Car rental

## 🧩 Composables Available

### 1. **useSchemaStructured** - Travel Packages
Untuk halaman paket wisata/travel dengan schema types:
- TourPackage, Product, TravelAgency, CollectionPage

### 2. **useSchemaRental** - Car Rental  
Untuk halaman sewa mobil/rental dengan schema types:
- Vehicle, Product, RentalCarAgency, CollectionPage

## 🎯 Schema Types Used

### 1. **Organization Schema**
```json
{
  "@type": "Organization",
  "@id": "https://www.jogjaliburan.com/#organization",
  "name": "3J Tour - Jogja Liburan",
  "contactPoint": {...},
  "address": {...},
  "sameAs": [...]
}
```

### 2. **TourPackage Schema** (Detail Pages)
```json
{
  "@type": "TourPackage",
  "@id": "https://www.jogjaliburan.com/paket-wisata-jogja/[slug]",
  "name": "Package Title",
  "duration": "P2D",
  "location": {...},
  "offers": [...],
  "itinerary": [...]
}
```

### 3. **Product Schema** (E-commerce Features)
```json
{
  "@type": "Product",
  "category": "Travel Package",
  "offers": {
    "@type": "AggregateOffer",
    "lowPrice": 350000,
    "highPrice": 2000000,
    "priceCurrency": "IDR"
  }
}
```

### 4. **CollectionPage Schema** (Category/Duration Pages)
```json
{
  "@type": "CollectionPage",
  "mainEntity": {
    "@type": "ItemList",
    "numberOfItems": 12,
    "itemListElement": [...]
  }
}
```

### 5. **BreadcrumbList Schema**
```json
{
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Home",
      "item": "https://www.jogjaliburan.com"
    },
    {
      "@type": "ListItem",
      "position": 2,
      "name": "Paket Wisata Jogja",
      "item": "https://www.jogjaliburan.com/paket-wisata-jogja"
    },
    {
      "@type": "ListItem",
      "position": 3,
      "name": "Paket Wisata Jogja 2 Hari 1 Malam",
      "item": "https://www.jogjaliburan.com/paket-wisata-jogja/paket-wisata-jogja-2-hari-1-malam"
    }
  ]
}
```

### 6. **FAQPage Schema**
```json
{
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "Question text",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Answer text"
      }
    }
  ]
}
```

### 7. **TravelAgency Schema** (Local Business)
```json
{
  "@type": "TravelAgency",
  "address": {...},
  "geo": {...},
  "priceRange": "IDR 350,000 - IDR 2,000,000",
  "hasOfferCatalog": {...}
}
```

## 🚗 Rental Schema Types (useSchemaRental)

### 8. **Vehicle Schema** (Car Detail Pages)
```json
{
  "@type": "Vehicle",
  "@id": "https://www.jogjaliburan.com/sewa-mobil-jogja/[slug]",
  "name": "Toyota Avanza",
  "manufacturer": {
    "@type": "Organization",
    "name": "Toyota"
  },
  "model": "Avanza",
  "vehicleSeatingCapacity": "7",
  "vehicleTransmission": "Manual",
  "productionDate": "2023",
  "offers": [
    {
      "@type": "Offer",
      "name": "Paket MS - 12jam",
      "price": "250000",
      "priceCurrency": "IDR",
      "availability": "https://schema.org/InStock"
    }
  ],
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": 4.8,
    "reviewCount": 125
  }
}
```

### 9. **RentalCarAgency Schema** (Rental Business)
```json
{
  "@type": "RentalCarAgency",
  "@id": "https://www.jogjaliburan.com/sewa-mobil-jogja#rental-service",
  "name": "3J Tour - Sewa Mobil Jogja",
  "description": "Layanan rental mobil terpercaya di Yogyakarta",
  "areaServed": {
    "@type": "State",
    "name": "DI Yogyakarta"
  },
  "availableVehicle": [
    {
      "@type": "Vehicle",
      "name": "Toyota Avanza",
      "category": "MPV Ekonomis"
    }
  ],
  "priceRange": "IDR 190,000 - IDR 900,000"
}
```

### 10. **Product Schema** (Rental E-commerce)
```json
{
  "@type": "Product",
  "@id": "https://www.jogjaliburan.com/sewa-mobil-jogja/[slug]#product",
  "name": "Sewa Toyota Avanza",
  "description": "Rental Toyota Avanza di Jogja",
  "category": "Car Rental",
  "offers": {
    "@type": "AggregateOffer",
    "lowPrice": 190000,
    "highPrice": 900000,
    "priceCurrency": "IDR",
    "availability": "https://schema.org/InStock",
    "offerCount": 3
  }
}
```

## 🚀 Implementation Guide

### 1. **Halaman Index** (`/paket-wisata-jogja`)
```javascript
// pages/paket-wisata-jogja/index.vue
import { useSchemaStructured } from '~/composables/seo/useSchemaStructured'

const { generateSchema } = useSchemaStructured()

const schemaData = generateSchema({
  pageType: 'index',
  title: 'Paket Wisata Jogja Murah 2024',
  description: 'Description...',
  items: filteredDestinations.value,
  faqs: faqData
})

useHead({
  script: [
    {
      type: 'application/ld+json',
      children: JSON.stringify(schemaData)
    }
  ]
})
```

### 2. **Halaman Detail** (`/paket-wisata-jogja/[slug]`)
```javascript
// pages/paket-wisata-jogja/[slug].vue
const schemaData = computed(() => {
  if (isDetailPage.value && destination.value) {
    return generateSchema({
      pageType: 'detail',
      paket: destination.value
      // FAQ removed - not needed for individual packages
    })
  }
  return null
})
```

## 🚗 Rental Implementation Guide

### 1. **Halaman Index Sewa Mobil** (`/sewa-mobil-jogja`)
```javascript
// pages/sewa-mobil-jogja/index.vue
import { useSchemaRental } from '~/composables/seo/useSchemaRental'

const { generateRentalSchema } = useSchemaRental()

const schemaData = generateRentalSchema({
  pageType: 'index',
  title: 'Sewa Mobil Jogja Murah & Terpercaya',
  description: 'Rental mobil Jogja dengan pilihan lengkap...',
  items: cars.value,
  faqs: rentalFAQs
})

useHead({
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify(schemaData)
    }
  ]
})
```

### 2. **Halaman Detail Mobil** (`/sewa-mobil-jogja/[slug]`)
```javascript
// pages/sewa-mobil-jogja/[slug].vue
const schemaData = computed(() => {
  if (!car.value) return null
  
  return generateRentalSchema({
    pageType: 'detail',
    mobil: car.value,
    title: `${car.value.name} - Rental Mobil Jogja`,
    description: `${car.value.description}...`,
    currentSlug: car.value.slug
  })
})
```

### 3. **Halaman Kategori Kendaraan**
```javascript
const schemaData = computed(() => {
  return generateRentalSchema({
    pageType: 'category',
    title: 'Sewa MPV Ekonomis Jogja',
    description: 'Pilihan MPV ekonomis...',
    items: filteredCars.value,
    currentSlug: 'mpv-ekonomis'
  })
})
```

### 3. **Halaman Kategori/Durasi**
```javascript
const schemaData = computed(() => {
  return generateSchema({
    pageType: isDuration.value ? 'duration' : 'category',
    title: pageTitle.value,
    description: pageDescription.value,
    items: filteredDestinations.value,
    currentSlug: slug.value
  })
})
```

## 📊 Schema Structure by Page Type

### **Index Page Schema Components:**
- ✅ Organization
- ✅ CollectionPage + ItemList
- ✅ TravelAgency (Local Business)
- ✅ FAQPage
- ✅ BreadcrumbList

### **Detail Page Schema Components:**
- ✅ Organization
- ✅ TourPackage (dengan itinerary)
- ✅ Product (untuk e-commerce features)
- ✅ Multiple Offer objects (untuk pricing options)
- ✅ AggregateRating
- ✅ BreadcrumbList (3-level: Home → Paket Wisata Jogja → Package Name)
- ❌ FAQPage (removed - maintenance overhead untuk banyak paket)

### **Category/Duration Page Schema Components:**
- ✅ Organization
- ✅ CollectionPage + ItemList
- ✅ BreadcrumbList (dengan hierarki yang tepat)

## 🎯 SEO Benefits

### **Rich Snippets Eligible:**
- ⭐ **Star Ratings** - Review stars di SERP
- 💰 **Price Information** - Range harga di hasil pencarian
- 📍 **Location Data** - Local pack inclusion
- ❓ **FAQ Snippets** - Featured FAQ di SERP
- 🍞 **Breadcrumbs** - Navigation path di SERP
- 🏢 **Business Info** - Knowledge panel data

### **AI Preview Optimization:**
- 🤖 **Structured Context** - AI dapat memahami context bisnis
- 📋 **Product Catalog** - Clear understanding tentang produk
- 💡 **Question Answering** - FAQ data untuk AI responses
- 🎯 **Intent Matching** - Schema membantu AI match user intent

## 🔧 Dynamic Features

### **Auto-generated Pricing:**
```javascript
// Automatically handles different pricing formats
const pricing = paket.pricing && paket.pricing.length > 0 
  ? paket.pricing.map(p => ({
      "@type": "Offer",
      name: p.label,
      price: p.price.replace(/[^\d]/g, ''),
      priceCurrency: "IDR"
    }))
  : [defaultOffer]
```

### **Dynamic Duration:**
```javascript
// Auto-converts duration from slug to ISO 8601 format
duration: `P${paket.duration?.includes('1-hari') ? '1' : 
           paket.duration?.includes('2-hari') ? '2' : 
           paket.duration?.includes('3-hari') ? '3' : 
           paket.duration?.includes('4-hari') ? '4' : '1'}D`
```

### **Dynamic FAQ Generation:**
```javascript
// Auto-generates relevant FAQs per package
const detailFAQs = computed(() => [
  {
    question: `Berapa harga ${destination.value.title}?`,
    answer: `Harga ${destination.value.title} mulai dari ${pricing}...`
  }
])
```

## 📈 Testing & Validation

### **Testing Tools:**
1. **Google Rich Results Test** - https://search.google.com/test/rich-results
2. **Schema.org Validator** - https://validator.schema.org/
3. **JSON-LD Playground** - https://json-ld.org/playground/

### **Validation Checklist:**
- ✅ Valid JSON-LD syntax
- ✅ Required properties present
- ✅ Proper @id linking between entities
- ✅ Correct data types (ISO 8601 dates, numbers, etc.)
- ✅ No circular references
- ✅ Breadcrumb hierarchy correct

## 🚨 Best Practices

### **DO's:**
- ✅ Use @id untuk link antar entities
- ✅ Include required properties untuk each schema type
- ✅ Use proper ISO formats (dates, currency, duration)
- ✅ Validate regularly dengan Google tools
- ✅ Keep schema updated dengan content changes
- ✅ Use specific schema types (TourPackage vs generic Product)
- ✅ Use 3-level breadcrumbs untuk detail pages (Home → Category → Item)
- ✅ Keep FAQ hanya di index pages yang general, bukan per-item

### **DON'Ts:**
- ❌ Don't markup invisible content
- ❌ Don't use schema untuk misleading information
- ❌ Don't create circular references
- ❌ Don't mix different structured data formats
- ❌ Don't overuse schema (quality over quantity)

## 🎨 Advanced Features

### **@graph Implementation:**
```javascript
return {
  "@context": "https://schema.org",
  "@graph": schemas.filter(Boolean)
}
```
**Benefits:**
- Multiple entities dalam single JSON-LD block
- Better organization dan readability
- Efficient entity linking dengan @id

### **Entity Linking:**
```javascript
"provider": {
  "@id": "https://www.jogjaliburan.com/#organization"
},
"seller": {
  "@id": "https://www.jogjaliburan.com/#organization"
}
```
**Benefits:**
- Establishes clear relationships
- Reduces redundancy
- Better entity recognition

## 📱 Mobile & Performance

### **Lazy Loading Considerations:**
- Schema included dalam initial HTML
- No CLS impact
- Fast rendering pada mobile devices

### **Size Optimization:**
- Efficient data structure
- Remove null/undefined values
- Compress dengan filter(Boolean)

## 🔮 Future Enhancements

### **Potential Additions:**
- **Event Schema** - untuk travel events
- **Review Schema** - customer reviews integration
- **Video Schema** - tour preview videos
- **Recipe Schema** - local food experiences

### **AI Enhancement Opportunities:**
- **CreativeWork** - untuk travel guides
- **Course** - untuk cultural experiences
- **Service** - untuk specific travel services

---

## 📞 Support

Untuk pertanyaan atau issues terkait structured schema:
- 📧 **Email**: <EMAIL>
- 💬 **Documentation**: Lihat file ini
- 🔍 **Testing**: Gunakan Google Rich Results Test

---

**Last Updated**: January 2024  
**Version**: 1.0.0 