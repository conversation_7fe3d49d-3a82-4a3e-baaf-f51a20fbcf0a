export default defineNuxtConfig({
    devtools: { enabled: process.env.NODE_ENV === 'development' },
    site: {
        url: process.env.NUXT_PUBLIC_SITE_URL || 'https://www.jogjaliburan.com',
        trailingSlash: true
    },

    modules: [
        '@nuxtjs/sitemap',
        '@nuxt/content',
        '@nuxtjs/tailwindcss',
        '@vueuse/nuxt',
        '@nuxt/image',
    ],

    // Konfigurasi Image module untuk external domains
    image: {
        // Disable optimization for BunnyCDN to prevent Vercel processing
        domains: ['liburanjogja.b-cdn.net', 'www.jogjaliburan.com', 'jogjaliburan.com'],
        providers: {
          bunny: {
            provider: 'bunny',
            options: {
              baseURL: 'https://liburanjogja.b-cdn.net'
            }
          }
        },
        // Disable Vercel Image Optimization for production
        ...(process.env.VERCEL ? {
          provider: 'none'
        } : {}),
        // Force external URLs to bypass optimization
        presets: {
          bunnycdn: {
            modifiers: {
              format: 'webp',
              quality: 80
            }
          }
        }
    },

    // Konfigurasi sitemap
    sitemap: {
        defaults: {
            changefreq: 'daily',
            priority: 0.5,
            lastmod: new Date().toISOString()
        },
        sources: [
            // Source untuk sewa mobil dinamis
            '/api/sitemap/sewa-mobil',
            // Source untuk paket wisata dinamis
            '/api/sitemap/paket-wisata',
            // Source untuk paket honeymoon dinamis
            '/api/sitemap/paket-honeymoon',
            // Source untuk paket gathering dinamis
            '/api/sitemap/paket-gathering'
        ]
    },

    // Simple content configuration
    content: {},

    app: {
        head: {
            title: '3J Tour - Liburan Terbaik di Jogja',
            meta: [
                { name: 'description', content: 'Professional travel tour website offering the best destinations around the world' },
                { name: 'google-site-verification', content: 'LfcLcfVK2oDOl1qJ1s13gKrdTku3Xkpeo95zhyMPi70' }
            ],
            link: [
                { rel: 'stylesheet', href: 'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap' },
                { rel: 'stylesheet', href: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap' }
            ],
            script: [
                {
                    innerHTML: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-M4X39CC');`,
                    type: 'text/javascript'
                },
                {
                    src: 'https://www.googletagmanager.com/gtag/js?id=AW-606851190',
                    async: true
                },
                {
                    innerHTML: `
                        window.dataLayer = window.dataLayer || [];
                        function gtag(){dataLayer.push(arguments);}
                        gtag('js', new Date());
                        gtag('config', 'AW-606851190');
                    `,
                    type: 'text/javascript'
                }
            ],
            noscript: [
                {
                    innerHTML: `<iframe src="https://www.googletagmanager.com/ns.html?id=GTM-M4X39CC"
height="0" width="0" style="display:none;visibility:hidden"></iframe>`
                }
            ]
        }
    },
    nitro: {
        // Preset hanya diatur untuk production/build, tidak untuk development
        ...(process.env.NODE_ENV === 'production' || process.env.NITRO_PRESET ? {
          preset: process.env.NITRO_PRESET || 'cloudflare-pages',
          static: true,
        } : {}),
    
        // Konfigurasi untuk API endpoints dan public assets
        publicAssets: [
          {
            baseURL: 'data',
            dir: 'data',
            maxAge: 60 * 60 * 24 * 365 // 1 tahun
          },
          {
            baseURL: '/',
            dir: 'public',
            maxAge: 60 * 60 * 24 * 365 // 1 tahun untuk aset statis
          }
        ],
        // Konfigurasi untuk API
        routeRules: {
          '/api/**': {
            prerender: true,
            cache: {
              maxAge: 60 * 60 * 24 // 1 hari
            }
          },
          // Redirects untuk URL sewa mobil lama ke struktur baru
          '/sewa-mobil-xpander/': { redirect: '/sewa-mobil-jogja/sewa-mobil-xpander-jogja/' },
          '/sewa-mobil-all-new-innova/': { redirect: '/sewa-mobil-jogja/sewa-mobil-all-new-innova-jogja/' },
          '/sewa-mobil-hyundai-h1/': { redirect: '/sewa-mobil-jogja/sewa-mobil-hyundai-h-1-jogja/' },
          '/sewa-mobil-all-new-avanza/': { redirect: '/sewa-mobil-jogja/sewa-mobil-avanza-jogja/' },
          '/sewa-mobil-hyundai-h1-royale/': { redirect: '/sewa-mobil-jogja/sewa-mobil-hyundai-h1-royale/' },
          '/sewa-mobil-all-new-innova-zenix/': { redirect: '/sewa-mobil-jogja/sewa-mobil-all-new-innova-zenix/' },
          '/sewa-mobil-elf-short-11-seats/': { redirect: '/sewa-mobil-jogja/sewa-mobil-elf-short-11-seats/' },
          '/sewa-mobil-fortuner-vrz/': { redirect: '/sewa-mobil-jogja/sewa-mobil-fortuner-vrz/' },
          '/sewa-mobil-new-alphard-transformer/': { redirect: '/sewa-mobil-jogja/sewa-mobil-new-alphard-transformer/' },
          '/sewa-mobil-hiace-commuter-14-seats/': { redirect: '/sewa-mobil-jogja/sewa-mobil-hiace-commuter-14-seats/' },
          '/sewa-hiace-premio-12-14-seats/': { redirect: '/sewa-mobil-jogja/sewa-mobil-hiace-premio-12-14-seats/' },
          '/sewa-bigbus-40-50-seats/': { redirect: '/sewa-mobil-jogja/sewa-mobil-big-bus-40-50-seats/' },
          '/sewa-mobil-grand-avanza/': { redirect: '/sewa-mobil-jogja/sewa-mobil-grand-avanza-jogja/' },
          '/sewa-hiace-premio-luxury/': { redirect: '/sewa-mobil-jogja/sewa-mobil-hiace-premio-luxury/' },
          '/sewa-medium-bus-31-33-seats/': { redirect: '/sewa-mobil-jogja/sewa-mobil-medium-bus-31-33-seats/' },
          // Redirects untuk URL paket wisata dengan hotel lama ke struktur baru
          '/paket-wisata-jogja-dan-hotel-brahmasta/': { redirect: '/paket-wisata-jogja/paket-brahmasta-dengan-hotel/' },
          '/paket-wisata-jogja-dan-hotel-nagapasa/': { redirect: '/paket-wisata-jogja/paket-nagapasa-dengan-hotel/' },
          '/paket-wisata-jogja-hotel-andanu/': { redirect: '/paket-wisata-jogja/paket-andanu-dengan-hotel/' },
          '/paket-wisata-jogja-dan-hotel-kalimasada/': { redirect: '/paket-wisata-jogja/paket-kalimasada-dengan-hotel/' },
          '/paket-wisata-jogja-dan-hotel-gandiwa/': { redirect: '/paket-wisata-jogja/paket-gandiwa-dengan-hotel/' },
          '/paket-wisata-jogja-dan-hotel-rujapala/': { redirect: '/paket-wisata-jogja/paket-rujapala-dengan-hotel/' },
          '/paket-wisata-jogja-dan-hotel-pancanaka/': { redirect: '/paket-wisata-jogja/paket-pancanaka-dengan-hotel/' },
          '/paket-wisata-jogja-dan-hotel-nenggala/': { redirect: '/paket-wisata-jogja/paket-nenggala-dengan-hotel/' },
          '/paket-wisata-jogja-dan-hotel-sudarsana/': { redirect: '/paket-wisata-jogja/paket-sudarsana-dengan-hotel/' },
          '/paket-wisata-jogja-hotel-nagaloka/': { redirect: '/paket-wisata-jogja/paket-nagaloka-dengan-hotel/' },
          '/paket-wisata-jogja-hotel-sempati/': { redirect: '/paket-wisata-jogja/paket-sempati-dengan-hotel/' },
          '/paket-wisata-jogja-hotel-taksaka/': { redirect: '/paket-wisata-jogja/paket-taksaka-dengan-hotel/' },
          '/paket-wisata-jogja-hotel-bendana/': { redirect: '/paket-wisata-jogja/paket-bendana-dengan-hotel/' },
          '/paket-wisata-jogja-hotel-antaboga/': { redirect: '/paket-wisata-jogja/paket-antaboga-dengan-hotel/' },
          '/paket-wisata-jogja-hotel-jatayu/': { redirect: '/paket-wisata-jogja/paket-jatayu-dengan-hotel/' },
          '/paket-wisata-jogja-hotel-bisma/': { redirect: '/paket-wisata-jogja/paket-bisma-dengan-hotel/' },
          '/paket-wisata-jogja-hotel-4d-anoman/': { redirect: '/paket-wisata-jogja/paket-anoman-dengan-hotel/' },
          '/paket-wisata-jogja-hotel-4d-arjuna/': { redirect: '/paket-wisata-jogja/paket-arjuna-dengan-hotel/' },
          // Redirects untuk URL paket wisata tanpa hotel lama ke struktur baru
          '/paket-wisata-jogja-bisma/': { redirect: '/paket-wisata-jogja/4d-bisma/' },
          '/paket-wisata-jogja-4d-anoman/': { redirect: '/paket-wisata-jogja/4d-anoman/' },
          '/paket-wisata-jogja-4d-arjuna/': { redirect: '/paket-wisata-jogja/4d-arjuna/' },
          '/paket-wisata-jogja-nagaloka/': { redirect: '/paket-wisata-jogja/4d-nagaloka/' },
          // Redirects untuk paket wisata tanpa hotel - batch lengkap
          '/paket-wisata-jogja-abimanyu/': { redirect: '/paket-wisata-jogja/abimanyu/' },
          '/paket-wisata-jogja-andanu/': { redirect: '/paket-wisata-jogja/andanu/' },
          '/paket-wisata-jogja-anoman/': { redirect: '/paket-wisata-jogja/anoman/' },
          '/paket-wisata-jogja-antaboga/': { redirect: '/paket-wisata-jogja/antaboga/' },
          '/paket-wisata-jogja-antasena/': { redirect: '/paket-wisata-jogja/antasena/' },
          '/paket-wisata-jogja-arjuna/': { redirect: '/paket-wisata-jogja/arjuna/' },
          '/paket-wisata-jogja-bendana/': { redirect: '/paket-wisata-jogja/bendana/' },
          '/paket-wisata-jogja-brahmasta/': { redirect: '/paket-wisata-jogja/brahmasta/' },
          '/paket-wisata-jogja-bugisan/': { redirect: '/paket-wisata-jogja/bugisan/' },
          '/paket-wisata-jogja-gandiwa/': { redirect: '/paket-wisata-jogja/gandiwa/' },
          '/paket-wisata-jogja-gatotkaca/': { redirect: '/paket-wisata-jogja/gatotkaca/' },
          '/paket-wisata-jogja-jagakarya/': { redirect: '/paket-wisata-jogja/jagakarya/' },
          '/paket-wisata-jogja-jatayu/': { redirect: '/paket-wisata-jogja/jatayu/' },
          '/paket-wisata-jogja-kalimasada/': { redirect: '/paket-wisata-jogja/kalimasada/' },
          '/paket-wisata-jogja-kresna/': { redirect: '/paket-wisata-jogja/kresna/' },
          '/paket-wisata-jogja-mantrijero/': { redirect: '/paket-wisata-jogja/mantrijero/' },
          '/paket-wisata-jogja-nagapasa/': { redirect: '/paket-wisata-jogja/nagapasa/' },
          '/paket-wisata-jogja-nenggala/': { redirect: '/paket-wisata-jogja/nenggala/' },
          '/paket-wisata-jogja-pancanaka/': { redirect: '/paket-wisata-jogja/pancanaka/' },
          '/paket-wisata-jogja-rujapala/': { redirect: '/paket-wisata-jogja/rujapala/' },
          '/paket-wisata-jogja-sempati/': { redirect: '/paket-wisata-jogja/sempati/' },
          '/paket-wisata-jogja-sudarsana/': { redirect: '/paket-wisata-jogja/sudarsana/' },
          '/paket-wisata-jogja-taksaka/': { redirect: '/paket-wisata-jogja/taksaka/' },
          // Redirects untuk kategori dan halaman utama
          '/paket-wisata-tanpa-hotel/': { redirect: '/paket-wisata-jogja/tanpa-hotel/' },
          '/paket-wisata-hotel/': { redirect: '/paket-wisata-jogja/dengan-hotel/' },
          // Redirects untuk paket gathering
          '/paket-gathering-jogja-ananta/': { redirect: '/paket-gathering/paket-gathering-jogja-ananta/' },
          '/paket-gathering-jogja-bhagawanta/': { redirect: '/paket-gathering/paket-gathering-jogja-bhagawanta/' },
          '/paket-gathering-jogja-jayantaka/': { redirect: '/paket-gathering/paket-gathering-jogja-jayantaka/' },
          '/paket-gathering-jogja-padmana/': { redirect: '/paket-gathering/paket-gathering-jogja-padmana/' },
          // Redirects untuk paket honeymoon
          '/paket-honeymoon-ramayana/': { redirect: '/paket-honeymoon/paket-honeymoon-ramayana/' },
          '/paket-honeymoon-mahabarata/': { redirect: '/paket-honeymoon/paket-honeymoon-mahabrata/' },
          '/paket-honeymoon-anjani/': { redirect: '/paket-honeymoon/paket-honeymoon-anjani/' },
          '/paket-honeymoon-srikandi/': { redirect: '/paket-honeymoon/paket-honeymoon-srikandi/' }
        },
        // Konfigurasi untuk data JSON
        bundledStorage: ['data'],
        // Konfigurasi untuk menghindari warning D1
        storage: {
          // Gunakan storage filesystem untuk menghindari D1
          data: {
            driver: 'fs',
            base: './data'
          }
        },
    
        // Konfigurasi prerender untuk SSG
        prerender: {
          // Penting untuk Cloudflare Pages route matching
          autoSubfolderIndex: false,
    
          // Prerender semua rute statis
          routes: [
            '/',
            '/blog/',
            '/tags/',
            '/authors/',
            '/paket-wisata-jogja/',
            '/sewa-mobil-jogja/',
            '/paket-honeymoon/',
                '/paket-gathering/',
            '/404/',
            '/sitemap.xml',
            '/robots.txt',
            // API endpoints penting
          ],
    
          // Crawl links untuk menemukan halaman terkait, termasuk blog dan tags
          crawlLinks: true,
    
          // Jangan gagal build jika ada error prerender
          failOnError: false,
    
          // Abaikan rute yang tidak perlu di-prerender
          ignore: [
            '/admin/**'
          ]
        }
      },
    
    
      // Konfigurasi vite untuk handling proxy requests
      vite: {
        build: {
          cssCodeSplit: true,
          chunkSizeWarningLimit: 1000,
          rollupOptions: {
            output: {
              manualChunks: {
                'vendor': [
                  'vue',
                  'vue-router',
                  '@vueuse/core'
                ]
              }
            }
          }
        },
        optimizeDeps: {
          include: [
            'vue',
            'vue-router'
          ]
        }
      },
    
    compatibilityDate: '2025-03-03'
})
