import { ref, computed } from 'vue'
import paketWisataData from '~/data/paket-wisata.json'

export const usePaketWisata = () => {
  const paketWisata = ref(paketWisataData)
  const isLoading = ref(false)
  const error = ref(null)

  // Get all paket wisata
  const getAllPaketWisata = () => {
    return paketWisata.value
  }

  // Get paket wisata by slug
  const getPaketWisataBySlug = (slug) => {
    return paketWisata.value.find(item => item.slug === slug)
  }

  // Get paket wisata by category
  const getPaketWisataByCategory = (category) => {
    return paketWisata.value.filter(item => item.category === category)
  }

  // Get paket wisata by duration
  const getPaketWisataByDuration = (duration) => {
    return paketWisata.value.filter(item => item.duration === duration)
  }

  // Get all categories
  const getAllCategories = computed(() => {
    const categories = paketWisata.value.map(item => item.category)
    return [...new Set(categories)].map(category => {
      const label = {
        'tanpa-hotel': 'Wisata Jogja Tanpa Hotel',
        'dengan-hotel': 'Wisata Jogja Dengan Hotel',
        'honeymoon': 'Honeymoon',
        'gathering': 'Gathering'
      }
      return {
        slug: category,
        label: label[category] || category
      }
    })
  })

  // Get all durations
  const getAllDurations = computed(() => {
    const durations = paketWisata.value.map(item => item.duration)
    return [...new Set(durations)].map(duration => {
      // Format duration label
      const label = duration
        .replace(/-/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase())
      
      return {
        slug: duration,
        label: label
      }
    }).sort((a, b) => {
      // Sort by number of days
      const daysA = parseInt(a.slug.split('-')[0])
      const daysB = parseInt(b.slug.split('-')[0])
      return daysA - daysB
    })
  })

  // Get category label
  const getCategoryLabel = (category) => {
    const labels = {
     'tanpa-hotel': 'Wisata Jogja Tanpa Hotel',
        'dengan-hotel': 'Wisata Jogja Dengan Hotel',
        'honeymoon': 'Honeymoon',
        'gathering': 'Gathering'
    }
    
    return labels[category] || category
  }

  // Get duration label
  const getDurationLabel = (duration) => {
    return duration
      .replace(/-/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
  }

  return {
    paketWisata,
    isLoading,
    error,
    getAllPaketWisata,
    getPaketWisataBySlug,
    getPaketWisataByCategory,
    getPaketWisataByDuration,
    getAllCategories,
    getAllDurations,
    getCategoryLabel,
    getDurationLabel
  }
}
