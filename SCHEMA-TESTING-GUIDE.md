# Schema Markup Testing Guide - 3J Tour Jog<PERSON>an 🧪

## 🔬 **Testing Tools & URLs**

### **Primary Testing Tools:**

#### **1. Google Rich Results Test**
```
https://search.google.com/test/rich-results
```
**Test these URLs:**
- Blog Index: `https://www.jogjaliburan.com/blog/`
- Sample Article: `https://www.jogjaliburan.com/panduan-lengkap-wisata-jogja-2025-tips-dan-destinasi-terbaik/`
- Tags Index: `https://www.jogjaliburan.com/tags/`
- Sample Tag: `https://www.jogjaliburan.com/tags/candi/`

#### **2. Schema.org Validator**
```
https://validator.schema.org/
```

#### **3. Google Search Console**
```
https://search.google.com/search-console/
```
**Monitor:** Enhancements > Structured Data

---

## 📋 **Schema Types to Validate**

### **Organization Schema**
**Expected Properties:**
- ✅ name: "3J <PERSON> - <PERSON>g<PERSON>"
- ✅ address: Jl. <PERSON>, Modalan, Banguntapan
- ✅ telephone: +62-851-8688-8837
- ✅ geo: -7.822014292198785, 110.40547447500533
- ✅ openingHours: Mo-Su 00:00-23:59
- ✅ sameAs: [Facebook, Instagram, YouTube links]

### **Article Schema**
**Expected Properties:**
- ✅ headline: Article title
- ✅ author: 3J Tour Team or specific author
- ✅ datePublished: Article date
- ✅ publisher: 3J Tour organization
- ✅ mainEntityOfPage: Article URL
- ✅ image: Article featured image
- ✅ about: Wisata Yogyakarta

### **BreadcrumbList Schema**
**Expected Structure:**
```json
{
  "@type": "BreadcrumbList",
  "itemListElement": [
    {"position": 1, "name": "Beranda", "item": "https://www.jogjaliburan.com"},
    {"position": 2, "name": "Tags", "item": "https://www.jogjaliburan.com/tags/"},
    {"position": 3, "name": "Article Title", "item": "article-url"}
  ]
}
```

### **WebSite Schema**
**Expected Properties:**
- ✅ name: "3J Tour - Jogja Liburan"
- ✅ url: https://www.jogjaliburan.com
- ✅ potentialAction: SearchAction for blog search
- ✅ publisher: Organization reference

### **TravelAgency Schema**
**Expected Properties:**
- ✅ @type: "TravelAgency"
- ✅ hasMap: Google Maps embed URL
- ✅ priceRange: "$$"
- ✅ aggregateRating: 4.8/5.0
- ✅ paymentAccepted: [Cash, Credit Card, Bank Transfer, Digital Payment]

---

## 🧪 **Testing Checklist**

### **✅ Pre-Test Setup**
- [ ] Ensure all pages are live and accessible
- [ ] Check that JSON-LD is properly embedded
- [ ] Verify no JavaScript errors in console
- [ ] Confirm proper meta tags are rendered

### **📊 Schema Validation Tests**

#### **Test 1: Organization Schema**
```bash
# Test URL: Any page on the site
# Expected: Valid Organization schema
# Check: Contact info, address, geo-coordinates, hours
```

**Validation Points:**
- [ ] Organization name displays correctly
- [ ] Address matches: "Jl. Merpati, Modalan, Banguntapan"
- [ ] Phone number: "+62-851-8688-8837"
- [ ] Coordinates: lat: -7.822014, lng: 110.405474
- [ ] Opening hours: 24/7 (Mo-Su 00:00-23:59)
- [ ] Social media links are valid
- [ ] Logo URL resolves

#### **Test 2: Article Schema**
```bash
# Test URL: Any blog article
# Expected: Valid Article schema with breadcrumbs
# Check: Author, publisher, publication date, images
```

**Validation Points:**
- [ ] Article headline matches page title
- [ ] Author information is present
- [ ] Publisher references Organization schema
- [ ] Publication date is valid ISO format
- [ ] Article image has proper dimensions
- [ ] mainEntityOfPage URL is correct
- [ ] Keywords and tags are included

#### **Test 3: Breadcrumb Schema**
```bash
# Test URL: Blog articles and tag pages
# Expected: Valid BreadcrumbList schema
# Check: Proper hierarchy and position numbering
```

**Validation Points:**
- [ ] Position numbers are sequential (1, 2, 3...)
- [ ] Breadcrumb names are user-friendly
- [ ] URLs are fully qualified (https://)
- [ ] Hierarchy makes logical sense
- [ ] No broken breadcrumb links

#### **Test 4: Website Schema**
```bash
# Test URL: Blog index and main pages
# Expected: Valid WebSite schema with search functionality
# Check: Search action, publisher, language
```

**Validation Points:**
- [ ] Website name is correct
- [ ] URL is canonical
- [ ] Search action target URL is valid
- [ ] Publisher links to Organization
- [ ] Language is set to "id-ID"

#### **Test 5: Local Business Schema**
```bash
# Test URL: Contact or about pages (if using local business schema)
# Expected: Valid TravelAgency schema
# Check: Business hours, location, services
```

**Validation Points:**
- [ ] Business type is "TravelAgency"
- [ ] Address matches Organization schema
- [ ] Opening hours are consistent
- [ ] Service offerings are listed
- [ ] Payment methods are specified
- [ ] Rating information is valid

---

## 🚨 **Common Issues & Fixes**

### **Issue 1: Missing Required Properties**
**Symptoms:**
- "Missing field 'image'" error
- "Missing field 'publisher'" error

**Fix:**
```javascript
// Ensure all required Article schema fields
'image': post.image?.src || `${baseUrl}/images/og-default.jpg`,
'publisher': { '@id': `${baseUrl}/#organization` }
```

### **Issue 2: Invalid Date Format**
**Symptoms:**
- "Invalid datePublished format"

**Fix:**
```javascript
'datePublished': new Date(post.date).toISOString(),
'dateModified': new Date(post.date).toISOString()
```

### **Issue 3: Broken URLs**
**Symptoms:**
- "URL not accessible" warnings

**Fix:**
```javascript
// Ensure all URLs are fully qualified
const canonicalUrl = `${baseUrl}${route.path}${route.path.endsWith('/') ? '' : '/'}`
```

### **Issue 4: Duplicate Schema**
**Symptoms:**
- Multiple organization schemas detected

**Fix:**
```javascript
// Use @graph to group schemas properly
{
  "@context": "https://schema.org",
  "@graph": [organizationSchema, articleSchema, breadcrumbSchema]
}
```

---

## 📈 **Testing Results Expectations**

### **Successful Validation Should Show:**

#### **Google Rich Results Test:**
- ✅ "Page is eligible for rich results"
- ✅ No errors or warnings
- ✅ Rich snippets preview available
- ✅ Breadcrumb navigation visible

#### **Schema.org Validator:**
- ✅ All schema types recognized
- ✅ No missing required properties
- ✅ Valid property types and values
- ✅ Proper @graph structure

#### **Search Console (after indexing):**
- ✅ Structured data items detected
- ✅ No invalid structured data errors
- ✅ Enhanced search results appearance
- ✅ Rich snippets in search results

---

## 🔄 **Post-Deployment Monitoring**

### **Week 1: Initial Validation**
- [ ] Run all schema tests
- [ ] Check Search Console for errors
- [ ] Monitor rich snippets appearance
- [ ] Verify local business listings

### **Week 2-4: Performance Monitoring**
- [ ] Track rich snippet CTR improvements
- [ ] Monitor local search rankings
- [ ] Check knowledge panel appearance
- [ ] Analyze organic traffic changes

### **Monthly: Ongoing Validation**
- [ ] Re-test schema markup for any new content
- [ ] Update any changed business information
- [ ] Monitor Search Console for new issues
- [ ] Track search performance improvements

---

## 🎯 **Success Metrics**

### **Technical Metrics:**
- **Schema Validation**: 100% pass rate
- **Rich Results**: Eligible for all tested URLs
- **Search Console**: Zero structured data errors
- **Page Speed**: No impact from schema implementation

### **SEO Performance Metrics:**
- **Rich Snippets**: Increased appearance in SERPs
- **CTR**: Improved click-through rates
- **Local Rankings**: Higher positions for local searches
- **Knowledge Panel**: Business information displayed

### **Business Impact:**
- **Organic Traffic**: Increased visitors from search
- **Local Visibility**: More local search appearances
- **Brand Recognition**: Enhanced SERP presence
- **Contact Inquiries**: More direct contact from search

---

## 🛠️ **Testing Commands**

### **Manual Testing:**
```bash
# 1. Test rich results
curl -s "https://search.google.com/test/rich-results/result?url=https://www.jogjaliburan.com/blog/"

# 2. Validate JSON-LD
curl -s "https://www.jogjaliburan.com/blog/" | grep -o '<script type="application/ld+json">.*</script>'

# 3. Check meta tags
curl -s "https://www.jogjaliburan.com/blog/" | grep -E '<meta|<title'
```

### **Automated Testing (if needed):**
```javascript
// Puppeteer script to extract schema
const schema = await page.evaluate(() => {
  const scripts = document.querySelectorAll('script[type="application/ld+json"]');
  return Array.from(scripts).map(script => JSON.parse(script.textContent));
});
```

---

## ✅ **Final Validation Checklist**

Before considering the schema implementation complete:

- [ ] All schema types validate without errors
- [ ] Business information is 100% accurate
- [ ] All URLs are accessible and return 200 status
- [ ] JSON-LD is properly formatted and embedded
- [ ] Meta tags complement schema markup
- [ ] Search Console shows no structured data errors
- [ ] Rich snippets appear in search results preview
- [ ] Local business information is consistent across all schemas

**Once all items are checked, the schema markup implementation is ready for production! 🚀**
