<template>
  <div class="tracking-example">
    <!-- Contoh button WhatsApp dengan tracking -->
    <button 
      @click="handleWhatsAppClick" 
      class="bg-green-500 text-white px-4 py-2 rounded"
    >
      Chat WhatsApp (Dengan Tracking)
    </button>

    <!-- Contoh button telepon dengan tracking -->
    <button 
      @click="handlePhoneClick" 
      class="bg-blue-500 text-white px-4 py-2 rounded ml-2"
    >
      Telepon (Dengan Tracking)
    </button>

    <!-- Contoh form dengan tracking -->
    <form @submit="handleFormSubmit" class="mt-4">
      <input 
        v-model="formData.name" 
        type="text" 
        placeholder="Nama" 
        class="border p-2 rounded"
      >
      <button 
        type="submit" 
        class="bg-red-500 text-white px-4 py-2 rounded ml-2"
      >
        Submit Form (Dengan Tracking)
      </button>
    </form>
  </div>
</template>

<script setup lang="ts">
// Import tracking composable
const { trackWhatsAppConversion, trackPhoneConversion, trackFormConversion, trackPageView } = useGoogleAds();

// Form data
const formData = reactive({
  name: ''
});

// WhatsApp click handler
const handleWhatsAppClick = () => {
  const whatsappUrl = 'https://wa.me/628123456789?text=Halo%20saya%20tertarik%20dengan%20paket%20wisata';
  trackWhatsAppConversion(whatsappUrl);
};

// Phone click handler
const handlePhoneClick = () => {
  trackPhoneConversion();
  // Redirect ke nomor telepon
  if (typeof window !== 'undefined') {
    window.location.href = 'tel:+628123456789';
  }
};

// Form submit handler
const handleFormSubmit = (event: Event) => {
  event.preventDefault();
  trackFormConversion('contact_form');
  
  // Process form submission here
  console.log('Form submitted:', formData);
  
  // Reset form
  formData.name = '';
};

// Track page view on component mount
onMounted(() => {
  if (typeof window !== 'undefined') {
    trackPageView(document.title, window.location.href);
  }
});
</script>

<style scoped>
.tracking-example {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
}
</style> 