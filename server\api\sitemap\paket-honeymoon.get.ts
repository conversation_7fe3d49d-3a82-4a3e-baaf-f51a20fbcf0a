export default defineEventHandler(async (event) => {
  try {
    // Import data honeymoon
    const honeymoonData = await import('~/data/honeymoon.json')
    const { honeymoonPackages } = honeymoonData.default || honeymoonData
    
    // Generate sitemap URLs untuk paket honeymoon
    const urls = honeymoonPackages.map((paket: any) => ({
      loc: `/paket-honeymoon/${paket.slug}/`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: 0.8
    }))
    
    return urls
  } catch (error) {
    console.error('Error generating paket honeymoon sitemap:', error)
    return []
  }
}) 