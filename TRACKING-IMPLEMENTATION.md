# 🎯 TRACKING IMPLEMENTATION GUIDE - 3J TOUR NUXT 3

## 📋 Overview
Panduan lengkap implementasi Google Tag Manager (GTM) dan <PERSON> Ads conversion tracking di website Nuxt 3 menggantikan script WordPress lama.

## 🚀 What's Implemented

### 1. Google Tag Manager (GTM)
- **ID**: `GTM-M4X39CC`
- **Implementasi**: Manual script di `nuxt.config.ts`
- **Plugin**: Custom plugin di `plugins/gtm.client.ts`
- **Features**: 
  - Auto page tracking
  - SPA route change tracking
  - Cross-device tracking

### 2. Google Ads Conversion Tracking
- **ID**: `AW-606851190`
- **Composable**: `composables/useGoogleAds.ts`
- **Conversion ID**: `dfOLCLzgx4cYEPagr6EC` (WhatsApp)

### 3. Google Site Verification
- **Meta tag**: `LfcLcfVK2oDOl1qJ1s13gKrdTku3Xkpeo95zhyMPi70`

### 4. Mobile Menu (Pengganti jQuery)
- **Plugin**: `plugins/mobile-menu.client.ts`
- **Framework**: Vanilla JavaScript (Vue 3 compatible)

## 🛠 Cara Menggunakan

### A. WhatsApp Conversion Tracking

```vue
<template>
  <button @click="handleWhatsAppClick">
    Chat WhatsApp
  </button>
</template>

<script setup>
const { trackWhatsAppConversion } = useGoogleAds();

const handleWhatsAppClick = () => {
  const whatsappUrl = 'https://wa.me/628123456789?text=Halo%20saya%20tertarik%20dengan%20paket%20wisata';
  trackWhatsAppConversion(whatsappUrl);
};
</script>
```

### B. Phone Conversion Tracking

```vue
<template>
  <a @click="handlePhoneClick" href="tel:+628123456789">
    Telepon Kami
  </a>
</template>

<script setup>
const { trackPhoneConversion } = useGoogleAds();

const handlePhoneClick = () => {
  trackPhoneConversion();
};
</script>
```

### C. Form Conversion Tracking

```vue
<template>
  <form @submit="handleFormSubmit">
    <input v-model="formData.name" type="text" placeholder="Nama">
    <button type="submit">Submit</button>
  </form>
</template>

<script setup>
const { trackFormConversion } = useGoogleAds();

const formData = reactive({ name: '' });

const handleFormSubmit = (event) => {
  event.preventDefault();
  trackFormConversion('contact_form');
  // Process form...
};
</script>
```

### D. Custom Page View Tracking

```vue
<script setup>
const { trackPageView } = useGoogleAds();

onMounted(() => {
  if (typeof window !== 'undefined') {
    trackPageView('Paket Wisata Jogja', window.location.href);
  }
});
</script>
```

## 📊 Available Tracking Functions

### useGoogleAds Composable

| Function | Description | Parameters |
|----------|-------------|------------|
| `trackWhatsAppConversion(url?)` | Track WhatsApp click with redirect | `url`: Optional WhatsApp URL |
| `trackPhoneConversion()` | Track phone call clicks | None |
| `trackFormConversion(formType)` | Track form submissions | `formType`: String (e.g., 'contact_form') |
| `trackPageView(title, location)` | Track custom page views | `title`: Page title, `location`: URL |

## 🎨 Styling & CSS

### Mobile Menu CSS (Untuk Theme Compatibility)
```css
/* Add to your main CSS file */
ul.et_mobile_menu li.menu-item-has-children[data-icon="P"]::after,
ul.et_mobile_menu li.page_item_has_children[data-icon="P"]::after {
  content: "P";
  font-family: 'ETmodules';
  speak: none;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  color: #333;
}

ul.et_mobile_menu li.dt-open .mobile-toggle::after {
  transform: rotate(180deg);
}

ul.et_mobile_menu li.is-hover {
  background-color: #f9f9f9;
}

ul.et_mobile_menu ul.children.visible,
ul.et_mobile_menu ul.sub-menu.visible {
  display: block !important;
  opacity: 1;
  visibility: visible;
}
```

## 🧪 Testing

### 1. GTM Testing
- Buka Developer Tools → Network
- Check for `googletagmanager.com` requests
- Verify `dataLayer` events in Console

### 2. Google Ads Testing
- Buka Chrome DevTools → Console
- Test click events
- Check conversion dalam Google Ads dashboard (24-48 jam delay)

### 3. Mobile Menu Testing
- Test di mobile/responsive mode
- Verify dropdown functionality
- Check hover states

## 🔧 Configuration Files

### nuxt.config.ts
```typescript
app: {
  head: {
    script: [
      {
        innerHTML: `(function(w,d,s,l,i){...})(window,document,'script','dataLayer','GTM-M4X39CC');`,
        type: 'text/javascript'
      },
      {
        src: 'https://www.googletagmanager.com/gtag/js?id=AW-606851190',
        async: true
      }
    ],
    noscript: [
      {
        innerHTML: `<iframe src="https://www.googletagmanager.com/ns.html?id=GTM-M4X39CC"...></iframe>`
      }
    ]
  }
}
```

## 📈 Migration Benefits

### ✅ Improvements dari WordPress
1. **Performance**: Lebih cepat loading tanpa jQuery dependency
2. **SEO**: Better Core Web Vitals scores
3. **Maintenance**: Easier to update dan debug
4. **Type Safety**: TypeScript support
5. **SSR/SPA**: Full compatibility dengan Nuxt 3

### ✅ Features Baru
1. **Auto Route Tracking**: Track SPA navigation otomatis
2. **Enhanced Ecommerce**: Ready untuk implementasi
3. **Custom Events**: Flexible event tracking
4. **Error Handling**: Better error boundaries

## 🚨 Important Notes

### 1. Environment Variables
```bash
# .env
NUXT_PUBLIC_GTM_ID=GTM-M4X39CC
NUXT_PUBLIC_GA_ID=AW-606851190
```

### 2. GDPR Compliance
- Implement cookie consent
- Respect Do Not Track headers
- Add privacy policy links

### 3. Testing Environment
- Use GTM Preview mode untuk testing
- Set up separate GTM containers untuk dev/staging

## 🔗 Resources

- [Google Tag Manager](https://tagmanager.google.com)
- [Google Ads Conversion Tracking](https://support.google.com/google-ads/answer/1722054)
- [Nuxt 3 Head Management](https://nuxt.com/docs/api/composables/use-head)

## 🆘 Troubleshooting

### Error: "gtag is not defined"
**Solution**: Pastikan plugin `gtm.client.ts` loaded sebelum component usage

### Error: "Cannot read properties of undefined"
**Solution**: Add client-side checks `typeof window !== 'undefined'`

### Mobile menu tidak berfungsi
**Solution**: Check CSS selector dan verify DOM structure

---
**Last Updated**: 2024
**Version**: 1.0
**Team**: 3J Tour Development 