{"compilerOptions": {"paths": {"nitropack/types": ["../node_modules/nitropack/types"], "nitropack/runtime": ["../node_modules/nitropack/runtime"], "nitropack": ["../node_modules/nitropack"], "defu": ["../node_modules/defu"], "h3": ["../node_modules/h3"], "consola": ["../node_modules/consola"], "ofetch": ["../node_modules/ofetch"], "@nuxt/devtools": ["../node_modules/@nuxt/devtools"], "@vue/runtime-core": ["../node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/@nuxt/schema"], "nuxt": ["../node_modules/nuxt"], "vite/client": ["../node_modules/vite/client"], "~": [".."], "~/*": ["../*"], "@": [".."], "@/*": ["../*"], "~~": [".."], "~~/*": ["../*"], "@@": [".."], "@@/*": ["../*"], "#shared": ["../shared"], "assets": ["../assets"], "assets/*": ["../assets/*"], "public": ["../public"], "public/*": ["../public/*"], "#app": ["../node_modules/nuxt/dist/app"], "#app/*": ["../node_modules/nuxt/dist/app/*"], "vue-demi": ["../node_modules/nuxt/dist/app/compat/vue-demi"], "#sitemap": ["../node_modules/@nuxtjs/sitemap/dist/runtime"], "#sitemap/*": ["../node_modules/@nuxtjs/sitemap/dist/runtime/*"], "#site-config": ["../node_modules/nuxt-site-config/dist/runtime"], "#site-config/*": ["../node_modules/nuxt-site-config/dist/runtime/*"], "#content/components": ["./content/components"], "#content/manifest": ["./content/manifest"], "#mdc-configs": ["./mdc-configs"], "#mdc-highlighter": ["./mdc-highlighter"], "#mdc-imports": ["./mdc-imports"], "#image": ["../node_modules/@nuxt/image/dist/runtime"], "#image/*": ["../node_modules/@nuxt/image/dist/runtime/*"], "#vue-router": ["../node_modules/vue-router"], "#unhead/composables": ["../node_modules/nuxt/dist/head/runtime/composables/v3"], "#imports": ["./imports"], "#app-manifest": ["./manifest/meta/dev.json"], "#components": ["./components"], "#build": ["."], "#build/*": ["./*"]}, "esModuleInterop": true, "skipLibCheck": true, "target": "ESNext", "allowJs": true, "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "verbatimModuleSyntax": true, "strict": true, "noUncheckedIndexedAccess": false, "forceConsistentCasingInFileNames": true, "noImplicitOverride": true, "module": "preserve", "noEmit": true, "lib": ["ESNext", "dom", "dom.iterable", "webworker"], "jsx": "preserve", "jsxImportSource": "vue", "types": [], "moduleResolution": "<PERSON><PERSON><PERSON>", "useDefineForClassFields": true, "noImplicitThis": true, "allowSyntheticDefaultImports": true}, "include": ["../**/*", "../.config/nuxt.*", "./nuxt.d.ts", "../node_modules/nuxt-site-config/dist/runtime", "../node_modules/nuxt-site-config/dist/dist/runtime", "../node_modules/@nuxtjs/sitemap/runtime", "../node_modules/@nuxtjs/sitemap/dist/runtime", "../node_modules/@nuxtjs/mdc/runtime", "../node_modules/@nuxtjs/mdc/dist/runtime", "../node_modules/@nuxt/content/runtime", "../node_modules/@nuxt/content/dist/runtime", "../node_modules/@nuxtjs/tailwindcss/runtime", "../node_modules/@nuxtjs/tailwindcss/dist/runtime", "../node_modules/@vueuse/nuxt/runtime", "../node_modules/@vueuse/nuxt/dist/runtime", "../node_modules/@nuxt/image/runtime", "../node_modules/@nuxt/image/dist/runtime", "../node_modules/@nuxt/devtools/runtime", "../node_modules/@nuxt/devtools/dist/runtime", "../node_modules/@nuxt/telemetry/runtime", "../node_modules/@nuxt/telemetry/dist/runtime", ".."], "exclude": ["../dist", "../.data", "../node_modules", "../node_modules/nuxt/node_modules", "../node_modules/nuxt-site-config/node_modules", "../node_modules/@nuxtjs/sitemap/node_modules", "../node_modules/@nuxtjs/mdc/node_modules", "../node_modules/@nuxt/content/node_modules", "../node_modules/@nuxtjs/tailwindcss/node_modules", "../node_modules/@vueuse/nuxt/node_modules", "../node_modules/@nuxt/image/node_modules", "../node_modules/@nuxt/devtools/node_modules", "../node_modules/@nuxt/telemetry/node_modules", "../node_modules/nuxt-site-config/dist/runtime/server", "../node_modules/nuxt-site-config/dist/dist/runtime/server", "../node_modules/@nuxtjs/sitemap/runtime/server", "../node_modules/@nuxtjs/sitemap/dist/runtime/server", "../node_modules/@nuxtjs/mdc/runtime/server", "../node_modules/@nuxtjs/mdc/dist/runtime/server", "../node_modules/@nuxt/content/runtime/server", "../node_modules/@nuxt/content/dist/runtime/server", "../node_modules/@nuxtjs/tailwindcss/runtime/server", "../node_modules/@nuxtjs/tailwindcss/dist/runtime/server", "../node_modules/@vueuse/nuxt/runtime/server", "../node_modules/@vueuse/nuxt/dist/runtime/server", "../node_modules/@nuxt/image/runtime/server", "../node_modules/@nuxt/image/dist/runtime/server", "../node_modules/@nuxt/devtools/runtime/server", "../node_modules/@nuxt/devtools/dist/runtime/server", "../node_modules/@nuxt/telemetry/runtime/server", "../node_modules/@nuxt/telemetry/dist/runtime/server", "../.output"]}