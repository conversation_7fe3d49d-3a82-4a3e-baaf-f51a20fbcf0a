<template>
  <!-- Direct img tag - bypasses all Nuxt Image processing -->
  <figure class="my-16 mb-20">
    <div class="relative w-full overflow-hidden bg-gray-50 rounded-lg">
      <!-- Loading placeholder -->
      <div v-if="!imageLoaded" class="w-full h-[400px] md:h-[500px] bg-gray-200 animate-pulse rounded-lg flex items-center justify-center">
        <div class="text-gray-500 text-sm">Memuat gambar...</div>
      </div>

      <!-- Error placeholder -->
      <div v-if="imageError" class="w-full h-[400px] md:h-[500px] bg-gray-100 rounded-lg flex items-center justify-center">
        <div class="text-center text-gray-500">
          <div class="text-sm mb-2">❌ Gambar tidak dapat dimuat</div>
          <div class="text-xs">{{ src }}</div>
        </div>
      </div>

      <!-- Direct image - no Nuxt Image processing -->
      <img
        v-show="!imageError"
        :src="finalSrc"
        :alt="alt"
        :width="width || 1200"
        :height="height || 800"
        class="w-full h-auto object-contain rounded-lg shadow-md mx-auto max-h-[600px]"
        :class="{ 'opacity-0': !imageLoaded, 'opacity-100 transition-opacity duration-300': imageLoaded }"
        loading="lazy"
        crossorigin="anonymous"
        @error="handleError"
        @load="handleLoad"
      />
    </div>
    <figcaption v-if="alt" class="text-center text-sm text-gray-500 mt-4 pt-2">
      {{ alt }}
    </figcaption>
  </figure>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface Props {
  src: string
  alt?: string
  width?: string | number
  height?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  alt: '',
  width: 1200,
  height: 800
})

const imageLoaded = ref(false)
const imageError = ref(false)

// Compute final src - ensure BunnyCDN URL
const finalSrc = computed(() => {
  // If already absolute URL, use as-is
  if (props.src.startsWith('http')) {
    return props.src
  }
  
  // If relative, convert to BunnyCDN
  const cleanPath = props.src.startsWith('/') ? props.src.substring(1) : props.src
  return `https://liburanjogja.b-cdn.net/${cleanPath}`
})

const handleError = () => {
  console.error('DirectImg failed to load:', finalSrc.value)
  imageError.value = true
  imageLoaded.value = true
}

const handleLoad = () => {
  imageLoaded.value = true
  imageError.value = false
  console.log('DirectImg loaded successfully:', finalSrc.value)
}

onMounted(() => {
  console.log('DirectImg mounted - bypassing Nuxt Image:', finalSrc.value)
})
</script> 