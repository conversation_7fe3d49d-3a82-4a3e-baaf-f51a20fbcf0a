<script setup lang="ts">
const props = defineProps({
  post: {
    type: Object,
    required: true
  }
});

// Helper function untuk mendapatkan URL post
function getPostUrl(post) {
  // Coba semua kemungkinan properti path
  if (post.path) return post.path;
  if (post._path) return post._path;
  
  // Fallback ke slug dari _path atau _file
  const slug = post._path?.split('/').pop() || 
               post._file?.split('/').pop()?.replace('.md', '') ||
               post._id?.split(':').pop();
  
  return `/blog/${slug}`;
}

// Format tanggal menggunakan Intl.DateTimeFormat
const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('id-ID', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
};
</script>

<template>
  <article 
    class="col-span-full mb-8 bg-white rounded-lg shadow-md overflow-hidden"
    itemscope 
    itemtype="https://schema.org/BlogPosting"
  >
    <div class="flex flex-col md:flex-row">
      <!-- Featured Image -->
      <div class="md:w-1/2">
        <a :href="getPostUrl(post)" itemprop="url">
          <img 
            :src="post.image?.src" 
            :alt="post.title" 
            class="w-full h-64 md:h-full object-cover"
            itemprop="image"
          />
        </a>
      </div>
      
      <!-- Content -->
      <div class="md:w-1/2 p-6 flex flex-col justify-between">
        <div>
          <!-- Meta Information -->
          <div class="flex items-center mb-2">
            <time 
              :datetime="post.date" 
              class="text-sm text-gray-500"
              itemprop="datePublished"
            >
              {{ formatDate(post.date) }}
            </time>
            <span class="mx-2 text-gray-300">u2022</span>
            <span 
              class="text-sm text-gray-500"
              itemprop="author" 
              itemscope 
              itemtype="https://schema.org/Person"
            >
              <span itemprop="name">{{ post.authors?.[0]?.name }}</span>
            </span>
          </div>
          
          <!-- Title -->
          <h2 class="text-2xl font-bold mb-3" itemprop="headline">
            <a :href="getPostUrl(post)" class="text-gray-900 hover:text-blue-600 transition">
              {{ post.title }}
            </a>
          </h2>
          
          <!-- Description -->
          <p class="text-gray-600 mb-4" itemprop="description">{{ post.description }}</p>
          
          <!-- Tags -->
          <div v-if="post.tags && post.tags.length" class="flex flex-wrap gap-2 mb-3">
            <a 
              v-for="tag in post.tags.slice(0, 3)" 
              :key="tag"
              :href="`/blog/tags/${tag.toLowerCase()}`"
              class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full hover:bg-blue-100 hover:text-blue-800 transition-colors"
            >
              #{{ tag }}
            </a>
            <span v-if="post.tags.length > 3" class="text-xs text-gray-500">+{{ post.tags.length - 3 }} more</span>
          </div>
          
          <!-- Category Badge -->
          <div v-if="post.badge" class="mb-2">
            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
              {{ post.badge.label }}
            </span>
          </div>
        </div>
        
        <!-- Read More Link -->
        <a 
          :href="getPostUrl(post)" 
          class="text-blue-600 hover:text-blue-800 font-medium inline-flex items-center mt-4"
          aria-label="Read more about {{ post.title }}"
        >
          Baca selengkapnya
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
          </svg>
        </a>
      </div>
    </div>
  </article>
</template>
