export default defineEventHandler(async (event) => {
  try {
    // Import data sewa mobil
    const sewaMobilData = await import('~/data/sewa-mobil.json')
    const { sewaMobil } = sewaMobilData.default || sewaMobilData
    
    // Generate sitemap URLs untuk sewa mobil
    const urls = sewaMobil.map((mobil: any) => ({
      loc: `/sewa-mobil-jogja/${mobil.slug}/`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: 0.8
    }))
    
    return urls
  } catch (error) {
    console.error('Error generating sewa mobil sitemap:', error)
    return []
  }
}) 