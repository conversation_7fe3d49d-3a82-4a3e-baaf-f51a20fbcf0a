// Example: Using SEO Composables in pages/[slug].vue (Updated approach)

<script setup lang="ts">
import { useRoute, useAsyncData, queryCollection } from '#imports'
import type { BlogPost } from '~/types/content'
import BlogImage from '~/components/blog/BlogImage.vue'

// Import SEO composables
const { getOrganizationSchema, getWebsiteSchema, getBreadcrumbSchema, getArticleSchema } = useSchema()
const { getBlogPostMeta, getCommonLinks } = useJogjaSeometa()

const route = useRoute()

// Fetch current post
const { data: post } = await useAsyncData(route.path, () => queryCollection('posts').path(route.path).first())

if (!post.value) {
  throw createError({ statusCode: 404, statusMessage: 'Post not found', fatal: true })
}

// SEO Setup using composables
const baseUrl = 'https://www.jogjaliburan.com'
const canonicalUrl = `${baseUrl}${route.path}${route.path.endsWith('/') ? '' : '/'}`

// Generate breadcrumb items
const breadcrumbItems = [
  { name: 'Beranda', url: baseUrl },
  { name: 'Tags', url: `${baseUrl}/tags/` },
  { name: post.value.title, url: canonicalUrl }
]

// Generate all schemas using composables
const organizationSchema = getOrganizationSchema()
const websiteSchema = getWebsiteSchema()
const breadcrumbSchema = getBreadcrumbSchema(breadcrumbItems)
const articleSchema = getArticleSchema(post.value, canonicalUrl)

// Combined schema graph
const jsonLd = {
  '@context': 'https://schema.org',
  '@graph': [
    organizationSchema,
    websiteSchema,
    articleSchema,
    breadcrumbSchema
  ]
}

// Meta tags using composable
const metaTags = getBlogPostMeta(post.value, canonicalUrl)
const linkTags = getCommonLinks(canonicalUrl)

// Apply SEO meta
useSeoMeta(metaTags)

useHead({
  link: linkTags,
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify(jsonLd)
    }
  ]
})

// Rest of the component logic remains the same...
// Related articles logic, activeSection, etc.

</script>

<!-- Template remains exactly the same -->
<template>
  <!-- Same template as before -->
</template>

<!-- This example shows how the new composables make SEO implementation much cleaner and more maintainable -->
