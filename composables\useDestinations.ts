import { ref } from 'vue'
import paketWisataData from '~/data/paket-wisata.json'

export interface PaketWisata {
  id: string
  slug: string
  title: string
  description: string
  location: string
  price: string
  rating: number
  reviews: number
  image: string
  images?: string[]
  duration: string
  highlights: string[]
  category: string
  itinerary?: any
  hotelClass?: string
  maxParticipants?: number
}

export const useDestinations = () => {
  const destinations = ref<PaketWisata[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  const fetchDestinations = async () => {
    isLoading.value = true
    error.value = null

    try {
      // Simulasi loading
      await new Promise(resolve => setTimeout(resolve, 1000))

      console.log('Raw Data:', paketWisataData)

      // Data paket wisata sudah berupa array langsung, bukan object dengan categories
      destinations.value = paketWisataData as PaketWisata[]

      console.log('Loaded Data:', destinations.value)
    } catch (e: any) {
      error.value = 'Gagal memuat data paket wisata'
      console.error('Error fetching destinations:', e)
    } finally {
      isLoading.value = false
    }
  }

  const getDestinationBySlug = (slug: string): PaketWisata | undefined => {
    return destinations.value.find(dest => dest.slug === slug)
  }

  const getDestinationsByCategory = (category: string): PaketWisata[] => {
    console.log('Getting destinations by category:', category)
    console.log('All destinations:', destinations.value)

    if (!destinations.value || destinations.value.length === 0) {
      console.warn('No destinations available')
      return []
    }

    const filtered = destinations.value.filter(dest => {
      console.log('Checking destination:', dest.title, 'Category:', dest.category, 'Matches:', dest.category === category)
      return dest.category === category
    })

    console.log('Filtered destinations by category:', filtered)
    return filtered
  }

  const getDestinationsByDuration = (duration: string): PaketWisata[] => {
    console.log('Getting destinations by duration:', duration)
    console.log('All destinations:', destinations.value)

    if (!destinations.value || destinations.value.length === 0) {
      console.warn('No destinations available')
      return []
    }

    const filtered = destinations.value.filter(dest => {
      console.log('Checking destination:', dest.title, 'Duration:', dest.duration, 'Matches:', dest.duration === duration)
      return dest.duration === duration
    })

    console.log('Filtered destinations by duration:', filtered)
    return filtered
  }

  return {
    destinations,
    isLoading,
    error,
    fetchDestinations,
    getDestinationBySlug,
    getDestinationsByCategory,
    getDestinationsByDuration
  }
}