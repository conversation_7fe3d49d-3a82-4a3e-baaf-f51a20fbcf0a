export const useSchemaStructured = () => {
  // Base Organization Schema
  const getOrganizationSchema = () => {
    return {
      "@type": "Organization",
      "@id": "https://www.jogjaliburan.com/#organization",
      name: "3J Tour - <PERSON><PERSON><PERSON>",
      alternateName: "3J Tour",
      url: "https://www.jogjaliburan.com",
      logo: {
        "@type": "ImageObject",
        url: "https://www.jogjaliburan.com/3j-tour-logo.webp",
        width: 300,
        height: 100
      },
      contactPoint: {
        "@type": "ContactPoint",
        telephone: "+62-812-3456-7890",
        contactType: "customer service",
        areaServed: "ID",
        availableLanguage: ["Indonesian"]
      },
      address: {
        "@type": "PostalAddress",
        addressLocality: "Yogyakarta",
        addressRegion: "DI Yogyakarta",
        addressCountry: "ID"
      },
      sameAs: [
        "https://www.instagram.com/3jtour",
        "https://wa.me/6285186888837"
      ]
    }
  }

  // Tour Package Schema for individual packages
  const getTourPackageSchema = (paket) => {
    if (!paket) return null

    const pricing = paket.pricing && paket.pricing.length > 0 
      ? paket.pricing.map(p => ({
          "@type": "Offer",
          name: p.label,
          price: p.price.replace(/[^\d]/g, ''),
          priceCurrency: "IDR",
          availability: "https://schema.org/InStock",
          validFrom: new Date().toISOString(),
          description: `Paket ${paket.title} untuk ${p.label}`,
          seller: {
            "@id": "https://www.jogjaliburan.com/#organization"
          }
        }))
      : [{
          "@type": "Offer",
          price: paket.price?.replace(/[^\d]/g, '') || "350000",
          priceCurrency: "IDR",
          availability: "https://schema.org/InStock",
          validFrom: new Date().toISOString(),
          seller: {
            "@id": "https://www.jogjaliburan.com/#organization"
          }
        }]

    return {
      "@type": "TourPackage",
      "@id": `https://www.jogjaliburan.com/paket-wisata-jogja/${paket.slug}/`,
      name: paket.title,
      description: paket.description,
      image: paket.image,
      url: `https://www.jogjaliburan.com/paket-wisata-jogja/${paket.slug}/`,
      touristType: "Families, Couples, Groups",
      duration: `P${paket.duration?.includes('1-hari') ? '1' : paket.duration?.includes('2-hari') ? '2' : paket.duration?.includes('3-hari') ? '3' : paket.duration?.includes('4-hari') ? '4' : '1'}D`,
      location: {
        "@type": "Place",
        name: paket.location || "Yogyakarta",
        address: {
          "@type": "PostalAddress",
          addressLocality: "Yogyakarta",
          addressRegion: "DI Yogyakarta",
          addressCountry: "ID"
        }
      },
      provider: {
        "@id": "https://www.jogjaliburan.com/#organization"
      },
      offers: pricing,
      aggregateRating: {
        "@type": "AggregateRating",
        ratingValue: paket.rating || 4.5,
        reviewCount: paket.reviews || 50,
        bestRating: 5,
        worstRating: 1
      },
      itinerary: paket.highlights ? paket.highlights.map((highlight, index) => ({
        "@type": "Day",
        name: `Aktivitas ${index + 1}`,
        description: highlight
      })) : null
    }
  }

  // Product Schema for packages (for e-commerce features)
  const getProductSchema = (paket) => {
    if (!paket) return null

    return {
      "@type": "Product",
      "@id": `https://www.jogjaliburan.com/paket-wisata-jogja/${paket.slug}/#product`,
      name: paket.title,
      description: paket.description,
      image: paket.image,
      brand: {
        "@id": "https://www.jogjaliburan.com/#organization"
      },
      category: "Travel Package",
      offers: {
        "@type": "AggregateOffer",
        lowPrice: paket.pricing && paket.pricing.length > 0 
          ? Math.min(...paket.pricing.map(p => parseInt(p.price.replace(/[^\d]/g, ''))))
          : parseInt(paket.price?.replace(/[^\d]/g, '') || "350000"),
        highPrice: paket.pricing && paket.pricing.length > 0 
          ? Math.max(...paket.pricing.map(p => parseInt(p.price.replace(/[^\d]/g, ''))))
          : parseInt(paket.price?.replace(/[^\d]/g, '') || "350000"),
        priceCurrency: "IDR",
        availability: "https://schema.org/InStock",
        offerCount: paket.pricing?.length || 1
      },
      aggregateRating: {
        "@type": "AggregateRating",
        ratingValue: paket.rating || 4.5,
        reviewCount: paket.reviews || 50,
        bestRating: 5,
        worstRating: 1
      }
    }
  }

  // Collection Page Schema for category/duration pages
  const getCollectionPageSchema = (title, description, items, pageType, currentSlug) => {
    const baseUrl = "https://www.jogjaliburan.com/paket-wisata-jogja/"
    const fullUrl = currentSlug ? `https://www.jogjaliburan.com/paket-wisata-jogja/${currentSlug}/` : baseUrl

    return {
      "@type": "CollectionPage",
      "@id": `${fullUrl}#collection`,
      name: title,
      description: description,
      url: fullUrl,
      mainEntity: {
        "@type": "ItemList",
        numberOfItems: items?.length || 0,
        itemListElement: items?.slice(0, 10).map((item, index) => ({
          "@type": "ListItem",
          position: index + 1,
          item: {
            "@type": "TourPackage",
            "@id": `https://www.jogjaliburan.com/paket-wisata-jogja/${item.slug}/`,
            name: item.title,
            description: item.description,
            image: item.image,
            url: `https://www.jogjaliburan.com/paket-wisata-jogja/${item.slug}/`,
            offers: {
              "@type": "Offer",
              price: item.pricing?.[0]?.price?.replace(/[^\d]/g, '') || item.price?.replace(/[^\d]/g, '') || "350000",
              priceCurrency: "IDR",
              seller: {
                "@type": "Organization",
                name: "3J Tour - Jogja Liburan",
                url: "https://www.jogjaliburan.com"
              },
              availability: "https://schema.org/InStock",
              validFrom: new Date().toISOString().split('T')[0]
            },
            aggregateRating: {
              "@type": "AggregateRating",
              ratingValue: item.rating || 4.5,
              reviewCount: item.reviews || 50,
              bestRating: 5,
              worstRating: 1
            }
          }
        })) || []
      },
      breadcrumb: getBreadcrumbSchema(pageType, currentSlug),
      provider: {
        "@id": "https://www.jogjaliburan.com/#organization"
      }
    }
  }

  // Breadcrumb Schema
  const getBreadcrumbSchema = (pageType, currentSlug = '', paketTitle = '') => {
    const breadcrumbs = [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: "https://www.jogjaliburan.com/"
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Paket Wisata Jogja",
        item: "https://www.jogjaliburan.com/paket-wisata-jogja/"
      }
    ]

    // For detail pages, add the specific package as level 3
    if (pageType === 'detail' && currentSlug && paketTitle) {
      breadcrumbs.push({
        "@type": "ListItem",
        position: 3,
        name: paketTitle,
        item: `https://www.jogjaliburan.com/paket-wisata-jogja/${currentSlug}/`
      })
    }
    // For category/duration pages, add the category/duration as level 3
    else if (pageType && currentSlug) {
      const pageNames = {
        'tanpa-hotel': 'Paket Wisata Jogja Tanpa Hotel',
        'dengan-hotel': 'Paket Wisata Jogja Dengan Hotel',
        '1-hari': 'Paket Wisata Jogja 1 Hari',
        '2-hari-1-malam': 'Paket Wisata Jogja 2 Hari 1 Malam',
        '3-hari-2-malam': 'Paket Wisata Jogja 3 Hari 2 Malam',
        '4-hari-3-malam': 'Paket Wisata Jogja 4 Hari 3 Malam'
      }

      if (pageNames[currentSlug]) {
        breadcrumbs.push({
          "@type": "ListItem",
          position: 3,
          name: pageNames[currentSlug],
          item: `https://www.jogjaliburan.com/paket-wisata-jogja/${currentSlug}/`
        })
      }
    }

    return {
      "@type": "BreadcrumbList",
      itemListElement: breadcrumbs
    }
  }

  // FAQ Schema
  const getFAQSchema = (faqs) => {
    if (!faqs || faqs.length === 0) return null

    return {
      "@type": "FAQPage",
      mainEntity: faqs.map(faq => ({
        "@type": "Question",
        name: faq.question,
        acceptedAnswer: {
          "@type": "Answer",
          text: faq.answer
        }
      }))
    }
  }

  // Local Business Schema (for location-based searches)
  const getLocalBusinessSchema = () => {
    return {
      "@type": "TravelAgency",
      "@id": "https://www.jogjaliburan.com/#localbusiness",
      name: "3J Tour - Jogja Liburan",
      image: "https://www.jogjaliburan.com/3j-tour-logo.webp",
      telephone: "+62-812-3456-7890",
      email: "<EMAIL>",
      address: {
        "@type": "PostalAddress",
        streetAddress: "Jl. Malioboro No. 123",
        addressLocality: "Yogyakarta",
        addressRegion: "DI Yogyakarta",
        postalCode: "55271",
        addressCountry: "ID"
      },
      geo: {
        "@type": "GeoCoordinates",
        latitude: -7.7955800,
        longitude: 110.3694900
      },
      url: "https://www.jogjaliburan.com",
      priceRange: "IDR 350,000 - IDR 2,000,000",
      servedCuisine: ["Tourism", "Travel Packages"],
      areaServed: {
        "@type": "State",
        name: "DI Yogyakarta"
      },
      hasOfferCatalog: {
        "@type": "OfferCatalog",
        name: "Paket Wisata Jogja",
        itemListElement: [
          {
            "@type": "OfferCatalog",
            name: "Paket Wisata 1 Hari",
            itemListElement: {
              "@type": "Offer",
              itemOffered: {
                "@type": "TourPackage",
                name: "Paket Wisata Jogja 1 Hari"
              }
            }
          }
        ]
      }
    }
  }

  // Main Schema Generator
  const generateSchema = (pageData) => {
    const { pageType, paket, items, title, description, currentSlug, faqs } = pageData
    
    const schemas = []

    // Always include organization
    schemas.push(getOrganizationSchema())

    switch (pageType) {
      case 'detail':
        if (paket) {
          schemas.push(getTourPackageSchema(paket))
          schemas.push(getProductSchema(paket))
          schemas.push(getBreadcrumbSchema('detail', paket.slug, paket.title))
        }
        break

      case 'index':
        schemas.push(getCollectionPageSchema(
          title || "Paket Wisata Jogja Terbaik 2024",
          description || "Temukan paket wisata Jogja terbaik dengan harga terjangkau. Paket 1-4 hari lengkap dengan guide profesional.",
          items,
          'index'
        ))
        schemas.push(getLocalBusinessSchema())
        // Add FAQ only for index page
        if (faqs && faqs.length > 0) {
          schemas.push(getFAQSchema(faqs))
        }
        break

      case 'category':
      case 'duration':
        schemas.push(getCollectionPageSchema(title, description, items, pageType, currentSlug))
        break

      default:
        schemas.push(getCollectionPageSchema(title, description, items, 'default', currentSlug))
    }

    return {
      "@context": "https://schema.org",
      "@graph": schemas.filter(Boolean)
    }
  }

  return {
    generateSchema,
    getOrganizationSchema,
    getTourPackageSchema,
    getProductSchema,
    getCollectionPageSchema,
    getBreadcrumbSchema,
    getFAQSchema,
    getLocalBusinessSchema
  }
} 