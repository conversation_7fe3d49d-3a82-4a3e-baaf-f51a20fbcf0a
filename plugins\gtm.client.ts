export default defineNuxtPlugin(() => {
  // Initialize dataLayer if not exists
  if (typeof window !== 'undefined') {
    window.dataLayer = window.dataLayer || [];
    
    // GTM helper functions
    const gtag = (...args: any[]) => {
      window.dataLayer.push(args);
    };
    
    // Make gtag globally available
    window.gtag = gtag;
    
    // Track route changes for SPA
    const router = useRouter();
    router.afterEach((to) => {
      nextTick(() => {
        gtag('config', 'GTM-M4X39CC', {
          page_path: to.path,
          page_title: document.title,
          page_location: window.location.href
        });
      });
    });
    
    // Initial page track
    nextTick(() => {
      gtag('config', 'GTM-M4X39CC', {
        page_path: window.location.pathname,
        page_title: document.title,
        page_location: window.location.href
      });
    });
  }
});

// Type declarations
declare global {
  interface Window {
    dataLayer: any[];
    gtag: (...args: any[]) => void;
  }
} 