<template>
  <div v-if="tocLinks.length > 0" class="toc-container bg-white rounded-xl shadow-sm border border-gray-200 p-6">
    <!-- Mobile Toggle Button -->
    <div class="lg:hidden mb-4">
      <button
        @click="toggleMobileTOC"
        class="w-full flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
      >
        <span class="font-medium text-gray-900">Table of Contents</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="w-5 h-5 transition-transform"
          :class="{ 'rotate-180': showMobileTOC }"
        >
          <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
        </svg>
      </button>
    </div>

    <!-- Desktop Header -->
    <div class="hidden lg:flex text-lg font-bold mb-4 text-gray-900 items-center gap-2">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-primary">
        <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 17.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
      </svg>
      <span>Table of Contents</span>
    </div>

    <!-- TOC Navigation -->
    <nav
      class="space-y-2 transition-all duration-300 toc-nav"
      :class="{ 'hidden lg:block': !showMobileTOC, 'block': showMobileTOC }"
    >
      <a
        v-for="link in tocLinks"
        :key="link.id"
        :href="`#${link.id}`"
        :class="[
          'block text-sm transition-colors duration-200 hover:text-primary py-1 leading-relaxed',
          'overflow-hidden text-ellipsis whitespace-nowrap',
          link.depth === 2 ? 'font-medium text-gray-900' : '',
          link.depth === 3 ? 'pl-4 text-gray-700' : '',
          link.depth === 4 ? 'pl-8 text-gray-600' : '',
          link.depth > 4 ? 'pl-12 text-gray-500' : '',
          activeId === link.id ? 'text-primary font-medium bg-primary/5 -mx-2 px-2 rounded' : ''
        ]"
        @click="scrollToHeading"
      >
        {{ link.text }}
      </a>
    </nav>

    <!-- Reading Progress -->
    <div
      class="mt-6 pt-4 border-t border-gray-200 transition-all duration-300"
      :class="{ 'hidden lg:block': !showMobileTOC, 'block': showMobileTOC }"
    >
      <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
        <span>Reading Progress</span>
        <span>{{ Math.round(readingProgress) }}%</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div
          class="bg-primary h-2 rounded-full transition-all duration-300"
          :style="{ width: `${readingProgress}%` }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  content: {
    type: Object,
    required: true
  }
})

const activeId = ref('')
const readingProgress = ref(0)
const tocLinks = ref([])
const showMobileTOC = ref(false)

// Toggle mobile TOC
const toggleMobileTOC = () => {
  showMobileTOC.value = !showMobileTOC.value
}

// Extract headings from content
const extractHeadings = () => {
  if (!process.client) return
  
  // Wait for content to be rendered
  nextTick(() => {
    const headings = document.querySelectorAll('.prose h2, .prose h3, .prose h4, .prose h5, .prose h6')
    const links = []
    
    headings.forEach((heading, index) => {
      // Generate ID if not exists
      if (!heading.id) {
        const id = heading.textContent
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '')
        heading.id = id
      }
      
      links.push({
        id: heading.id,
        text: heading.textContent,
        depth: parseInt(heading.tagName.charAt(1))
      })
    })
    
    tocLinks.value = links
  })
}

// Smooth scroll to heading
const scrollToHeading = (event) => {
  event.preventDefault()
  const targetId = event.target.getAttribute('href').substring(1)
  const targetElement = document.getElementById(targetId)

  if (targetElement) {
    const offsetTop = targetElement.offsetTop - 100 // Account for sticky header
    window.scrollTo({
      top: offsetTop,
      behavior: 'smooth'
    })

    // Close mobile TOC after clicking
    if (window.innerWidth < 1024) {
      showMobileTOC.value = false
    }
  }
}

// Track active heading and reading progress
const updateActiveHeading = () => {
  if (!process.client || tocLinks.value.length === 0) return
  
  const headings = tocLinks.value.map(link => document.getElementById(link.id)).filter(Boolean)
  const scrollTop = window.scrollY
  const windowHeight = window.innerHeight
  const documentHeight = document.documentElement.scrollHeight
  
  // Update reading progress
  readingProgress.value = Math.min(100, (scrollTop / (documentHeight - windowHeight)) * 100)
  
  // Find active heading
  let currentActiveId = ''
  
  for (let i = headings.length - 1; i >= 0; i--) {
    const heading = headings[i]
    if (heading && scrollTop >= heading.offsetTop - 150) {
      currentActiveId = heading.id
      break
    }
  }
  
  activeId.value = currentActiveId
}

// Setup scroll listener and sticky behavior
onMounted(() => {
  extractHeadings()

  // Add scroll listener with throttling
  let ticking = false
  const handleScroll = () => {
    if (!ticking) {
      requestAnimationFrame(() => {
        updateActiveHeading()
        ensureStickyBehavior()
        ticking = false
      })
      ticking = true
    }
  }

  window.addEventListener('scroll', handleScroll, { passive: true })
  window.addEventListener('resize', handleScroll, { passive: true })

  // Initial call
  updateActiveHeading()

  // Ensure sticky positioning works properly
  nextTick(() => {
    setupStickyTOC()
  })

  // Cleanup
  onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll)
    window.removeEventListener('resize', handleScroll)
  })
})

// Setup sticky TOC behavior
const setupStickyTOC = () => {
  if (window.innerWidth >= 1024) {
    const tocContainer = document.querySelector('.toc-container')
    if (tocContainer) {
      // Force sticky positioning with important styles
      tocContainer.style.setProperty('position', 'sticky', 'important')
      tocContainer.style.setProperty('top', '2rem', 'important')
      tocContainer.style.setProperty('align-self', 'flex-start', 'important')
      tocContainer.style.setProperty('z-index', '10', 'important')

      // Ensure parent container supports sticky
      const parent = tocContainer.parentElement
      if (parent) {
        parent.style.position = 'relative'
        parent.style.height = 'fit-content'
      }

      // Force browser to recalculate
      tocContainer.offsetHeight
    }
  }
}

// Ensure sticky behavior is maintained during scroll
const ensureStickyBehavior = () => {
  if (window.innerWidth >= 1024) {
    const tocContainer = document.querySelector('.toc-container')
    if (tocContainer) {
      // Check if sticky is working by comparing position
      const rect = tocContainer.getBoundingClientRect()
      const expectedTop = 32 // 2rem = 32px

      // If TOC is not sticking properly, force it
      if (window.scrollY > 100 && rect.top !== expectedTop) {
        tocContainer.style.setProperty('position', 'sticky', 'important')
        tocContainer.style.setProperty('top', '2rem', 'important')
      }
    }
  }
}

// Watch for content changes
watch(() => props.content, () => {
  nextTick(() => {
    extractHeadings()
  })
}, { deep: true })
</script>

<style scoped>
/* Sticky positioning for desktop */
@media (min-width: 1024px) {
  .toc-container {
    position: -webkit-sticky; /* Safari */
    position: sticky;
    top: 2rem;
    max-height: calc(100vh - 4rem);
    align-self: flex-start;
    /* Ensure proper sticky behavior */
    z-index: 10;
    background: white; /* Ensure background covers content behind */
  }
}

/* TOC Navigation styling */
.toc-nav {
  max-height: none; /* Remove height restriction on mobile */
}

/* Desktop TOC navigation with scrollbar only when needed */
@media (min-width: 1024px) {
  .toc-nav {
    max-height: calc(100vh - 16rem); /* Leave space for header, padding, and progress */
    overflow-y: auto;
    overflow-x: hidden; /* Prevent horizontal scroll */
    scrollbar-gutter: stable; /* Reserve space for scrollbar */
  }

  /* Custom scrollbar only for desktop - thin and subtle */
  .toc-nav::-webkit-scrollbar {
    width: 3px;
  }

  .toc-nav::-webkit-scrollbar-track {
    background: transparent;
  }

  .toc-nav::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .toc-nav::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Hide scrollbar when not needed */
  .toc-nav {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 transparent;
  }
}

/* Ensure proper stacking context and layout */
.toc-container {
  z-index: 10;
  will-change: transform; /* Optimize for sticky positioning */
  overflow: visible; /* Prevent container from creating scroll context */
  width: 100%;
}

/* Desktop TOC sizing */
@media (min-width: 1024px) {
  .toc-container {
    width: 100%; /* Full width of parent container */
    max-width: none; /* Remove max-width restriction */
  }
}

/* Mobile TOC should be full width */
@media (max-width: 1023px) {
  .toc-container {
    max-width: none;
    min-width: auto;
  }
}

/* Force sticky positioning on desktop */
@media (min-width: 1024px) {
  .toc-container {
    position: -webkit-sticky !important;
    position: sticky !important;
    top: 2rem !important;
  }
}
</style>
