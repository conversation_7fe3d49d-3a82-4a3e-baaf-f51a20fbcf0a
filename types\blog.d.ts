// Global type definitions for blog components

export interface BreadcrumbItem {
  text: string
  href?: string
}

export interface BlogPost {
  _path: string
  title: string
  description?: string
  date: string
  tags?: string[]
  image?: string
  authors?: string[]
  badge?: {
    label: string
    color?: string
  }
}

export interface TagItem {
  tag: string
  count: number
}

// Extend Window interface for Disqus
declare global {
  interface Window {
    DISQUS?: {
      reset: (config: {
        reload: boolean
        config: () => void
      }) => void
    }
    disqus_config?: () => void
    page?: {
      identifier: string
      url: string
      title: string
    }
  }
}

export {} 