<template>
  <div class="relative">
    <!-- Main Grid Layout -->
    <div class="grid grid-cols-1 md:grid-cols-12 gap-2 rounded-xl overflow-hidden h-[500px]">
      <!-- Main Large Image (Left Side) - 7 columns wide -->
      <div class="md:col-span-7 h-full">
        <div class="w-full h-full overflow-hidden">
          <img
            :src="safeImages[0]"
            :alt="title"
            class="w-full h-full object-cover hover:opacity-95 transition-opacity cursor-pointer"
            @click="openGallery(0)"
          />
          <!-- Overlay untuk efek hover -->
          <div class="absolute inset-0 bg-black opacity-0 hover:opacity-10 transition-opacity"></div>
        </div>
      </div>

      <!-- Right Side Grid (2x2) - 5 columns wide -->
      <div class="hidden md:block md:col-span-5 h-full">
        <div class="grid grid-cols-2 grid-rows-2 gap-2 h-full">
          <!-- Top Row -->
          <div class="relative h-full">
            <!-- Jika gambar kedua tersedia, tampilkan gambar -->
            <template v-if="safeImages[1]">
              <img
                :src="safeImages[1]"
                :alt="`${title} view 2`"
                class="w-full h-full object-cover hover:opacity-95 transition-opacity cursor-pointer"
                @click="openGallery(1)"
              />
              <!-- Overlay untuk efek hover -->
              <div class="absolute inset-0 bg-black opacity-0 hover:opacity-10 transition-opacity cursor-pointer" @click="openGallery(1)"></div>
            </template>

            <!-- Jika gambar kedua tidak tersedia, tampilkan placeholder -->
            <template v-else>
              <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-gray-400">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                </svg>
              </div>
            </template>
          </div>

          <div class="relative h-full">
            <!-- Jika gambar ketiga tersedia, tampilkan gambar -->
            <template v-if="safeImages[2]">
              <img
                :src="safeImages[2]"
                :alt="`${title} view 3`"
                class="w-full h-full object-cover hover:opacity-95 transition-opacity cursor-pointer"
                @click="openGallery(2)"
              />
              <!-- Overlay untuk efek hover -->
              <div class="absolute inset-0 bg-black opacity-0 hover:opacity-10 transition-opacity cursor-pointer" @click="openGallery(2)"></div>
            </template>

            <!-- Jika gambar ketiga tidak tersedia, tampilkan placeholder -->
            <template v-else>
              <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-gray-400">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                </svg>
              </div>
            </template>
          </div>

          <!-- Bottom Row -->
          <div class="relative h-full">
            <!-- Jika gambar keempat tersedia, tampilkan gambar -->
            <template v-if="safeImages[3]">
              <img
                :src="safeImages[3]"
                :alt="`${title} view 4`"
                class="w-full h-full object-cover hover:opacity-95 transition-opacity cursor-pointer"
                @click="openGallery(3)"
              />
              <!-- Overlay untuk efek hover -->
              <div class="absolute inset-0 bg-black opacity-0 hover:opacity-10 transition-opacity cursor-pointer" @click="openGallery(3)"></div>
            </template>

            <!-- Jika gambar keempat tidak tersedia, tampilkan placeholder -->
            <template v-else>
              <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-gray-400">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                </svg>
              </div>
            </template>
          </div>

          <div class="relative h-full">
            <!-- Jika gambar kelima tersedia, tampilkan gambar -->
            <template v-if="safeImages[4]">
              <img
                :src="safeImages[4]"
                :alt="`${title} view 5`"
                class="w-full h-full object-cover hover:opacity-95 transition-opacity cursor-pointer"
                @click="openGallery(4)"
              />
              <!-- Overlay untuk efek hover -->
              <div class="absolute inset-0 bg-black opacity-0 hover:opacity-10 transition-opacity cursor-pointer" @click="openGallery(4)"></div>
            </template>

            <!-- Jika gambar kelima tidak tersedia, tampilkan placeholder -->
            <template v-else>
              <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-gray-400">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                </svg>
              </div>
            </template>

            <!-- Tombol "Show all photos" -->
            <div
              v-if="safeImages.length > 5"
              class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center"
            >
              <button
                @click="openGallery(0)"
                class="bg-white px-4 py-2 rounded-lg text-sm font-medium shadow-md hover:shadow-lg transition-all hover:scale-105"
              >
                <div class="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                  </svg>
                  Show all photos
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Swiper (Only visible on mobile) -->
    <div class="md:hidden relative mt-2">
      <div class="flex overflow-x-auto gap-2 pb-2 snap-x">
        <div
          v-for="(image, index) in safeImages.slice(1)"
          :key="index"
          class="flex-shrink-0 w-64 h-48 snap-center"
        >
          <div class="w-full h-full overflow-hidden rounded-lg">
            <img
              :src="image"
              :alt="`${title} mobile view ${index + 2}`"
              class="w-full h-full object-cover cursor-pointer"
              @click="openGallery(index + 1)"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Show All Photos Button (Mobile) -->
    <button
      @click="openGallery(0)"
      class="md:hidden absolute right-4 top-4 bg-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-md z-10"
    >
      <div class="flex items-center gap-1">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3">
          <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
        </svg>
        Show all
      </div>
    </button>

    <!-- Full Screen Gallery Modal -->
    <div v-if="showFullGallery" class="fixed inset-0 bg-black z-50 overflow-y-auto">
      <div class="relative min-h-screen">
        <!-- Close Button -->
        <button
          @click="showFullGallery = false"
          class="absolute top-4 left-4 bg-black/50 text-white rounded-full p-2 z-10 hover:bg-black/70"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <!-- Image Counter -->
        <div class="absolute top-4 right-4 bg-black/50 text-white px-3 py-1.5 rounded-lg z-10">
          {{ currentImage + 1 }} / {{ safeImages.length }}
        </div>

        <!-- Image Container -->
        <div class="flex items-center justify-center min-h-screen">
          <img
            :src="safeImages[currentImage]"
            :alt="`${title} full view ${currentImage + 1}`"
            class="max-w-full max-h-[90vh] object-contain"
          />
        </div>

        <!-- Navigation Buttons -->
        <button
          v-if="currentImage > 0"
          @click="prevImage"
          class="absolute left-4 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md hover:shadow-lg"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <button
          v-if="currentImage < safeImages.length - 1"
          @click="nextImage"
          class="absolute right-4 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md hover:shadow-lg"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>

        <!-- Thumbnails at bottom -->
        <div class="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 py-2 px-4 bg-black/50 rounded-lg overflow-x-auto">
          <div
            v-for="(image, index) in safeImages"
            :key="index"
            @click="currentImage = index"
            class="h-16 w-24 flex-shrink-0 cursor-pointer"
            :class="currentImage === index ? 'ring-2 ring-white' : ''"
          >
            <img :src="image" :alt="`Thumbnail ${index + 1}`" class="h-full w-full object-cover"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  images: {
    type: Array,
    required: true,
    default: () => []
  },
  title: {
    type: String,
    default: 'Image Gallery'
  }
})

// Ensure we have at least one image and exactly 5 images
const safeImages = computed(() => {
  console.log('ImageGallery - props.images:', props.images)

  if (!props.images || props.images.length === 0) {
    // If no images provided, return array with 5 placeholders
    return Array(5).fill('https://images.unsplash.com/photo-1507525428034-b723cf961d3e?q=80&w=2946&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D')
  }

  // Make a copy of the images array
  let images = [...props.images]

  // Ensure we have exactly 5 images
  if (images.length < 5) {
    // If less than 5 images, duplicate existing images
    let index = 0
    while (images.length < 5) {
      images.push(images[index % images.length])
      index++
    }
  } else if (images.length > 5) {
    // If more than 5 images, truncate to 5
    images = images.slice(0, 5)
  }

  console.log('ImageGallery - safeImages:', images)
  return images
})

// Gallery state
const showFullGallery = ref(false)
const currentImage = ref(0)

// Gallery methods
function openGallery(index) {
  currentImage.value = index
  showFullGallery.value = true
}

function nextImage() {
  if (currentImage.value < safeImages.value.length - 1) {
    currentImage.value++
  }
}

function prevImage() {
  if (currentImage.value > 0) {
    currentImage.value--
  }
}
</script>

<style scoped>
/* Hide scrollbar for Chrome, Safari and Opera */
.overflow-x-auto::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.overflow-x-auto {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
</style>
