<script setup lang="ts">
defineProps({
  post: {
    type: Object,
    required: true
  }
});

// Format tanggal menggunakan Intl.DateTimeFormat
const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('id-ID', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
};
</script>

<template>
  <header class="mb-8">
    <h1 class="text-3xl md:text-4xl font-bold mb-4">{{ post.title }}</h1>
    
    <div class="flex flex-wrap items-center mb-6">
      <!-- Badge kategori -->
      <div v-if="post.badge" class="mr-4">
        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
          {{ post.badge.label }}
        </span>
      </div>
      
      <!-- Tanggal publikasi -->
      <time 
        :datetime="post.date" 
        class="text-gray-600 text-sm"
        itemprop="datePublished"
      >
        {{ formatDate(post.date) }}
      </time>
      
      <!-- Separator -->
      <span class="mx-2 text-gray-300">•</span>
      
      <!-- Informasi penulis -->
      <div 
        v-if="post.authors && post.authors.length" 
        class="flex items-center"
        itemprop="author" 
        itemscope 
        itemtype="https://schema.org/Person"
      >
        <img 
          v-if="post.authors[0].avatar?.src" 
          :src="post.authors[0].avatar.src" 
          :alt="post.authors[0].name" 
          class="w-6 h-6 rounded-full mr-2"
          itemprop="image"
        />
        <span class="text-gray-600 text-sm" itemprop="name">{{ post.authors[0].name }}</span>
      </div>
    </div>
    
    <!-- Tags -->
    <div v-if="post.tags && post.tags.length" class="flex flex-wrap gap-2 mb-6">
      <a 
        v-for="tag in post.tags" 
        :key="tag"
        :href="`/blog/tags/${tag.toLowerCase()}`"
        class="bg-gray-100 text-gray-800 text-sm px-3 py-1 rounded-full hover:bg-blue-100 hover:text-blue-800 transition-colors"
      >
        #{{ tag }}
      </a>
    </div>
    
    <!-- Featured Image -->
    <figure v-if="post.image?.src" class="mb-8">
      <img 
        :src="post.image.src" 
        :alt="post.title" 
        class="w-full h-auto max-h-[500px] object-cover rounded-lg shadow-md"
        itemprop="image"
      />
    </figure>
    
    <!-- Description/Summary -->
    <p 
      class="text-xl text-gray-600 mb-8 max-w-3xl"
      itemprop="description"
    >
      {{ post.description }}
    </p>
  </header>
</template>
