<template>
  <main
    v-if="post"
    class="min-h-screen bg-zinc-100"
    itemscope
    itemtype="https://schema.org/BlogPosting"
  >
    <!-- Hero Section with Background Image -->
    <div 
      class="relative min-h-[50vh] md:min-h-[60vh] flex items-center justify-center overflow-hidden"
      :class="!post.image ? 'bg-gradient-to-br from-zinc-800 to-zinc-900' : ''"
      role="banner"
      :aria-label="`Hero image for article: ${post.title}`"
    >
      <!-- Background Image with Alt Text Support -->
      <img
        v-if="post.image"
        :src="typeof post.image === 'string' ? post.image : post.image.src"
        :alt="`Featured image for ${post.title} - 3J Tour ${post.title}`"
        class="absolute inset-0 w-full h-full object-cover object-center"
        loading="eager"
        itemprop="image"
      />
      
      <!-- Modern Color Overlay -->
      <div class="absolute inset-0 bg-gradient-to-br from-red-900/70 via-black/60 to-zinc-900/80"></div>
      
      <!-- Hero Content -->
      <div class="relative z-10 w-full">
        <div class="container mx-auto px-4 py-8 max-w-5xl text-center">
          <!-- Article Header -->
          <header>
            <!-- Tags -->
            <div v-if="post.tags && post.tags.length" class="flex flex-wrap justify-center gap-2 mb-6">
              <a
                v-for="tag in post.tags"
                :key="tag"
                :href="`/tags/${tag.toLowerCase()}/`"
                class="bg-red-500/90 backdrop-blur-sm text-white text-sm px-4 py-2 rounded-full hover:bg-red-600/90 transition-all duration-300 font-medium border border-red-400/30"
              >
                #{{ tag }}
              </a>
            </div>

            <!-- Main Title -->
            <h1 class="text-3xl md:text-5xl lg:text-6xl font-bold mb-6 text-white leading-tight drop-shadow-2xl" itemprop="headline">
              {{ post.title }}
            </h1>

            <!-- Article Meta -->
            <div class="flex flex-wrap justify-center items-center gap-4">
              <time 
                class="text-white/80 text-sm font-medium bg-white/10 backdrop-blur-md px-4 py-2 rounded-full border border-white/20" 
                itemprop="datePublished" 
                :datetime="typeof post.date === 'string' ? post.date : post.date.toISOString()"
              >
                📅 {{ new Date(post.date).toLocaleDateString('id-ID', { year: 'numeric', month: 'long', day: 'numeric' }) }}
              </time>
              
              <div v-if="post.authors && post.authors.length" class="flex items-center bg-white/10 backdrop-blur-md px-4 py-2 rounded-full border border-white/20" itemprop="author" itemscope itemtype="https://schema.org/Person">
                <img
                  v-if="post.authors[0].avatar?.src"
                  :src="post.authors[0].avatar.src"
                  :alt="post.authors[0].name"
                  class="w-6 h-6 rounded-full mr-3 border-2 border-white/30"
                  loading="lazy"
                />
                <span class="text-white/80 text-sm font-medium" itemprop="name">
                  👤 {{ post.authors[0].name }}
                </span>
              </div>

              <!-- Reading Time Estimate -->
              <div class="flex items-center bg-white/10 backdrop-blur-md px-4 py-2 rounded-full border border-white/20">
                <span class="text-white/80 text-sm font-medium">
                  ⏱️ 5 min read
                </span>
              </div>
            </div>
          </header>
        </div>
      </div>
    </div>

    <!-- Breadcrumb Section -->
    <div class="bg-white border-b border-zinc-200">
      <div class="container mx-auto px-4 py-4 max-w-5xl">
        <nav>
          <ol class="flex items-center space-x-2 text-sm">
            <li>
              <a href="/" class="text-zinc-600 hover:text-red-600 hover:underline transition-colors">
                Beranda
              </a>
            </li>
            <li class="text-zinc-400">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
            </li>
            <li>
              <a href="/tags/" class="text-zinc-600 hover:text-red-600 hover:underline transition-colors">
                Blog
              </a>
            </li>
            <li class="text-zinc-400">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
            </li>
            <li>
              <span class="text-zinc-800 font-medium">{{ post.title }}</span>
            </li>
          </ol>
        </nav>
      </div>
    </div>

    <!-- Article Content with Enhanced Layout -->
    <div class="bg-white py-16">
      <div class="container mx-auto px-4 max-w-5xl">
        <div class="flex flex-col lg:flex-row gap-12">
          <!-- Main Content -->
          <article class="prose prose-lg max-w-none lg:w-2/3 min-w-0" itemprop="articleBody">
            <ContentRenderer :value="post" />
          </article>

          <!-- Table of Contents Sidebar -->
          <aside class="lg:w-1/3 order-first lg:order-last">
            <div class="sticky top-8">
              <BlogToc :content="post" :active-section="activeSection" />
            </div>
          </aside>
        </div>
      </div>
    </div>

    <!-- Related Articles Section -->
    <section v-if="relatedPosts && relatedPosts.length" class="py-16 bg-zinc-50 border-t border-zinc-200">
      <div class="container mx-auto px-4 max-w-5xl">
        <h2 class="text-3xl font-bold mb-8 text-zinc-900">Artikel Terkait</h2>
        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <article
            v-for="relatedItem in relatedPosts"
            :key="relatedItem._path"
            class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group"
          >
            <!-- Related Article Image -->
            <div 
              v-if="relatedItem.image" 
              class="relative h-48 overflow-hidden group-hover:scale-105 transition-transform duration-300"
            >
              <img 
                :src="typeof relatedItem.image === 'string' ? relatedItem.image : relatedItem.image.src"
                :alt="`Image for ${relatedItem.title}`"
                class="w-full h-full object-cover object-center"
                loading="lazy"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent flex items-end p-4">
                <h3 class="text-white font-semibold text-sm line-clamp-2">
                  {{ relatedItem.title }}
                </h3>
              </div>
            </div>
            
            <div class="p-6">
              <h3 v-if="!relatedItem.image" class="text-lg font-semibold text-zinc-900 mb-3 line-clamp-2">
                <NuxtLink
                  v-if="relatedItem._path"
                  :to="relatedItem._path"
                  class="hover:text-red-600 transition-colors"
                >
                  {{ relatedItem.title }}
                </NuxtLink>
                <span v-else class="text-zinc-500">
                  {{ relatedItem.title }}
                </span>
              </h3>
              <p v-if="relatedItem.description" class="text-zinc-600 text-sm line-clamp-2 mb-4">
                {{ relatedItem.description }}
              </p>
              <div class="flex items-center justify-between">
                <time v-if="relatedItem.date" class="text-xs text-zinc-500">
                  {{ new Date(relatedItem.date).toLocaleDateString('id-ID', { year: 'numeric', month: 'short', day: 'numeric' }) }}
                </time>
                <NuxtLink
                  v-if="relatedItem._path"
                  :to="relatedItem._path"
                  class="text-red-600 hover:text-red-700 text-sm font-medium"
                >
                  Baca Selengkapnya →
                </NuxtLink>
              </div>
            </div>
          </article>
        </div>
      </div>
    </section>

  </main>

  <!-- Loading State -->
  <div v-else class="min-h-screen flex items-center justify-center bg-white">
    <div class="text-center">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto mb-4"></div>
      <p class="text-zinc-600 text-lg">Memuat artikel...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useAsyncData, queryCollection, ref } from '#imports'
import type { BlogPost } from '~/types/content'
import BlogImage from '~/components/blog/BlogImage.vue'

const route = useRoute()

// Fetch current post
const { data: post } = await useAsyncData(route.path, () => queryCollection('posts').path(route.path).first())

if (!post.value) {
  throw createError({ statusCode: 404, statusMessage: 'Post not found', fatal: true })
}

// --- BEGIN RELATED ARTICLES LOGIC ---

// Fetch related posts
const { data: relatedPosts } = await useAsyncData<BlogPost[]>(
  `related-${route.path}`, // Unique key for this asyncData
  async () => {
    if (!post.value || !post.value.tags || post.value.tags.length === 0) {
      return [];
    }

    const currentPostPath = post.value.path;
    if (!currentPostPath) {
        return [];
    }
    // Ensure trailing slash for consistent comparison
    const normalizedCurrentPath = currentPostPath.endsWith('/') ? currentPostPath : `${currentPostPath}/`;
    const currentPostTags = post.value.tags as string[];

    try {
      const allPostsData = await queryCollection('posts').all();

      if (!Array.isArray(allPostsData)) {
        return [];
      }
      
      const allPosts = allPostsData as BlogPost[];

      const postsWithPaths = allPosts.map(p => {
        let calculatedPath = p._path;
        // @ts-ignore
        const postId = p.id as string | undefined;

        if (!calculatedPath && postId && typeof postId === 'string') {
          if (postId.startsWith('posts/blog/') && postId.endsWith('.md')) {
            const slug = postId.substring('posts/blog/'.length, postId.length - '.md'.length);
            calculatedPath = `/${slug}/`;
          }
        }
        return { ...p, _path: calculatedPath };
      });
      
      const related = postsWithPaths
        .filter(p => {
          if (!p._path) {
            return false;
          }
          // Normalize path for comparison
          const normalizedPath = p._path.endsWith('/') ? p._path : `${p._path}/`;
          if (normalizedPath === normalizedCurrentPath) return false;
          if (!p.tags || p.tags.length === 0) return false;
          return p.tags.some(tag => currentPostTags.includes(tag));
        })
        .sort((a, b) => {
          const dateA = new Date(a.date).getTime();
          const dateB = new Date(b.date).getTime();
          return dateB - dateA;
        })
        .slice(0, 3);

      return related;

    } catch (error) {
      // console.error("[RelatedPosts] Error fetching or processing related posts:", error); // Optional: Keep for production if needed
      return [];
    }
  },
  {
    default: () => []
  }
);

// --- END RELATED ARTICLES LOGIC ---

// Active section for TOC
const activeSection = ref('')

// Breadcrumb items
const breadcrumbItems = [
  { text: 'Beranda', href: '/' },
  { text: 'Tags', href: '/tags/' },
  { text: post.value?.title || 'Artikel' }
]

// Dapatkan base URL untuk canonical
const baseUrl = 'https://www.jogjaliburan.com'
const canonicalUrl = `${baseUrl}${route.path}${route.path.endsWith('/') ? '' : '/'}`

// Enhanced Structured data for SEO dengan breadcrumb dan lebih lengkap
const organizationSchema = {
  '@type': 'Organization',
  '@id': `${baseUrl}/#organization`,
  'name': '3J Tour - Jogja Liburan',
  'description': 'Travel agency terpercaya untuk wisata Yogyakarta dan sekitarnya',
  'url': baseUrl,
  'logo': {
    '@type': 'ImageObject',
    'url': `${baseUrl}/3j-tour-logo.webp`,
    'width': 300,
    'height': 100
  },
  'contactPoint': {
    '@type': 'ContactPoint',
    'telephone': '+62-274-123456',
    'contactType': 'Customer Service',
    'areaServed': 'ID',
    'availableLanguage': ['Indonesian', 'English']
  },
  'address': {
    '@type': 'PostalAddress',
    'addressLocality': 'Yogyakarta',
    'addressRegion': 'Daerah Istimewa Yogyakarta',
    'addressCountry': 'ID'
  },
  'sameAs': [
    'https://www.facebook.com/jogjaliburan',
    'https://www.instagram.com/jogjaliburan',
    'https://wa.me/6285186888837'
  ]
}

const breadcrumbSchema = {
  '@type': 'BreadcrumbList',
  'itemListElement': [
    {
      '@type': 'ListItem',
      'position': 1,
      'name': 'Beranda',
      'item': baseUrl
    },
    {
      '@type': 'ListItem',
      'position': 2,
      'name': 'Tags',
      'item': `${baseUrl}/tags/`
    },
    {
      '@type': 'ListItem',
      'position': 3,
      'name': post.value.title,
      'item': canonicalUrl
    }
  ]
}

const articleSchema = {
  '@type': 'Article',
  '@id': canonicalUrl,
  'headline': post.value.title,
  'description': post.value.description,
  'image': post.value.image ? {
    '@type': 'ImageObject',
    'url': typeof post.value.image === 'string' ? post.value.image : post.value.image.src,
    'width': 1200,
    'height': 630
  } : undefined,
  'datePublished': post.value.date,
  'dateModified': post.value.date,
  'author': {
    '@type': 'Person',
    'name': post.value.authors?.[0]?.name || '3J Tour Team',
    'description': 'Travel expert dan pemandu wisata Yogyakarta'
  },
  'publisher': organizationSchema,
  'mainEntityOfPage': {
    '@type': 'WebPage',
    '@id': canonicalUrl
  },
  'articleSection': 'Travel Guide',
  'keywords': post.value.tags?.join(', ') || 'wisata jogja, travel yogyakarta, destinasi wisata',
  'wordCount': 1500, // Estimasi, bisa dihitung dari content
  'inLanguage': 'id-ID',
  'about': {
    '@type': 'Thing',
    'name': 'Wisata Yogyakarta',
    'description': 'Panduan lengkap wisata dan liburan di Yogyakarta'
  },
  'mentions': post.value.tags?.map(tag => ({
    '@type': 'Thing',
    'name': tag,
    'url': `${baseUrl}/tags/${tag.toLowerCase()}/`
  })) || []
}

const webPageSchema = {
  '@type': 'WebPage',
  '@id': canonicalUrl,
  'name': post.value.title,
  'description': post.value.description,
  'url': canonicalUrl,
  'inLanguage': 'id-ID',
  'isPartOf': {
    '@type': 'WebSite',
    '@id': `${baseUrl}/#website`,
    'name': '3J Tour - Jogja Liburan',
    'description': 'Portal wisata terlengkap untuk liburan di Yogyakarta',
    'url': baseUrl,
    'publisher': organizationSchema
  },
  'breadcrumb': breadcrumbSchema,
  'mainEntity': articleSchema
}

// Gabungkan semua schema dalam graph
const jsonLd = {
  '@context': 'https://schema.org',
  '@graph': [
    organizationSchema,
    webPageSchema,
    articleSchema,
    breadcrumbSchema
  ]
}

// Enhanced SEO metadata - Menggunakan frontmatter description
const title = `${post.value.title} | 3J Tour - Jogja Liburan`

// Prioritas: frontmatter description > fallback
const metaDescription = post.value.description || 
  `Panduan lengkap ${post.value.title} - Temukan informasi terlengkap tentang wisata dan liburan di Yogyakarta bersama 3J Tour.`

// Pastikan meta description optimal untuk SEO (150-160 karakter)
const description = metaDescription.length > 160 
  ? metaDescription.substring(0, 157) + '...' 
  : metaDescription

const imageUrl = post.value.image ? (typeof post.value.image === 'string' ? post.value.image : post.value.image.src) : `${baseUrl}/images/og-default.jpg`
const articleTags = post.value.tags?.join(', ') || 'wisata jogja, travel yogyakarta'

useHead({
  title: title,
  meta: [
    // Basic meta tags
    { name: 'description', content: description },
    { name: 'keywords', content: `${articleTags}, panduan wisata jogja, destinasi yogyakarta, liburan jogja` },
    { name: 'author', content: post.value.authors?.[0]?.name || '3J Tour Team' },
    { name: 'robots', content: 'index, follow, max-image-preview:large, max-snippet:-1' },
    
    // Open Graph tags untuk social media
    { property: 'og:title', content: title },
    { property: 'og:description', content: description },
    { property: 'og:type', content: 'article' },
    { property: 'og:url', content: canonicalUrl },
    { property: 'og:image', content: imageUrl },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '630' },
    { property: 'og:image:alt', content: post.value.title },
    { property: 'og:site_name', content: '3J Tour - Jogja Liburan' },
    { property: 'og:locale', content: 'id_ID' },
    
    // Article specific Open Graph
    { property: 'article:published_time', content: new Date(post.value.date).toISOString() },
    { property: 'article:modified_time', content: new Date(post.value.date).toISOString() },
    { property: 'article:author', content: post.value.authors?.[0]?.name || '3J Tour Team' },
    { property: 'article:section', content: 'Travel Guide' },
    ...(post.value.tags?.map(tag => ({ property: 'article:tag', content: tag })) || []),
    
    // Twitter Card tags
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: title },
    { name: 'twitter:description', content: description },
    { name: 'twitter:image', content: imageUrl },
    { name: 'twitter:image:alt', content: post.value.title },
    { name: 'twitter:site', content: '@jogja_jalan_jalan' },
    { name: 'twitter:creator', content: '@jogja_jalan_jalan' },
    
    // Additional SEO tags
    { name: 'geo.region', content: 'ID-YO' },
    { name: 'geo.placename', content: 'Bantul, Yogyakarta' },
    { name: 'geo.position', content: '-7.822014292198785;110.**************' },
    { name: 'ICBM', content: '-7.822014292198785, 110.**************' },
    
    // Mobile optimization
    { name: 'viewport', content: 'width=device-width, initial-scale=1, viewport-fit=cover' },
    { name: 'theme-color', content: '#f97316' },
    { name: 'msapplication-TileColor', content: '#f97316' },
    
    // Article reading time estimation
    { name: 'article:reading_time', content: '5' } // 5 menit rata-rata
  ],
  link: [
    { rel: 'canonical', href: canonicalUrl },
    { rel: 'amphtml', href: `${canonicalUrl}amp/` }, // Jika ada AMP version
    
    // Preconnect untuk performance
    { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
    { rel: 'preconnect', href: 'https://www.google-analytics.com' },
    
    // Alternative languages jika ada
    { rel: 'alternate', hreflang: 'id', href: canonicalUrl },
    { rel: 'alternate', hreflang: 'x-default', href: canonicalUrl }
  ],
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify(jsonLd)
    }
  ]
})
</script>

<style scoped>
/* Enhanced Typography and Layout */
:deep(.prose) {
  max-width: 100%;
  font-family: 'Poppins', sans-serif;
}

/* Improved Image Styling */
:deep(.prose img) {
  border-radius: 0.75rem;
  margin: 2.5rem auto;
  display: block;
  max-width: 100%;
  height: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

:deep(.prose img:hover) {
  transform: scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Figure Spacing Improvements */
:deep(.prose figure) {
  margin: 3rem 0;
  clear: both;
}

:deep(.prose figure img) {
  margin: 0 auto;
  border-radius: 0.75rem;
}

:deep(.prose figcaption) {
  text-align: center;
  font-style: italic;
  color: #6b7280;
  font-size: 0.9rem;
  margin-top: 0.75rem;
}

/* Enhanced Heading Styling */
:deep(.prose h2) {
  color: #111827;
  font-weight: 700;
  border-bottom: 2px solid #fef2f2;
  padding-bottom: 0.5rem;
  margin-top: 3rem;
  margin-bottom: 1.5rem;
  scroll-margin-top: 120px;
}

:deep(.prose h3) {
  color: #374151;
  font-weight: 600;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  scroll-margin-top: 120px;
}

:deep(.prose h4) {
  color: #4b5563;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 0.75rem;
  scroll-margin-top: 120px;
}

/* Paragraph and Text Enhancements */
:deep(.prose p) {
  line-height: 1.8;
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  color: #374151;
}

:deep(.prose p:first-of-type) {
  font-size: 1.125rem;
  line-height: 1.7;
  color: #1f2937;
}

/* List Styling */
:deep(.prose ul),
:deep(.prose ol) {
  margin: 1.5rem 0;
}

:deep(.prose li) {
  margin: 0.5rem 0;
  line-height: 1.7;
}

/* Enhanced Link Styling */
:deep(.prose a) {
  color: #dc2626;
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

:deep(.prose a:hover) {
  color: #b91c1c;
  border-bottom-color: #dc2626;
}

/* Strong Text Styling */
:deep(.prose strong) {
  color: #dc2626;
  font-weight: 600;
}

/* Blockquote Styling */
:deep(.prose blockquote) {
  border-left: 4px solid #fecaca;
  background: #fef2f2;
  padding: 1.5rem;
  margin: 2rem 0;
  border-radius: 0.5rem;
  font-style: italic;
}

:deep(.prose blockquote p) {
  margin: 0;
  color: #7f1d1d;
}

/* Code Styling */
:deep(.prose code) {
  background: #f3f4f6;
  color: #1f2937;
  padding: 0.25rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.9em;
  font-weight: 500;
  border: 1px solid #e5e7eb;
}

:deep(.prose pre) {
  background: #1f2937;
  color: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 2rem 0;
}

:deep(.prose pre code) {
  background: transparent;
  color: inherit;
  padding: 0;
  border: none;
}

/* Table Styling */
:deep(.prose table) {
  width: 100%;
  margin: 2rem 0;
  border-collapse: collapse;
}

:deep(.prose th),
:deep(.prose td) {
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  text-align: left;
}

:deep(.prose th) {
  background: #f9fafb;
  font-weight: 600;
  color: #1f2937;
}

/* Line Clamp Utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Mobile Optimizations */
@media (max-width: 640px) {
  :deep(.prose) {
    font-size: 1rem;
  }
  
  :deep(.prose h2) {
    font-size: 1.5rem;
    margin-top: 2rem;
  }
  
  :deep(.prose h3) {
    font-size: 1.25rem;
    margin-top: 1.5rem;
  }
  
  :deep(.prose img) {
    margin: 1.5rem auto;
    border-radius: 0.5rem;
  }
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Enhanced Focus States */
:deep(a:focus),
:deep(button:focus) {
  outline: 2px solid #dc2626;
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  :deep(.prose) {
    font-size: 12pt;
    line-height: 1.5;
  }
  
  :deep(.prose a) {
    color: #000;
    text-decoration: underline;
  }
  
  :deep(.prose img) {
    max-width: 100%;
    page-break-inside: avoid;
  }
}
</style>