<script setup>
defineProps({
  title: {
    type: String,
    default: 'Blog'
  },
  description: {
    type: String,
    default: 'Latest articles and updates'
  }
})
</script>

<template>
  <div class="blog-header">
    <h1 class="blog-title">{{ title }}</h1>
    <p class="blog-description">{{ description }}</p>
  </div>
</template>

<style scoped>
.blog-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem 0;
}

.blog-title {
  font-size: 2.5rem;
  color: #1a202c;
  margin-bottom: 1rem;
}

.blog-description {
  font-size: 1.125rem;
  color: #4a5568;
  max-width: 600px;
  margin: 0 auto;
}
</style>
