<script setup lang="ts">
import { useRoute, useAsyncData, queryCollection } from '#imports'

// Definisi metadata halaman untuk memastikan routing yang benar
definePageMeta({
  validate: async (route) => {
    return !!route.params.tag
  }
})

const route = useRoute()
const tag = route.params.tag as string
const decodedTag = decodeURIComponent(tag)

// Mengambil semua artikel
const { data: allPosts } = await useAsyncData(`posts-${tag}`, () => queryCollection('posts').all())

// Filter artikel berdasarkan tag
const posts = computed(() => {
  if (!allPosts.value) return []
  
  return allPosts.value.filter(post => {
    return post.tags && Array.isArray(post.tags) && 
           post.tags.some(t => t.toLowerCase() === decodedTag.toLowerCase())
  })
})

// Helper function untuk mendapatkan URL post
function getPostUrl(post) {
  // Coba semua kemungkinan properti path
  if (post.path) return post.path.endsWith('/') ? post.path : `${post.path}/`
  if (post._path) return post._path.endsWith('/') ? post._path : `${post._path}/`
  
  // Fallback ke slug dari _path atau _file
  const slug = post._path?.split('/').pop() || 
               post._file?.split('/').pop()?.replace('.md', '') ||
               post._id?.split(':').pop()
  
  return `/${slug}/`
}

// Enhanced SEO metadata untuk Individual Tag Page
const baseUrl = 'https://www.jogjaliburan.com'
const currentUrl = `${baseUrl}/tags/${encodeURIComponent(decodedTag.toLowerCase())}/`
const title = `${decodedTag} - Panduan Wisata Yogyakarta | 3J Tour Jogja Liburan`
const description = `Temukan ${posts.value.length} panduan wisata dan tips ${decodedTag} terbaik di Yogyakarta. Informasi lengkap destinasi, kuliner, dan aktivitas seru untuk liburan Anda`
const imageUrl = `${baseUrl}/images/tags/${decodedTag.toLowerCase()}-og.jpg`

// Structured data untuk Individual Tag Page
const tagSchema = {
  '@context': 'https://schema.org',
  '@graph': [
    {
      '@type': 'Organization',
      '@id': `${baseUrl}/#organization`,
      'name': '3J Tour - Jogja Liburan',
      'url': baseUrl,
      'logo': {
        '@type': 'ImageObject',
        'url': `${baseUrl}/3j-tour-logo.webp`
      }
    },
    {
      '@type': 'WebPage',
      '@id': currentUrl,
      'name': title,
      'description': description,
      'url': currentUrl,
      'inLanguage': 'id-ID',
      'isPartOf': {
        '@type': 'WebSite',
        '@id': `${baseUrl}/#website`,
        'name': '3J Tour - Jogja Liburan',
        'url': baseUrl
      },
      'breadcrumb': {
        '@type': 'BreadcrumbList',
        'itemListElement': [
          {
            '@type': 'ListItem',
            'position': 1,
            'name': 'Beranda',
            'item': baseUrl
          },
          {
            '@type': 'ListItem',
            'position': 2,
            'name': 'Kategori Wisata',
            'item': `${baseUrl}/tags/`
          },
          {
            '@type': 'ListItem',
            'position': 3,
            'name': decodedTag,
            'item': currentUrl
          }
        ]
      }
    },
    {
      '@type': 'CollectionPage',
      '@id': `${currentUrl}#collection`,
      'name': `Wisata ${decodedTag} Yogyakarta`,
      'description': description,
      'url': currentUrl,
      'about': {
        '@type': 'Thing',
        'name': decodedTag,
        'description': `Kategori wisata ${decodedTag} di Yogyakarta`
      },
      'mainEntity': {
        '@type': 'ItemList',
        'name': `Artikel ${decodedTag}`,
        'description': `Daftar artikel panduan ${decodedTag} di Yogyakarta`,
        'numberOfItems': posts.value.length,
        'itemListElement': posts.value.map((post, index) => ({
          '@type': 'ListItem',
          'position': index + 1,
          'name': post.title,
          'url': getPostUrl(post),
          'description': post.description,
          'author': {
            '@type': 'Person',
            'name': post.authors?.[0]?.name || '3J Tour Team'
          },
          'datePublished': post.date
        }))
      }
    }
  ]
}

useSeoMeta({
  title: title,
  description: description,
  keywords: `${decodedTag} jogja, wisata ${decodedTag} yogyakarta, panduan ${decodedTag}, destinasi ${decodedTag}, travel guide ${decodedTag}`,
  author: '3J Tour Team',
  robots: 'index, follow',
  
  // Open Graph
  ogTitle: title,
  ogDescription: description,
  ogType: 'website',
  ogUrl: currentUrl,
  ogImage: imageUrl,
  ogSiteName: '3J Tour - Jogja Liburan',
  ogLocale: 'id_ID',
  
  // Twitter Card
  twitterCard: 'summary_large_image',
  twitterTitle: title,
  twitterDescription: description,
  twitterImage: imageUrl,
  twitterSite: '@jogja_jalan_jalan',
  
  // Additional meta
  themeColor: '#f97316'
})

useHead({
  link: [
    { rel: 'canonical', href: currentUrl },
    { rel: 'alternate', hreflang: 'id', href: currentUrl }
  ],
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify(tagSchema)
    }
  ]
})

// Format date
function formatDate(date) {
  if (!date) return ''
  return new Date(date).toLocaleDateString('id-ID', { year: 'numeric', month: 'long', day: 'numeric' })
}

// Debug route params
console.log('Tag route params:', route.params)
console.log('Decoded tag:', decodedTag)
console.log('Posts with this tag:', posts.value)
</script>

<template>
  <main class="py-20 bg-gray-50 dark:bg-gray-800 px-5">
    <div class="container mx-auto">
      <!-- Header -->
      <div class="text-center mb-12">
        <div class="inline-block bg-[#1e88e5]/10 text-blue-500 text-2xl font-bold px-6 py-3 rounded-full mb-4">
          #{{ decodedTag }}
        </div>
        <h1 class="text-4xl font-bold mb-4 text-gray-900">Artikel dengan tag "{{ decodedTag }}"</h1>
        <p class="text-gray-600 max-w-2xl mx-auto">{{ posts.length }} artikel ditemukan</p>
      </div>
      
      <!-- Blog Posts Grid -->
      <div v-if="posts.length" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        <!-- Posts -->
        <div 
          v-for="post in posts" 
          :key="post._path || post._id" 
          class="bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden flex flex-col h-full border-l-4 border-blue-500 relative"
        >
          <div class="absolute top-0 right-0 w-16 h-16 -mt-5 -mr-5 bg-[#1e88e5]/10 rounded-full"></div>
          <div class="absolute bottom-0 left-0 w-12 h-12 -mb-4 -ml-4 bg-[#1e88e5]/10 rounded-full"></div>
          <div class="bg-gradient-to-br from-blue-500/5 to-blue-500/20 p-6 flex items-center justify-center min-h-[200px]">
            <h2 class="text-2xl font-bold text-blue-500 dark:text-white text-center">
              {{ post.title }}
            </h2>
          </div>
          <div class="p-6 flex-grow">
            <div v-if="post.badge" class="mb-3">
              <span class="bg-[#1e88e5]/10 text-blue-500 text-xs font-medium px-3 py-1 rounded-full">
                {{ post.badge.label }}
              </span>
            </div>
            <p class="text-gray-600 dark:text-gray-300 mb-6">{{ post.description }}</p>
            
            <!-- Tags -->
            <div class="flex flex-wrap gap-2 mb-6">
              <a 
                v-for="postTag in post.tags" 
                :key="postTag"
                :href="`/tags/${encodeURIComponent(postTag.toLowerCase())}/`"
                :class="[postTag.toLowerCase() === decodedTag.toLowerCase() ? 'bg-[#1e88e5]/20 text-blue-500' : 'bg-gray-100 text-gray-800']"
                class="text-xs px-2 py-1 rounded-full hover:bg-[#1e88e5]/10 transition-colors"
              >
                #{{ postTag }}
              </a>
            </div>
            
            <div class="flex items-center mt-auto">
              <div v-if="post.authors && post.authors.length" class="flex items-center">
                <span class="text-sm text-gray-700 font-medium">{{ post.authors[0].name }}</span>
              </div>
              <span class="mx-2 text-gray-300">•</span>
              <time class="text-sm text-gray-600" :datetime="post.date">
                {{ formatDate(post.date) }}
              </time>
            </div>
            <div class="mt-6">
              <NuxtLink 
                :to="getPostUrl(post)" 
                class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg font-medium transition-colors"
              >
                Baca Selengkapnya
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                </svg>
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Empty State -->
      <div v-else class="text-center py-12 bg-gray-100 rounded-lg max-w-3xl mx-auto mb-12">
        <p class="text-gray-600">Tidak ada artikel dengan tag "{{ decodedTag }}"</p>
      </div>
      
      <!-- Navigation -->
      <div class="flex justify-between items-center max-w-3xl mx-auto">
        <a href="/tags/" class="inline-flex items-center text-blue-500 hover:text-blue-500/80 font-medium">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
          </svg>
          Semua Tags
        </a>
        
        <a href="/blog/" class="inline-flex items-center text-blue-500 hover:text-blue-500/80 font-medium">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Kembali ke Blog
        </a>
      </div>
    </div>
  </main>
</template>

<style>
/* Styles for tag page */
</style>