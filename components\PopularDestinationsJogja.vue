<template>
  <section class="py-16 bg-gradient-to-br from-gray-50 to-blue-50">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Destinasi Wisata Populer di Jogja
        </h2>
        <p class="text-lg text-gray-600 max-w-3xl mx-auto">
          Jelajahi tempat-tempat menakjubkan yang akan Anda kunjungi dalam paket wisata kami
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Destination Cards -->
        <div v-for="(destination, index) in destinations" :key="index" class="group">
          <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
            <div class="relative h-48 overflow-hidden">
              <img
                :src="destination.image"
                :alt="destination.name"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
              />
              <div class="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300"></div>
              <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full">
                <div class="flex items-center gap-1">
                  <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                  <span class="text-sm font-medium text-gray-700">{{ destination.rating }}</span>
                </div>
              </div>
            </div>

            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 mb-2">{{ destination.name }}</h3>
              <p class="text-gray-600 mb-4 leading-relaxed">{{ destination.description }}</p>

              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center gap-2 text-sm text-gray-500">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  {{ destination.location }}
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-500">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {{ destination.duration }}
                </div>
              </div>

              <!-- Blog Link Button -->
              <div class="pt-2 border-t border-gray-100">
                <NuxtLink
                  v-if="destination.blogUrl"
                  :to="destination.blogUrl"
                  class="inline-flex items-center gap-2 text-primary hover:text-primary-dark font-medium text-sm transition-colors group/link"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                  Baca Panduan Lengkap
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transform group-hover/link:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </NuxtLink>
                <div
                  v-else
                  class="inline-flex items-center gap-2 text-gray-400 text-sm"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                  Panduan segera hadir
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- CTA Section -->
      <div class="mt-16 text-center">
        <div class="bg-white rounded-2xl shadow-lg p-8">
          <h3 class="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
            Mulai Traveling di Jogja Sekarang
          </h3>
          <p class="text-lg text-gray-600 mb-6">
            Pilih paket Traveling yang sesuai dan jelajahi keindahan Yogyakarta bersama kami
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="#paket-wisata"
              class="bg-primary text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-dark transition-colors"
            >
              Lihat Paket Wisata
            </a>
            <a 
              href="https://wa.me/6285186888837?text=Halo,%20saya%20tertarik%20dengan%20paket%20wisata%20Jogja.%20Bisa%20minta%20informasi%20destinasi%20wisata?"
              target="_blank"
              class="border-2 border-primary text-primary px-8 py-3 rounded-lg font-semibold hover:bg-primary hover:text-white transition-colors"
            >
              Konsultasi Destinasi
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
const destinations = ref([
  {
    name: "Candi Borobudur",
    description: "Candi Buddha terbesar di dunia dan situs warisan dunia UNESCO. Nikmati sunrise yang memukau dari puncak candi bersejarah ini.",
    image: "https://liburanjogja.b-cdn.net/candi-borobudur.webp",
    location: "Magelang",
    duration: "2-3 jam",
    rating: "4.9",
    blogUrl: "/candi-borobudur-keajaiban-dunia-yang-memukau/" // Ready for future blog
  },
  {
    name: "Candi Prambanan",
    description: "Kompleks candi Hindu terbesar di Indonesia dengan arsitektur yang menakjubkan. Destinasi wajib untuk pecinta sejarah dan budaya.",
    image: "https://liburanjogja.b-cdn.net/candi-prambanan-1.webp",
    location: "Sleman",
    duration: "2 jam",
    rating: "4.8",
    blogUrl: "/candi-prambanan-keajaiban-arsitektur-hindu-di-jogja/" // Ready for future blog
  },
  {
    name: "Malioboro Street",
    description: "Jalan legendaris Jogja dengan suasana yang hidup. Tempat terbaik untuk berbelanja oleh-oleh, kuliner, dan merasakan budaya lokal.",
    image: "https://liburanjogja.b-cdn.net/tempat-wisata-jalan-malioboro-yogyakarta-1024x683.webp",
    location: "Kota Jogja",
    duration: "2-3 jam",
    rating: "4.7",
    blogUrl: "#" // Ready for future blog
  },
  {
    name: "Keraton Yogyakarta",
    description: "Istana Sultan dengan arsitektur Jawa yang megah. Pelajari sejarah dan budaya Kesultanan Yogyakarta yang masih lestari hingga kini.",
    image: "https://liburanjogja.b-cdn.net/keraton-jogja.webp",
    location: "Kota Jogja",
    duration: "1-2 jam",
    rating: "4.6",
    blogUrl: "#" // Ready for future blog
  },
  {
    name: "Pantai Parangtritis",
    description: "Pantai eksotis dengan legenda Ratu Kidul. Nikmati sunset yang memukau dan aktivitas seru seperti naik andong di tepi pantai.",
    image: "https://liburanjogja.b-cdn.net/pantai-parangtritis-1.webp",
    location: "Bantul",
    duration: "3-4 jam",
    rating: "4.5",
    blogUrl: "/menjelajahi-pesona-pantai-parangtritis-bantul/" // Ready for future blog
  },
  {
    name: "Taman Sari",
    description: "Kompleks taman air bekas tempat rekreasi keluarga kerajaan. Arsitektur unik dengan kolam-kolam dan terowongan bawah tanah yang misterius.",
    image: "https://liburanjogja.b-cdn.net/gambar-taman-sari-destinasi-liburan-budaya-di-jogja.webp",
    location: "Kota Jogja",
    duration: "1-2 jam",
    rating: "4.4",
    blogUrl: "#" // Ready for future blog
  }
])
</script>
