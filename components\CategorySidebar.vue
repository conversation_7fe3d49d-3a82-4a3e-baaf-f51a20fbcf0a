<template>
  <div class="space-y-6">
    <!-- Search Box -->
    <div class="bg-white p-6 rounded-xl shadow-sm">
      <h3 class="text-lg font-bold mb-4"><PERSON><PERSON></h3>
      <div class="relative">
        <input 
          v-model="searchQuery"
          type="text" 
          placeholder="<PERSON><PERSON> artikel, tips, atau destinasi..."
          class="w-full pl-4 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          @keyup.enter="performSearch"
        />
        <button 
          @click="performSearch"
          class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Categories Navigation -->
    <div class="bg-white p-6 rounded-xl shadow-sm">
      <h3 class="text-lg font-bold mb-4">Kategori</h3>
      <nav class="space-y-2">
        <!-- Main Categories -->
        <div v-for="category in mainCategories" :key="category.slug" class="category-group">
          <NuxtLink 
            :to="`/${category.slug}/`"
            :class="[
              'flex items-center justify-between p-3 rounded-lg transition-colors group',
              isActiveCategory(category.slug) ? 'bg-primary text-white' : 'hover:bg-gray-50'
            ]"
          >
            <div class="flex items-center gap-3">
              <component :is="getCategoryIcon(category.slug)" class="w-5 h-5" />
              <span class="font-medium">{{ category.title }}</span>
            </div>
            <button 
              v-if="hasSubcategories(category.slug)"
              @click.prevent="toggleCategory(category.slug)"
              :class="[
                'p-1 rounded transition-transform',
                expandedCategories.includes(category.slug) ? 'rotate-180' : '',
                isActiveCategory(category.slug) ? 'text-white' : 'text-gray-400'
              ]"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
              </svg>
            </button>
          </NuxtLink>
          
          <!-- Subcategories -->
          <div 
            v-if="hasSubcategories(category.slug) && (expandedCategories.includes(category.slug) || isParentActive(category.slug))"
            class="ml-4 mt-2 space-y-1 border-l-2 border-gray-100 pl-4"
          >
            <NuxtLink
              v-for="subcategory in getSubcategories(category.slug)"
              :key="subcategory.slug"
              :to="`/${subcategory.slug}/`"
              :class="[
                'block p-2 rounded-lg text-sm transition-colors',
                isActiveCategory(subcategory.slug) ? 'bg-primary/10 text-primary font-medium' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              ]"
            >
              {{ subcategory.title }}
            </NuxtLink>
          </div>
        </div>
      </nav>
    </div>

    <!-- Popular Tags -->
    <div class="bg-white p-6 rounded-xl shadow-sm">
      <h3 class="text-lg font-bold mb-4">Tag Populer</h3>
      <div class="flex flex-wrap gap-2">
        <NuxtLink 
          v-for="tag in popularTags" 
          :key="tag.slug" 
          :to="`/blog/tag/${tag.slug}/`"
          class="bg-gray-100 px-3 py-1 rounded-full text-sm hover:bg-primary hover:text-white transition-colors"
        >
          {{ tag.name }}
        </NuxtLink>
      </div>
    </div>

    <!-- Quick Links -->
    <div class="bg-white p-6 rounded-xl shadow-sm">
      <h3 class="text-lg font-bold mb-4">Link Cepat</h3>
      <div class="space-y-3">
        <NuxtLink 
          to="/paket-wisata-jogja" 
          class="flex items-center gap-3 text-gray-600 hover:text-primary transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 9v7.5" />
          </svg>
          <span>Paket Wisata</span>
        </NuxtLink>
        
        <NuxtLink 
          to="/blog" 
          class="flex items-center gap-3 text-gray-600 hover:text-primary transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5M5.25 19.5a2.25 2.25 0 01-2.25-2.25V6.75A2.25 2.25 0 015.25 4.5h13.5A2.25 2.25 0 0121 6.75v10.5a2.25 2.25 0 01-2.25 2.25H5.25z" />
          </svg>
          <span>Blog & Tips</span>
        </NuxtLink>
        
        <a 
          href="https://wa.me/6285186888837" 
          target="_blank"
          class="flex items-center gap-3 text-gray-600 hover:text-green-600 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
          </svg>
          <span>Hubungi Kami</span>
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  currentCategory: {
    type: String,
    default: ''
  },
  categories: {
    type: Array,
    default: () => []
  }
})

const router = useRouter()
const searchQuery = ref('')
const expandedCategories = ref(['wisata-jogja']) // Default expand wisata-jogja

// Category configurations
const categoryConfigs = {
  'wisata-jogja': { title: 'Wisata Jogja', icon: 'MapPinIcon' },
  'wisata-jogja/pantai-di-jogja': { title: 'Pantai di Jogja', icon: 'BeachIcon' },
  'wisata-jogja/wisata-alam-yogyakarta': { title: 'Wisata Alam', icon: 'TreeIcon' },
  'wisata-jogja/wisata-kuliner-di-jogja': { title: 'Kuliner Jogja', icon: 'CakeIcon' },
  'wisata-jogja/wisata-goa-yogyakarta': { title: 'Wisata Goa', icon: 'MountainIcon' },
  'wisata-jogja/wisata-candi': { title: 'Wisata Candi', icon: 'BuildingIcon' },
  'peluang-usaha': { title: 'Peluang Usaha', icon: 'BriefcaseIcon' }
}

// Get main categories (without slash)
const mainCategories = computed(() => {
  return Object.keys(categoryConfigs)
    .filter(slug => !slug.includes('/'))
    .map(slug => ({
      slug,
      title: categoryConfigs[slug].title,
      icon: categoryConfigs[slug].icon
    }))
})

// Check if category has subcategories
const hasSubcategories = (categorySlug) => {
  return Object.keys(categoryConfigs).some(slug => 
    slug.startsWith(categorySlug + '/') && slug !== categorySlug
  )
}

// Get subcategories for a parent category
const getSubcategories = (parentSlug) => {
  return Object.keys(categoryConfigs)
    .filter(slug => slug.startsWith(parentSlug + '/') && slug.split('/').length === 2)
    .map(slug => ({
      slug,
      title: categoryConfigs[slug].title
    }))
}

// Check if current category is active
const isActiveCategory = (categorySlug) => {
  return props.currentCategory === categorySlug
}

// Check if parent category is active
const isParentActive = (parentSlug) => {
  return props.currentCategory.startsWith(parentSlug + '/')
}

// Toggle category expansion
const toggleCategory = (categorySlug) => {
  const index = expandedCategories.value.indexOf(categorySlug)
  if (index > -1) {
    expandedCategories.value.splice(index, 1)
  } else {
    expandedCategories.value.push(categorySlug)
  }
}

// Get category icon component
const getCategoryIcon = (categorySlug) => {
  const iconMap = {
    'wisata-jogja': 'svg',
    'peluang-usaha': 'svg'
  }
  
  // Return a simple SVG component
  return {
    template: `
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
        ${categorySlug === 'wisata-jogja' 
          ? '<path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />'
          : '<path stroke-linecap="round" stroke-linejoin="round" d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 00.75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 00-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0112 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 01-.673-.38m0 0A2.18 2.18 0 013 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 013.413-.387m7.5 0V5.25A2.25 2.25 0 0013.5 3h-3a2.25 2.25 0 00-2.25 2.25v.894m7.5 0a48.667 48.667 0 00-7.5 0M12 12.75h.008v.008H12v-.008z" />'
        }
      </svg>
    `
  }
}

// Popular tags
const popularTags = ref([
  { name: 'Wisata Jogja', slug: 'wisata-jogja' },
  { name: 'Kuliner', slug: 'kuliner' },
  { name: 'Pantai', slug: 'pantai' },
  { name: 'Candi', slug: 'candi' },
  { name: 'Alam', slug: 'alam' },
  { name: 'Budaya', slug: 'budaya' },
  { name: 'Sejarah', slug: 'sejarah' },
  { name: 'Tips Travel', slug: 'tips-travel' }
])

// Search functionality
const performSearch = () => {
  if (searchQuery.value.trim()) {
    router.push(`/blog/search?q=${encodeURIComponent(searchQuery.value)}`)
  }
}

// Auto-expand parent category if subcategory is active
onMounted(() => {
  if (props.currentCategory.includes('/')) {
    const parentCategory = props.currentCategory.split('/')[0]
    if (!expandedCategories.value.includes(parentCategory)) {
      expandedCategories.value.push(parentCategory)
    }
  }
})
</script>

<style scoped>
.category-group {
  @apply relative;
}

.category-group:not(:last-child) {
  @apply mb-2;
}
</style>
