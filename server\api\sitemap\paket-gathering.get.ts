export default defineEventHandler(async (event) => {
  try {
    // Import data gathering dengan path relatif
    const gatheringData = await import('../../../data/gathering.json')
    const { gatheringPackages } = gatheringData.default || gatheringData
    
    // Generate sitemap URLs untuk paket gathering
    const urls = gatheringPackages.map((paket: any) => ({
      loc: `/paket-gathering/${paket.slug}/`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: 0.8
    }))
    
    return urls
  } catch (error) {
    console.error('Error generating paket gathering sitemap:', error)
    return []
  }
}) 