import type { NitroApp } from 'nitropack'

interface Destination {
  id: string
  slug: string
  name: string
  description: string
  location: string
  price: string
  rating: number
  reviews: number
  image: string
  duration: string
  highlights: string[]
  category: string
}

interface DestinationsData {
  destinations: Destination[]
}

export default defineEventHandler(async (event) => {
  try {
    const params = getRouterParams(event)
    const slug = params?.slug
    
    if (!slug || typeof slug !== 'string') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid slug parameter'
      })
    }

    const data = await useStorage().getItem('server:data/destinations.json') as DestinationsData | null
    
    if (!data || !data.destinations || !Array.isArray(data.destinations)) {
      console.error('No destinations data found in storage')
      throw createError({
        statusCode: 404,
        statusMessage: 'Destinations data not found'
      })
    }
    
    const destination = data.destinations.find((d: Destination) => d.slug === slug)
    
    if (!destination) {
      console.error(`Destination with slug "${slug}" not found`)
      throw createError({
        statusCode: 404,
        statusMessage: 'Destination not found'
      })
    }

    return destination
  } catch (error) {
    console.error(`Error in destinations/${params?.slug}.get:`, error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})