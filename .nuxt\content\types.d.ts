import type { PageCollectionItemBase, DataCollectionItemBase } from '@nuxt/content'

declare module '@nuxt/content' {
   /* eslint-disable */
  /**
   * This file was automatically generated by json-schema-to-typescript.
   * DO NOT MODIFY IT BY HAND. Instead, modify the source JSONSchema file,
   * and run json-schema-to-typescript to regenerate this file.
   */
  
  interface PostsCollectionItem extends PageCollectionItemBase {
    sitemap?: {
    priority?: number;
    changefreq?: "always" | "hourly" | "daily" | "weekly" | "monthly" | "yearly" | "never";
    };
    title: string;
    description: string;
    image?:
    | string
    | {
      src: string;
      };
    authors?: {
    name: string;
    to?: string;
    avatar?: {
      src?: string;
    };
    }[];
    date: string;
    tags?: string[];
    popular?: boolean;
  }
  

  interface PageCollections {
    posts: PostsCollectionItem
  }

  interface Collections {
    posts: PostsCollectionItem
  }
}
