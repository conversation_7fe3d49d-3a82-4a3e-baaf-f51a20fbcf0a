<template>
  <div>
    <!-- Filter Container -->
    <div class="bg-white rounded-xl shadow-sm p-5">
      <!-- <PERSON><PERSON><PERSON> -->
      <div class="mb-6">
        <h3 class="text-xl font-bold mb-4"><PERSON><PERSON><PERSON></h3>
        <div class="space-y-2.5">
          <NuxtLink
            to="/sewa-mobil-jogja/"
            class="block px-4 py-2.5 rounded-lg text-sm transition-colors"
            :class="!activeCategory ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
          >
            <div class="flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 1-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m8.25 4.5V16.5a1.125 1.125 0 0 1 1.125-1.125h2.25a1.125 1.125 0 0 1 1.125 1.125v1.875a1.125 1.125 0 0 1-1.125 1.125H9.375A1.125 1.125 0 0 1 8.25 15V14.25m8.25 4.5h-2.25a1.125 1.125 0 0 1-1.125-1.125V16.5h3.375a1.125 1.125 0 0 1 1.125 1.125v1.875Z" />
              </svg>
              Semua Kendaraan
            </div>
          </NuxtLink>
          <NuxtLink
            v-for="category in categories"
            :key="category.slug"
            :to="`/sewa-mobil-jogja/${category.slug}/`"
            class="block px-4 py-2.5 rounded-lg text-sm transition-colors"
            :class="activeCategory === category.slug ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
          >
            <div class="flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 1-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m8.25 4.5V16.5a1.125 1.125 0 0 1 1.125-1.125h2.25a1.125 1.125 0 0 1 1.125 1.125v1.875a1.125 1.125 0 0 1-1.125 1.125H9.375A1.125 1.125 0 0 1 8.25 15V14.25m8.25 4.5h-2.25a1.125 1.125 0 0 1-1.125-1.125V16.5h3.375a1.125 1.125 0 0 1 1.125 1.125v1.875Z" />
              </svg>
              {{ category.name }}
            </div>
          </NuxtLink>
        </div>
      </div>

      <!-- Divider -->
      <div class="border-t border-gray-200 my-6"></div>

      <!-- Paket Wisata Terkait -->
      <div class="mb-6">
        <h3 class="text-xl font-bold mb-4">Paket Wisata Terkait</h3>
        <div class="space-y-3">
          <!-- Featured Travel Packages -->
          <template v-if="popularPackages.length > 0">
            <NuxtLink 
              v-for="paket in popularPackages.slice(0, 2)" 
              :key="paket.id"
              :to="`/paket-wisata-jogja/${paket.slug}/`"
              class="border border-gray-200 rounded-lg p-3 hover:border-primary transition-colors block"
            >
              <div class="flex items-center gap-3">
                <img 
                  :src="paket.image" 
                  :alt="paket.title"
                  class="w-12 h-12 object-cover rounded-lg"
                />
                <div class="flex-1 min-w-0">
                  <h4 class="font-medium text-sm text-gray-900 truncate">{{ paket.title }}</h4>
                  <div class="flex items-center gap-2 text-xs text-gray-500">
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-3 h-3 text-yellow-500">
                        <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
                      </svg>
                      <span class="ml-1">{{ paket.rating }}</span>
                    </div>
                    <span>•</span>
                    <span>{{ getDurationLabel(paket.duration) }}</span>
                  </div>
                  <div class="text-sm font-medium text-primary mt-1">
                    Mulai {{ getPackagePrice(paket) }}
                  </div>
                </div>
              </div>
            </NuxtLink>
          </template>
          
          <!-- Fallback if no packages available -->
          <template v-else>
            <div class="text-center py-4 text-gray-500 text-sm">
              <p>Sedang memuat paket wisata...</p>
            </div>
          </template>

          <!-- Button Paket Wisata -->
          <NuxtLink
            to="/paket-wisata-jogja/"
            class="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-3 px-4 rounded-lg font-medium hover:from-green-700 hover:to-green-800 transition-all duration-300 shadow-sm hover:shadow-md block"
          >
            <div class="flex items-center justify-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z" />
              </svg>
              Lihat Paket Wisata
            </div>
          </NuxtLink>
        </div>
      </div>

      <!-- Divider -->
      <div class="border-t border-gray-200 my-6"></div>

      <!-- Quick Contact -->
      <div>
        <h3 class="text-xl font-bold mb-4">Butuh Bantuan?</h3>
        <div class="space-y-3">
          <!-- WhatsApp Contact -->
          <button
            @click="openWhatsApp"
            class="w-full bg-gradient-to-r from-primary to-primary-dark text-white py-3 px-4 rounded-lg font-medium hover:from-primary-dark hover:to-primary transition-all duration-300 shadow-sm hover:shadow-md"
          >
            <div class="flex items-center justify-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z" />
              </svg>
              Konsultasi Gratis
            </div>
          </button>

          <!-- Phone Contact -->
          <a
            href="tel:+6285186888837"
            class="w-full border-2 border-primary text-primary py-3 px-4 rounded-lg font-medium hover:bg-primary/10 transition-colors text-center block"
          >
            <div class="flex items-center justify-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z" />
              </svg>
              Telepon Langsung
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import sewaMobilData from '~/data/sewa-mobil.json'
import paketWisataData from '~/data/paket-wisata.json'

const props = defineProps({
  activeCategory: {
    type: String,
    default: ''
  }
})

// Get vehicle categories
const categories = computed(() => sewaMobilData.categories.filter(cat => cat.id !== 'semua-mobil'))

// Get popular travel packages (top rated packages)
const popularPackages = computed(() => {
  if (!paketWisataData || paketWisataData.length === 0) return []
  
  return paketWisataData
    .filter(paket => paket.rating >= 4.7) // Filter high rated packages
    .sort((a, b) => {
      // Sort by rating first, then by reviews count
      if (b.rating !== a.rating) {
        return b.rating - a.rating
      }
      return (b.reviews || 0) - (a.reviews || 0)
    })
    .slice(0, 2) // Get top 2 packages for sidebar
})

// Helper function to get duration label
const getDurationLabel = (duration) => {
  const labels = {
    '1-hari': '1 Hari',
    '2-hari-1-malam': '2D1N',
    '3-hari-2-malam': '3D2N',
    '4-hari-3-malam': '4D3N'
  }
  return labels[duration] || duration
}

// Helper function to get package price
const getPackagePrice = (paket) => {
  if (paket.pricing && paket.pricing.length > 0) {
    return paket.pricing[0].price
  }
  return paket.price || 'Rp 350.000'
}

// Open WhatsApp for rental inquiry
const openWhatsApp = () => {
  const message = `Halo! 👋

Saya tertarik untuk *sewa mobil di Jogja* 🚗

Bisa bantuin saya untuk:
• Informasi armada mobil yang tersedia
• Harga sewa mobil terbaru
• Paket rental + driver
• Syarat dan ketentuan sewa
• Rekomendasi mobil yang cocok

Terima kasih! 🙏`

  const whatsappUrl = `https://wa.me/6285186888837?text=${encodeURIComponent(message)}`
  window.open(whatsappUrl, '_blank')
}
</script> 