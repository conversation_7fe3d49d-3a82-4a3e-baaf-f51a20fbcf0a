/**
 * Composable for handling images with fallback to placeholder images
 */
export const useImageWithFallback = () => {
  // Function to get safe image URL with fallback
  const getSafeImageUrl = (imageUrl: string, type: string = 'default') => {
    // If image URL is empty or undefined, return fallback image
    if (!imageUrl) {
      return getFallbackImage(type)
    }

    // Return the original image URL
    return imageUrl
  }

  // Function to get fallback image based on type
  const getFallbackImage = (type: string) => {
    const fallbackImages: Record<string, string> = {
      'tanpa-hotel': '/images/fallback/tanpa-hotel.jpg',
      'dengan-hotel': '/images/fallback/dengan-hotel.jpg',
      'honeymoon': '/images/fallback/honeymoon.jpg',
      'gathering': '/images/fallback/gathering.jpg',
      '1-hari': '/images/fallback/1-hari.jpg',
      '2-hari-1-malam': '/images/fallback/2-hari-1-malam.jpg',
      '3-hari-2-malam': '/images/fallback/3-hari-2-malam.jpg',
      '4-hari-3-malam': '/images/fallback/4-hari-3-malam.jpg',
      'hero': '/images/fallback/hero.jpg',
      'default': '/images/fallback/default.jpg'
    }

    return fallbackImages[type] || fallbackImages.default
  }

  return {
    getSafeImageUrl,
    getFallbackImage
  }
}
