<template>
  <div>
    <!-- <PERSON>readcrumb -->
    <div class="bg-gray-50 py-4">
      <div class="container mx-auto px-4">
        <nav class="flex items-center space-x-2 text-sm">
          <NuxtLink to="/" class="text-gray-500 hover:text-gray-700">Home</NuxtLink>
          <span class="text-gray-400">/</span>
          <span class="text-gray-900 font-medium">Sewa Mobil Jogja</span>
        </nav>
      </div>
    </div>

    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-primary to-primary-dark text-white py-16">
      <div class="container mx-auto px-4">
        <div class="text-center max-w-4xl mx-auto">
          <h1 class="text-4xl md:text-5xl font-bold mb-6">
            Sewa Mobil Jogja Murah & Terpercaya
          </h1>
          <p class="text-xl md:text-2xl mb-8 text-white/90">
            <PERSON><PERSON><PERSON> leng<PERSON><PERSON> kendaraan untuk liburan, bis<PERSON>, dan k<PERSON><PERSON><PERSON> harian Anda di Yogyakarta
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button 
              @click="scrollToVehicles"
              class="bg-white text-primary px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Lihat Kendaraan
            </button>
            <button 
              @click="openWhatsApp"
              class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors"
            >
              Konsultasi Gratis
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold mb-4">Mengapa Rental Mobil di 3J Tour Jogja?</h2>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">Layanan rental mobil terbaik dengan berbagai keunggulan</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div class="text-center">
            <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2">Harga Rental Terjangkau</h3>
            <p class="text-gray-600">Tarif kompetitif dengan kualitas mobil terbaik</p>
          </div>
          
          <div class="text-center">
            <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2">Kondisi Kendaraan Prima</h3>
            <p class="text-gray-600">Kendaraan terawat dan siap menemani perjalanan anda</p>
          </div>
          
          <div class="text-center">
            <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2">Layanan 24/7</h3>
            <p class="text-gray-600">layanan 24 jam untuk menggaransi kenyamanan perjalanan anda</p>
          </div>
          
          <div class="text-center">
            <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2">Sopir Berpengalaman</h3>
            <p class="text-gray-600">Driver profesional dan ramah siap menemani perjalanan anda</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Vehicle Categories Section -->
    <section id="vehicles" class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold mb-4">Pilihan Sewa Mobil Jogja Terjangkau</h2>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">Berbagai jenis mobil rental di Jogja untuk menemani perjalanan Anda di Yogyakarta dengan harga terjangkau</p>
        </div>

        <!-- Main Content with Sidebar -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Sidebar (Desktop) -->
          <!-- <div class="lg:block hidden">
            <SewaMobilFilter />
          </div> -->

          <!-- Main Content -->
          <div class="lg:col-span-3">
            <!-- Category Tabs -->
            <div class="flex flex-wrap justify-center gap-2 mb-12">
              <button
                v-for="category in categories"
                :key="category.id"
                @click="activeCategory = category.id"
                :class="[
                  'px-6 py-3 rounded-full font-medium transition-all duration-300',
                  activeCategory === category.id
                    ? 'bg-primary text-white shadow-lg'
                    : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
                ]"
              >
                {{ category.name }}
              </button>
            </div>

            <!-- Vehicle Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div
                v-for="car in filteredCars"
                :key="car.id"
                class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
              >
                <!-- Car Image -->
                <div class="relative h-48 overflow-hidden">
                  <img 
                    :src="car.image" 
                    :alt="car.name"
                    class="w-full h-full object-cover"
                  />
                  <div class="absolute top-4 right-4">
                    <div class="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 flex items-center gap-1">
                      <svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                      </svg>
                      <span class="text-sm font-medium text-gray-900">{{ car.rating }}</span>
                    </div>
                  </div>
                </div>

                <!-- Car Info -->
                <div class="p-6">
                  <h3 class="text-xl font-bold text-gray-900 mb-2">{{ car.name }}</h3>
                  <p class="text-gray-600 mb-4 line-clamp-2">{{ car.description }}</p>
                  
                  <!-- Car Specs -->
                  <div class="grid grid-cols-3 gap-4 mb-6">
                    <div class="text-center">
                      <div class="text-sm text-gray-500">Kapasitas</div>
                      <div class="font-semibold text-gray-900">{{ car.capacity }}</div>
                    </div>
                    <div class="text-center">
                      <div class="text-sm text-gray-500">Transmisi</div>
                      <div class="font-semibold text-gray-900">{{ car.transmission }}</div>
                    </div>
                    <div class="text-center">
                      <div class="text-sm text-gray-500">Tahun</div>
                      <div class="font-semibold text-gray-900">{{ car.year }}</div>
                    </div>
                  </div>

                  <!-- Pricing -->
                  <div class="border-t border-gray-200 pt-4 mb-6">
                    <div class="text-center mb-3">
                      <div class="text-sm text-gray-500 mb-2">Mulai dari</div>
                      <div class="text-xl font-bold text-primary">{{ formatPrice(car.packages?.ms?.pricing?.['12jam'] || 0) }}</div>
                      <div class="text-xs text-gray-400">Paket MS - 12 Jam</div>
                    </div>
                    <div class="text-xs text-gray-500 text-center">
                      Tersedia paket MS, MSB, AIO
                    </div>
                  </div>

                  <!-- Action Buttons -->
                  <div class="flex gap-3">
                    <NuxtLink 
                      :to="`/sewa-mobil-jogja/${car.slug}/`"
                      class="flex-1 bg-primary text-white py-2 px-4 rounded-lg font-medium hover:bg-primary-dark transition-colors text-center"
                    >
                      Detail
                    </NuxtLink>
                    <button 
                      @click="bookCar(car)"
                      class="flex-1 border-2 border-primary text-primary py-2 px-4 rounded-lg font-medium hover:bg-primary/10 transition-colors"
                    >
                      Pesan
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Mobile Sidebar -->
        <div class="lg:hidden mt-8">
          <div class="bg-white rounded-xl shadow-lg p-6">
            <h3 class="text-xl font-bold mb-4">Kategori & Bantuan</h3>
            <SewaMobilFilter />
          </div>
        </div>
      </div>
    </section>

    <!-- Booking Process Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold mb-4">Proses Rental Mobil di 3J Tour Jogja</h2>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">Proses booking dan pemesanan mobil rental yang mudah dan cepat</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div class="text-center">
            <div class="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">1</div>
            <h3 class="text-xl font-semibold mb-2">Pilih Mobil</h3>
            <p class="text-gray-600">Pilih kendaraan sesuai kebutuhan Anda</p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">2</div>
            <h3 class="text-xl font-semibold mb-2">Hubungi Kami</h3>
            <p class="text-gray-600">Konsultasi via WhatsApp atau telepon</p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">3</div>
            <h3 class="text-xl font-semibold mb-2">Konfirmasi</h3>
            <p class="text-gray-600">Konfirmasi detail dan pembayaran</p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">4</div>
            <h3 class="text-xl font-semibold mb-2">Ambil Mobil</h3>
            <p class="text-gray-600">Mobil siap digunakan sesuai jadwal</p>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold mb-4">FAQ Sewa Mobil di 3J Tour Jogja</h2>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">Jawaban untuk pertanyaan yang sering diajukan oleh penyewa mobil</p>
        </div>

        <div class="max-w-4xl mx-auto space-y-6">
          <div class="bg-white rounded-xl p-6 shadow-lg">
            <h3 class="text-xl font-semibold mb-3">Apakah harga sudah termasuk BBM?</h3>
            <p class="text-gray-600">Harga belum termasuk BBM. BBM ditanggung oleh penyewa sesuai pemakaian.</p>
          </div>

          <div class="bg-white rounded-xl p-6 shadow-lg">
            <h3 class="text-xl font-semibold mb-3">Bagaimana sistem pembayaran?</h3>
            <p class="text-gray-600">Pembayaran dapat dilakukan via transfer bank, cash, atau e-wallet. DP minimal 50% untuk konfirmasi booking.</p>
          </div>

          <div class="bg-white rounded-xl p-6 shadow-lg">
            <h3 class="text-xl font-semibold mb-3">Apakah ada jaminan asuransi?</h3>
            <p class="text-gray-600">Ya, semua kendaraan sudah tercover asuransi. Namun ada excess/deductible sesuai ketentuan asuransi.</p>
          </div>

          <div class="bg-white rounded-xl p-6 shadow-lg">
            <h3 class="text-xl font-semibold mb-3">Berapa lama minimal sewa?</h3>
            <p class="text-gray-600">Minimal sewa 12 jam. Untuk sewa harian dihitung 16 jam dari waktu pengambilan.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-primary text-white">
      <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">Siap Memulai Perjalanan di Jogja?</h2>
        <p class="text-xl mb-8 text-white/90">Hubungi kami sekarang untuk booking Mobil dan konsultasi gratis</p>

        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            @click="openWhatsApp"
            class="bg-white text-primary px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            WhatsApp Sekarang
          </button>
          <!-- <a
            href="tel:+6285186888837"
            class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors"
          >
            Telepon Langsung
          </a> -->
          <NuxtLink
            to="/paket-wisata-jogja/"
            class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors"
          >
            Cek Paket Wisata Jogja
          </NuxtLink>
        </div>

        <div class="mt-8 text-white/80">
          <p>📞 + 62 8518 6888 837 | 📧 <EMAIL></p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import sewaMobilData from '~/data/sewa-mobil.json'
import SewaMobilFilter from '~/components/SewaMobilFilter.vue'
import { useSchemaRental } from '~/composables/seo/useSchemaRental'

// Reactive data
const activeCategory = ref('semua-mobil')

// Get data from JSON
const cars = computed(() => sewaMobilData.sewaMobil)
const categories = computed(() => sewaMobilData.categories)

// Filter cars by category
const filteredCars = computed(() => {
  if (activeCategory.value === 'semua-mobil') {
    return cars.value
  }
  return cars.value.filter(car => car.category === activeCategory.value)
})

// FAQ data for rental services
const faqs = [
  {
    question: 'Apakah harga sudah termasuk BBM?',
    answer: 'Harga belum termasuk BBM. BBM ditanggung oleh penyewa sesuai pemakaian.'
  },
  {
    question: 'Bagaimana sistem pembayaran?',
    answer: 'Pembayaran dapat dilakukan via transfer bank, cash, atau e-wallet. DP minimal 50% untuk konfirmasi booking.'
  },
  {
    question: 'Apakah ada jaminan asuransi?',
    answer: 'Ya, semua kendaraan sudah tercover asuransi. Namun ada excess/deductible sesuai ketentuan asuransi.'
  },
  {
    question: 'Berapa lama minimal sewa?',
    answer: 'Minimal sewa 12 jam. Untuk sewa harian dihitung 16 jam dari waktu pengambilan.'
  },
  {
    question: 'Apakah bisa sewa mobil tanpa sopir?',
    answer: 'Ya, tersedia opsi lepas kunci (self drive) dengan syarat dan ketentuan tertentu.'
  },
  {
    question: 'Area jangkauan layanan sampai mana?',
    answer: 'Layanan meliputi seluruh Yogyakarta dan sekitarnya. Untuk area luar kota ada biaya tambahan.'
  }
]

// Schema Markup
const { generateRentalSchema } = useSchemaRental()

const schemaData = computed(() => generateRentalSchema({
  pageType: 'index',
  items: cars.value,
  title: 'Sewa Mobil Jogja Murah & Terpercaya - Rental Mobil Yogyakarta',
  description: 'Rental mobil Jogja dengan pilihan lengkap: city car, MPV, minibus. Harga mulai 150 ribu. Driver berpengalaman, kondisi prima, layanan 24/7.',
  faqs
}))

// Methods
const formatPrice = (price) => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0
  }).format(price)
}

const scrollToVehicles = () => {
  document.getElementById('vehicles').scrollIntoView({
    behavior: 'smooth'
  })
}

const openWhatsApp = () => {
  const message = 'Halo, saya ingin konsultasi tentang sewa mobil di Jogja. Bisa minta informasi lebih detail?'
  const whatsappUrl = `https://wa.me/6281325005958?text=${encodeURIComponent(message)}`
  window.open(whatsappUrl, '_blank')
}

const bookCar = (car) => {
  const message = `Halo, saya tertarik dengan ${car.name}. Bisa minta informasi lebih detail dan cara pemesanannya?`
  const whatsappUrl = `https://wa.me/6281325005958?text=${encodeURIComponent(message)}`
  window.open(whatsappUrl, '_blank')
}

// SEO metadata with schema
useHead(() => ({
  title: 'Sewa Mobil Jogja Murah - Rental Mobil Yogyakarta | 3J Tour',
  meta: [
    {
      name: 'description',
      content: 'Sewa mobil Jogja murah dengan pilihan mobil rental lengkap: Avanza, Innova, Hiace, dll. Harga mulai 150 ribu. Driver berpengalaman dan layanan 24/7.'
    },
    {
      name: 'keywords',
      content: 'sewa mobil jogja, rental mobil yogyakarta, sewa mobil murah jogja, rental avanza jogja, sewa innova jogja, rental hiace jogja'
    },
    {
      property: 'og:title',
      content: 'Sewa Mobil Jogja Murah - Rental Mobil Yogyakarta | 3J Tour'
    },
    {
      property: 'og:description',
      content: 'Sewa mobil Jogja murah dengan pilihan mobil rental lengkap: Avanza, Innova, Hiace, dll. Harga mulai 150 ribu. Driver berpengalaman dan layanan 24/7'
    },
    {
      property: 'og:type',
      content: 'website'
    },
    {
      property: 'og:url',
      content: 'https://www.jogjaliburan.com/sewa-mobil-jogja/'
    }
  ],
  link: [
    {
      rel: 'canonical',
      href: 'https://www.jogjaliburan.com/sewa-mobil-jogja/'
    }
  ],
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify(schemaData.value)
    }
  ]
}))
</script>
