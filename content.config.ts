import { defineContentConfig, defineCollection } from '@nuxt/content'
import { asSitemapCollection } from '@nuxtjs/sitemap/content'
import { z } from 'zod'

export default defineContentConfig({
  collections: {
    posts: defineCollection(
      asSitemapCollection({
        type: 'page',
        source: {
          include: 'blog/**/*',
          prefix: '/'
        },
        schema: z.object({
          title: z.string().nonempty(),
          description: z.string().nonempty(),
          image: z.union([
            z.string().nonempty(), // Support direct URL string
            z.object({
              src: z.string().nonempty()
            })
          ]).optional(),
          authors: z.array(
            z.object({
              name: z.string().nonempty(),
              to: z.string().optional(),
              avatar: z.object({ src: z.string().optional() }).optional()
            })
          ).optional(),
          date: z.date(),
          tags: z.array(z.string()).optional(),
          popular: z.boolean().optional(),
          sitemap: z.object({
            priority: z.number().min(0).max(1).optional().default(0.7),
            changefreq: z.enum(['always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never']).optional().default('weekly'),
          }).optional()
        })
      })
    )
  }
})
