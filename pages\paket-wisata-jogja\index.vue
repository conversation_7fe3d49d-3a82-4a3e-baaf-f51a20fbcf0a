<template>
  <div>
    <main class="">
      <!-- Hero Section -->
      <section class="relative h-[60vh] lg:h-[85vh] bg-gray-900">
        <img
          :src="getSafeImageUrl('https://liburanjogja.b-cdn.net/taman-sari-kraton-yogyakarta-2-1024x683.webp', 'hero')"
          alt="Paket Wisata Jogja"
          class="w-full h-full object-cover opacity-50"
        />
        <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center">
          <div class="container mx-auto px-4">
            <div class="max-w-2xl">
              <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
                Paket Wisata Jogja Custom Harga Terbaik
              </h1>
              <p class="text-xl text-white/80 max-w-2xl mb-8">
                Temukan pengalaman wisata terbaik di Jogja dengan paket wisata Jogja kami yang dirancang khusus untuk Anda
              </p>
              <div class="flex flex-wrap items-center gap-6 text-white">
                <div class="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                  </svg>
                  <span>Yogyakarta</span>
                </div>
                <div class="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5 text-yellow-500">
                    <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
                  </svg>
                  <span>4.8 (156 ulasan)</span>
                </div>
                <div class="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>1-4 Hari</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Why Choose Us Section -->
      <WhyChooseUs />
<PopularDestinations />
      <!-- Main Content Section -->
      <section id="paket-wisata-jogja-terlengkap" class="py-16">
        <div class="px-[9%]">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold mb-4">Pilihan Paket Wisata Jogja Terlengkap</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
              Temukan paket wisata Jogja yang sesuai dengan kebutuhan dan preferensi Anda
            </p>
          </div>

          <div class="flex flex-col lg:flex-row gap-8">
            <!-- Sidebar Filter -->
            <div class="lg:w-1/5">
              <PaketWisataFilter 
                :active-category="activeCategory"
                :active-duration="activeDuration"
              />
            </div>
            <!-- Main Content -->
            <div class="lg:w-4/5">
              <div v-if="isLoading" class="text-center py-12">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary border-t-transparent"></div>
                <p class="text-lg text-gray-600 mt-4">Memuat paket wisata...</p>
              </div>

              <div v-else-if="error" class="text-center py-12">
                <p class="text-lg text-red-600">{{ error }}</p>
              </div>

              <div v-else>
                <!-- Paket Wisata Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6">
                  <div v-for="paket in filteredDestinations" :key="paket.id">
                    <PaketWisataCard :paket="paket" />
                  </div>
                </div>

                <div v-if="filteredDestinations.length === 0" class="text-center py-12">
                  <p class="text-lg text-gray-600">Tidak ada paket wisata yang sesuai dengan filter</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    <!-- Popular Destinations Section -->
      <PopularDestinationsJogja />
      <!-- How It Works Section -->
      <HowItWorks />

      <!-- Reviews Section -->
      <ReviewsSection />

      <!-- FAQ Section -->
      <FAQSection />
    </main>

  </div>
</template>

<script setup>
import { usePaketWisata } from '~/composables/usePaketWisata'
import { useImageWithFallback } from '~/composables/useImageWithFallback'
import { useSchemaStructured } from '~/composables/seo/useSchemaStructured'
import { useRoute } from 'vue-router'
import WhyChooseUs from '~/components/WhyChooseUs.vue'
import PopularDestinationsJogja from '~/components/PopularDestinationsJogja.vue'
import HowItWorks from '~/components/HowItWorks.vue'
import ReviewsSection from '~/components/ReviewsSection.vue'
import FAQSection from '~/components/FAQSection.vue'

const route = useRoute()
const {
  isLoading,
  error,
  getAllPaketWisata,
  getPaketWisataByCategory,
  getPaketWisataByDuration,
  getAllCategories,
  getAllDurations
} = usePaketWisata()

const { getSafeImageUrl } = useImageWithFallback()

// State untuk filter
const selectedPrice = ref('')
const activeCategory = ref('')
const activeDuration = ref('')

// Get all categories and durations
const categories = getAllCategories.value
const durations = getAllDurations.value

// Filter paket wisata berdasarkan filter yang dipilih
const filteredDestinations = computed(() => {
  return getAllPaketWisata() // Menampilkan semua paket wisata
})

// Filter untuk paket tanpa hotel
const popularWithoutHotel = computed(() => {
  const filtered = getPaketWisataByCategory('tanpa-hotel')
  return filtered
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 3)
})

// Filter untuk paket dengan hotel
const popularWithHotel = computed(() => {
  const filtered = getPaketWisataByCategory('dengan-hotel')
  return filtered
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 3)
})

// Filter untuk paket honeymoon
const popularHoneymoon = computed(() => {
  const filtered = getPaketWisataByCategory('honeymoon')
  return filtered
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 3)
})

// Filter untuk paket gathering
const popularGathering = computed(() => {
  const filtered = getPaketWisataByCategory('gathering')
  return filtered
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 3)
})

// Filter untuk paket berdasarkan durasi
const paketSatuHari = computed(() => {
  const filtered = getPaketWisataByDuration('1-hari')
  return filtered
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 3)
})

const paketDuaHari = computed(() => {
  const filtered = getPaketWisataByDuration('2-hari-1-malam')
  return filtered
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 3)
})

// Import structured schema composable
const { generateSchema } = useSchemaStructured()

// FAQ data for schema
const faqData = [
  {
    question: "Berapa harga paket wisata Jogja?",
    answer: "Harga paket wisata Jogja mulai dari Rp 350.000 per orang untuk paket 1 hari. Harga bervariasi tergantung durasi, fasilitas, dan jumlah peserta."
  },
  {
    question: "Apa saja yang termasuk dalam paket wisata?",
    answer: "Paket wisata kami meliputi transportasi, pemandu wisata profesional, tiket masuk destinasi, dan makan sesuai program. Untuk paket dengan hotel, sudah termasuk akomodasi."
  },
  {
    question: "Apakah bisa custom itinerary sesuai keinginan?",
    answer: "Ya, kami menyediakan paket custom sesuai kebutuhan dan keinginan Anda. Hubungi tim kami untuk konsultasi gratis."
  },
  {
    question: "Bagaimana cara booking paket wisata?",
    answer: "Anda bisa booking melalui WhatsApp, mengisi form di website, atau menghubungi customer service kami. Proses booking mudah dan cepat."
  },
  {
    question: "Apakah cocok untuk wisata keluarga dengan anak-anak?",
    answer: "Sangat cocok! Paket wisata kami family-friendly dengan destinasi yang aman dan menarik untuk anak-anak. Kami juga menyediakan fasilitas khusus seperti car seat untuk anak dan dapat menyesuaikan itinerary dengan kebutuhan keluarga."
  },
  {
    question: "Kapan waktu terbaik untuk wisata ke Jogja?",
    answer: "Jogja dapat dikunjungi sepanjang tahun. Musim kemarau (April-Oktober) ideal untuk wisata outdoor, sedangkan musim hujan (November-Maret) cocok untuk wisata indoor seperti museum dan keraton. Kami siap melayani kapan saja Anda ingin berkunjung."
  },
  {
    question: "Apakah tersedia paket untuk gathering perusahaan?",
    answer: "Ya, kami memiliki paket khusus gathering perusahaan dengan fasilitas meeting room, team building activities, dan program leadership training. Paket dapat disesuaikan dengan jumlah peserta dan kebutuhan perusahaan Anda."
  },
  {
    question: "Bagaimana jika cuaca buruk saat tour?",
    answer: "Kami memiliki rencana alternatif untuk cuaca buruk. Destinasi outdoor dapat diganti dengan wisata indoor seperti museum, keraton, atau pusat perbelanjaan. Tim guide kami berpengalaman menangani berbagai kondisi cuaca."
  }
]

// Generate structured schema
const schemaData = generateSchema({
  pageType: 'index',
  title: 'Paket Wisata Jogja Murah 2024 - Tour Yogyakarta Terbaik',
  description: 'Paket wisata Jogja murah mulai Rp 350rb! Tersedia paket tour Jogja 1 hari, 2 hari 1 malam, 3 hari 2 malam. Paket custom, keluarga, honeymoon & gathering.',
  items: filteredDestinations.value,
  faqs: faqData
})

// SEO metadata
useHead({
  title: 'Paket Wisata Jogja Murah mulai 350K Durasi 1-4 Hari | 3J Tour',
  meta: [
    { name: 'description', content: 'Paket wisata Jogja murah mulai Rp 350rb! Tersedia paket tour 1 hari sampai 4 hari 3 malam dengan pilihan destinasi pantai, alam, city tour, hingga budaya.' },
    { name: 'keywords', content: 'paket wisata jogja, paket tour jogja, paket wisata jogja murah, paket wisata jogja custom, paket wisata jogja 2 hari, paket wisata jogja 3h2m, paket wisata yogyakarta, tour paket jogja, paket travel jogja, paket wisata di jogja, paket wisata murah jogja, paket wisata jogja 1 hari, paket study tour jogja, wisata malam jogja' },
    // Open Graph / Facebook
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: 'https://www.jogjaliburan.com/paket-wisata-jogja/' },
    { property: 'og:title', content: 'Paket Wisata Jogja Murah mulai 350K Durasi 1-4 Hari | 3J Tour' },
    { property: 'og:description', content: 'Paket wisata Jogja murah mulai Rp 350rb! Tersedia paket tour 1 hari sampai 4 hari 3 malam dengan pilihan destinasi pantai, alam, city tour, hingga budaya.' },
    { property: 'og:image', content: 'https://images.unsplash.com/photo-1630214801769-24784bfd2b9c?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D' },
    // Twitter
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:url', content: 'https://www.jogjaliburan.com/paket-wisata-jogja/' },
    { name: 'twitter:title', content: 'Paket Wisata Jogja Murah mulai 350K Durasi 1-4 Hari | 3J Tour' },
    { name: 'twitter:description', content: 'Paket wisata Jogja murah mulai Rp 350rb! Tersedia paket tour Jogja 1 hari, 2 hari 1 malam, 3 hari 2 malam. Paket custom, keluarga, honeymoon & gathering.' },
    { name: 'twitter:image', content: 'https://images.unsplash.com/photo-1630214801769-24784bfd2b9c?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D' }
  ],
  link: [
    { rel: 'canonical', href: 'https://www.jogjaliburan.com/paket-wisata-jogja/' }
  ],
  script: [
    {
      type: 'application/ld+json',
      children: JSON.stringify(schemaData)
    }
  ]
})
</script>