<template>
  <div>
    <main>
      <HeroSection />
        <FeaturesSection />
       <ServicesSection />
      <TestimonialsSection />
      <BlogCardsSection />
      <NewsletterSection />
    </main>
  </div>
</template>

<script setup>
import ServicesSection from '~/components/ServicesSection.vue'

// Comprehensive Homepage Schema Data
const homepageSchema = {
  "@context": "https://schema.org",
  "@graph": [
    // Organization Schema
    {
      "@type": ["Organization", "TravelAgency"],
      "@id": "https://www.jogjaliburan.com/#organization",
      name: "3J Tour - Jogja Jalan Jalan",
      alternateName: ["3J Tour", "liburan jogja", "3J Tour Yogyakarta"],
      url: "https://www.jogjaliburan.com/",
      logo: {
        "@type": "ImageObject",
        url: "https://www.jogjaliburan.com/3j-tour-logo.webp",
        width: 300,
        height: 100,
        caption: "3J Tour Logo"
      },
      image: "https://www.jogjaliburan.com/images/jogja-hero.webp",
      description: "3J Tour adalah penyedia layanan liburan terpercaya di Yogyakarta yang menawarkan paket wisata lengkap, sewa mobil, paket honeymoon, dan gathering perusahaan dengan pelayanan profesional dan harga terjangkau.",
      
      contactPoint: [
        {
          "@type": "ContactPoint",
          telephone: "+62-812-3456-7890",
          contactType: "customer service",
          areaServed: "ID",
          availableLanguage: ["Indonesian", "English"],
          hoursAvailable: {
            "@type": "OpeningHoursSpecification",
            dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
            opens: "08:00",
            closes: "21:00"
          }
        }
      ],
      
      address: {
        "@type": "PostalAddress",
        addressLocality: "Yogyakarta",
        addressRegion: "DI Yogyakarta",
        addressCountry: "ID",
        postalCode: "55000"
      },
      
      geo: {
        "@type": "GeoCoordinates",
        latitude: -7.7956,
        longitude: 110.3695
      },
      
      areaServed: [
        { "@type": "State", name: "DI Yogyakarta" },
        { "@type": "City", name: "Yogyakarta" },
        { "@type": "City", name: "Sleman" },
        { "@type": "City", name: "Bantul" },
        { "@type": "City", name: "Kulon Progo" },
        { "@type": "City", name: "Gunung Kidul" }
      ],
      
      sameAs: [
        "https://www.instagram.com/3jtour",
        "https://www.facebook.com/3jtour",
        "https://wa.me/6285186888837",
        "https://www.youtube.com/@3jtour"
      ],
      
      hasOfferCatalog: {
        "@type": "OfferCatalog",
        name: "3J Tour Services",
        itemListElement: [
          {
            "@type": "Offer",
            itemOffered: {
              "@type": "Service",
              name: "Paket Wisata Jogja",
              url: "https://www.jogjaliburan.com/paket-wisata-jogja/"
            }
          },
          {
            "@type": "Offer",
            itemOffered: {
              "@type": "Service",
              name: "Sewa Mobil Jogja",
              url: "https://www.jogjaliburan.com/sewa-mobil-jogja/"
            }
          },
          {
            "@type": "Offer",
            itemOffered: {
              "@type": "Service",
              name: "Paket Honeymoon",
              url: "https://www.jogjaliburan.com/paket-honeymoon/"
            }
          },
          {
            "@type": "Offer",
            itemOffered: {
              "@type": "Service",
              name: "Paket Gathering",
              url: "https://www.jogjaliburan.com/paket-gathering/"
            }
          }
        ]
      },
      
      knowsAbout: [
        "Wisata Yogyakarta", "Candi Borobudur", "Candi Prambanan",
        "Malioboro Street", "Pantai Parangtritis", "Rental Mobil",
        "Tour Guide", "Paket Honeymoon", "Team Building", "Corporate Gathering"
      ]
    },

    // Website Schema
    {
      "@type": "WebSite",
      "@id": "https://www.jogjaliburan.com/#website",
      url: "https://www.jogjaliburan.com/",
      name: "3J Tour - Jogja Liburan",
      alternateName: "Jogja Liburan",
      description: "Website resmi 3J Tour untuk booking paket wisata Jogja, sewa mobil, honeymoon, dan gathering perusahaan",
      publisher: {
        "@id": "https://www.jogjaliburan.com/#organization"
      },
      potentialAction: {
        "@type": "SearchAction",
        target: "https://www.jogjaliburan.com/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      },
      mainEntity: {
        "@id": "https://www.jogjaliburan.com/#organization"
      }
    },

    // LocalBusiness Schema
    {
      "@type": "LocalBusiness",
      "@id": "https://www.jogjaliburan.com/#localbusiness",
      name: "3J Tour - Travel Agent Yogyakarta",
      image: "https://www.jogjaliburan.com/images/jogja-hero.webp",
      telephone: "+62-812-3456-7890",
      email: "<EMAIL>",
      address: {
        "@type": "PostalAddress",
        addressLocality: "Yogyakarta",
        addressRegion: "DI Yogyakarta",
        addressCountry: "ID"
      },
      geo: {
        "@type": "GeoCoordinates",
        latitude: -7.7956,
        longitude: 110.3695
      },
      url: "https://www.jogjaliburan.com/",
      openingHoursSpecification: [
        {
          "@type": "OpeningHoursSpecification",
          dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
          opens: "08:00",
          closes: "17:00"
        },
        {
          "@type": "OpeningHoursSpecification",
          dayOfWeek: "Sunday",
          opens: "09:00",
          closes: "15:00"
        }
      ],
      priceRange: "IDR 190,000 - IDR 2,000,000",
      aggregateRating: {
        "@type": "AggregateRating",
        ratingValue: 4.8,
        reviewCount: 324,
        bestRating: 5,
        worstRating: 1
      },
      paymentAccepted: ["Cash", "Credit Card", "Bank Transfer", "E-Wallet"],
      currenciesAccepted: "IDR"
    },

    // Service Schema - Paket Wisata
    {
      "@type": "Service",
      "@id": "https://www.jogjaliburan.com/paket-wisata-jogja/#service",
      name: "Paket Wisata Jogja",
      description: "Paket tour lengkap ke destinasi terbaik Yogyakarta dengan guide profesional, transportasi nyaman, dan itinerary yang fleksibel",
      provider: {
        "@id": "https://www.jogjaliburan.com/#organization"
      },
      areaServed: {
        "@type": "State",
        name: "DI Yogyakarta"
      },
      hasOfferCatalog: {
        "@type": "OfferCatalog",
        name: "Paket Wisata Options",
        itemListElement: [
          {
            "@type": "Offer",
            name: "Paket Wisata 1 Hari",
            price: "350000",
            priceCurrency: "IDR"
          },
          {
            "@type": "Offer",
            name: "Paket Wisata 2 Hari 1 Malam",
            price: "650000",
            priceCurrency: "IDR"
          },
          {
            "@type": "Offer",
            name: "Paket Wisata 3 Hari 2 Malam",
            price: "950000",
            priceCurrency: "IDR"
          }
        ]
      },
      url: "https://www.jogjaliburan.com/paket-wisata-jogja/"
    },

    // Service Schema - Sewa Mobil
    {
      "@type": "Service",
      "@id": "https://www.jogjaliburan.com/sewa-mobil-jogja/#service",
      name: "Sewa Mobil Jogja",
      description: "Layanan rental mobil dengan pilihan lengkap kendaraan untuk kebutuhan wisata, bisnis, dan keperluan harian dengan harga terjangkau",
      provider: {
        "@id": "https://www.jogjaliburan.com/#organization"
      },
      areaServed: {
        "@type": "State",
        name: "DI Yogyakarta"
      },
      hasOfferCatalog: {
        "@type": "OfferCatalog",
        name: "Rental Car Options",
        itemListElement: [
          {
            "@type": "Offer",
            name: "City Car Rental",
            price: "190000",
            priceCurrency: "IDR"
          },
          {
            "@type": "Offer",
            name: "MPV Rental",
            price: "250000",
            priceCurrency: "IDR"
          },
          {
            "@type": "Offer",
            name: "Minibus Rental",
            price: "500000",
            priceCurrency: "IDR"
          }
        ]
      },
      url: "https://www.jogjaliburan.com/sewa-mobil-jogja/"
    },

    // Service Schema - Honeymoon
    {
      "@type": "Service",
      "@id": "https://www.jogjaliburan.com/paket-honeymoon/#service",
      name: "Paket Honeymoon Jogja",
      description: "Paket bulan madu romantis di Yogyakarta dengan akomodasi mewah, spa couple, candle light dinner, dan destinasi eksotis untuk pasangan",
      provider: {
        "@id": "https://www.jogjaliburan.com/#organization"
      },
      areaServed: {
        "@type": "State",
        name: "DI Yogyakarta"
      },
      hasOfferCatalog: {
        "@type": "OfferCatalog",
        name: "Honeymoon Packages",
        itemListElement: [
          {
            "@type": "Offer",
            name: "Honeymoon Package Romantic",
            price: "1200000",
            priceCurrency: "IDR"
          },
          {
            "@type": "Offer",
            name: "Honeymoon Package Premium",
            price: "1800000",
            priceCurrency: "IDR"
          }
        ]
      },
      url: "https://www.jogjaliburan.com/paket-honeymoon/"
    },

    // Service Schema - Gathering
    {
      "@type": "Service",
      "@id": "https://www.jogjaliburan.com/paket-gathering/#service",
      name: "Paket Gathering Perusahaan",
      description: "Program gathering perusahaan dengan team building, leadership training, dan wisata edukatif untuk meningkatkan kekompakan tim",
      provider: {
        "@id": "https://www.jogjaliburan.com/#organization"
      },
      areaServed: {
        "@type": "State",
        name: "DI Yogyakarta"
      },
      hasOfferCatalog: {
        "@type": "OfferCatalog",
        name: "Corporate Gathering Packages",
        itemListElement: [
          {
            "@type": "Offer",
            name: "Team Building Package",
            price: "450000",
            priceCurrency: "IDR"
          },
          {
            "@type": "Offer",
            name: "Leadership Training Package",
            price: "650000",
            priceCurrency: "IDR"
          }
        ]
      },
      url: "https://www.jogjaliburan.com/paket-gathering/"
    },

    // Breadcrumb Schema
    {
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Home",
          item: "https://www.jogjaliburan.com/"
        }
      ]
    },

    // FAQ Schema
    {
      "@type": "FAQPage",
      mainEntity: [
        {
          "@type": "Question",
          name: "Apa saja layanan yang disediakan 3J Tour?",
          acceptedAnswer: {
            "@type": "Answer",
            text: "3J Tour menyediakan layanan lengkap untuk liburan di Yogyakarta: paket wisata 1-4 hari, sewa mobil dengan/tanpa sopir, paket honeymoon romantis, dan paket gathering perusahaan dengan program team building."
          }
        },
        {
          "@type": "Question",
          name: "Berapa harga paket wisata Jogja dari 3J Tour?",
          acceptedAnswer: {
            "@type": "Answer",
            text: "Harga paket wisata mulai dari Rp 350.000/orang untuk paket 1 hari. Paket 2D1N mulai Rp 650.000, paket 3D2N mulai Rp 950.000, dan paket 4D3N mulai Rp 1.250.000. Harga sudah termasuk transportasi, guide, dan tiket masuk destinasi utama."
          }
        },
        {
          "@type": "Question",
          name: "Apakah 3J Tour menyediakan layanan sewa mobil?",
          acceptedAnswer: {
            "@type": "Answer",
            text: "Ya, 3J Tour menyediakan layanan sewa mobil lengkap dengan pilihan city car, MPV, dan minibus. Harga mulai Rp 190.000/12jam. Tersedia opsi dengan sopir atau lepas kunci sesuai kebutuhan."
          }
        },
        {
          "@type": "Question",
          name: "Bagaimana cara booking paket wisata di 3J Tour?",
          acceptedAnswer: {
            "@type": "Answer",
            text: "Booking sangat mudah! Anda bisa menghubungi kami via WhatsApp di + 62 8518 6888 837, kunjungi website jogjaliburan.com, atau datang langsung ke kantor kami. Tim customer service siap membantu 24/7."
          }
        },
        {
          "@type": "Question",
          name: "Apakah 3J Tour melayani paket honeymoon dan gathering perusahaan?",
          acceptedAnswer: {
            "@type": "Answer",
            text: "Ya, kami menyediakan paket honeymoon romantis dengan akomodasi mewah dan destinasi eksotis, serta paket gathering perusahaan dengan program team building dan leadership training yang terstruktur."
          }
        },
        {
          "@type": "Question",
          name: "Apa keunggulan memilih 3J Tour sebagai travel agent?",
          acceptedAnswer: {
            "@type": "Answer",
            text: "3J Tour memiliki pengalaman bertahun-tahun, guide profesional, armada terawat, harga kompetitif, layanan 24/7, dan sudah dipercaya ribuan wisatawan. Kami berkomitmen memberikan pengalaman liburan terbaik di Yogyakarta."
          }
        }
      ]
    }
  ]
}

// SEO metadata with comprehensive schema
useHead({
  title: 'Liburan Jogja Murah & Terpercaya - 3J Tour',
  meta: [
    { name: 'description', content: 'Solusi liburan di Jogja yang nyaman dengan berbagai paket layanan untuk mempermudah aktivitas liburan Anda.' },
    { name: 'keywords', content: '3j tour, liburan jogja, paket liburan jogja'},
    // Open Graph / Facebook
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: 'https://www.jogjaliburan.com/' },
    { property: 'og:title', content: 'Liburan Jogja Murah & Terpercaya - 3J Tour' },
    { property: 'og:description', content: 'Solusi liburan di Jogja yang nyaman dengan berbagai paket layanan untuk mempermudah aktivitas liburan Anda.' },
    { property: 'og:image', content: 'https://www.jogjaliburan.com/images/jogja-hero.webp' },
    { property: 'og:site_name', content: '3J Tour - Jogja Liburan' },
    // Twitter
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:url', content: 'https://www.jogjaliburan.com/' },
    { name: 'twitter:title', content: 'Liburan Jogja Murah & Terpercaya - 3J Tour' },
    { name: 'twitter:description', content: 'Solusi liburan di Jogja yang nyaman dengan berbagai paket layanan untuk mempermudah aktivitas liburan Anda.' },
    { name: 'twitter:image', content: 'https://www.jogjaliburan.com/images/jogja-hero.webp' },
    // Additional SEO
    { name: 'robots', content: 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1' },
    { name: 'googlebot', content: 'index, follow' },
    { name: 'author', content: '3J Tour' },
    { name: 'geo.region', content: 'ID-YO' },
    { name: 'geo.placename', content: 'Yogyakarta' },
    { name: 'geo.position', content: '-7.7956;110.3695' },
    { name: 'ICBM', content: '-7.7956, 110.3695' }
  ],
  link: [
    { rel: 'canonical', href: 'https://www.jogjaliburan.com/' }
  ],
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify(homepageSchema)
    }
  ]
})
</script>