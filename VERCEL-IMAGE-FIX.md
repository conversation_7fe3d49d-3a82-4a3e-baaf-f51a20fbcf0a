# Vercel Image Optimization Fix untuk BunnyCDN

## Masalah yang Terjadi

Ketika deploy ke Vercel, gambar BunnyCDN tidak muncul karena Nuxt Image mencoba mengoptimasi gambar melalui Vercel Image Optimization:

```html
<!-- URL Asli -->
https://liburanjogja.b-cdn.net/Stupa_Borobudur.webp

<!-- URL Vercel (Salah) -->
/_vercel/image?url=%2Ftampilan-panorama-candi-borobudur.webp&w=1536&q=100
```

## Solusi yang Diterapkan

### 1. **Konfigurasi Nuxt Image**
File: `nuxt.config.ts`
```js
image: {
    domains: ['liburanjogja.b-cdn.net', 'www.jogjaliburan.com', 'jogjaliburan.com'],
    // Disable Vercel Image Optimization untuk production
    ...(process.env.VERCEL ? {
        provider: 'none'
    } : {}),
}
```

### 2. **Komponen BlogImage.vue**
- Menggunakan raw `<img>` tag
- Bypass Nuxt Image processing
- Force BunnyCDN URL untuk semua gambar

### 3. **Komponen DirectImg.vue** (Alternatif)
- Komponen khusus untuk bypass total Nuxt Image
- Dapat digunakan dengan syntax: `::DirectImg{src="https://liburanjogja.b-cdn.net/image.webp" alt="Description"}`

## Format URL yang Direkomendasikan

### ✅ **Gunakan URL Absolut BunnyCDN:**
```markdown
![Alt text](https://liburanjogja.b-cdn.net/nama-file.webp)
```

### ❌ **Hindari URL Relatif di Production:**
```markdown
![Alt text](/nama-file.webp)
```

## Testing

### **Development (localhost:3000):**
- URL relatif berfungsi normal
- URL absolut berfungsi normal

### **Production (Vercel):**
- Hanya URL absolut BunnyCDN yang berfungsi dengan benar
- URL relatif akan diproses oleh Vercel Image Optimization

## Alternatif Syntax MDC

Jika masih ada masalah, gunakan komponen DirectImg:

```markdown
::DirectImg{src="https://liburanjogja.b-cdn.net/image.webp" alt="Description"}
::
```

## Environment Variables

Untuk debugging, tambahkan log di production:

```bash
VERCEL=1 # Otomatis ada di Vercel
NODE_ENV=production
```

## Best Practice

1. **Gunakan URL absolut BunnyCDN** untuk semua gambar content
2. **Test di production** sebelum publish artikel baru
3. **Check browser console** untuk error loading gambar
4. **Pastikan nama file** sesuai antara markdown dan BunnyCDN

---

**Status:** ✅ Fixed - Gambar BunnyCDN sekarang bypass Vercel Image Optimization 