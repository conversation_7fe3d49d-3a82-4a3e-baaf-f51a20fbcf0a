<!-- pages/blog/index.vue -->
<template>
  <Head>
    <link rel="canonical" href="https://www.jogjaliburan.com/blog/" />
  </Head>

  <main class="py-20 bg-gradient-to-br from-orange-50 via-yellow-50 to-green-50 dark:bg-gray-800 px-5">
    <!-- Header Blog -->
    <div class="container mx-auto">
      <h1 class="max-w-3xl mx-auto text-4xl md:text-4xl font-bold text-gray-900 mb-6 text-center">
        Blog <span class="text-orange-500">Jogja</span><span class="text-yellow-500"> Liburan</span>
      </h1>
      <p class="max-w-3xl mx-auto text-lg text-gray-700 mb-10 text-center">
        Panduan wisata dan tips liburan terbaik untuk menjelajahi <span class="font-bold text-orange-600">keindahan Yogyakarta</span> dan <span class="font-bold text-green-600">destinasi sekitarnya</span>
      </p>
      
      <!-- Search Bar -->
      <div class="max-w-xl mx-auto mb-12">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg class="w-4 h-4 text-orange-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
            </svg>
          </div>
          <input 
            type="search" 
            v-model="searchQuery" 
            class="block w-full p-4 pl-10 text-sm text-gray-900 border border-orange-200 rounded-lg bg-white focus:ring-orange-500 focus:border-orange-500 shadow-sm" 
            placeholder="Cari destinasi wisata, kuliner, atau tips liburan..." 
            @input="handleSearch"
          >
          <button 
            v-if="searchQuery" 
            @click="clearSearch" 
            class="absolute inset-y-0 right-0 flex items-center pr-3 text-orange-500 hover:text-orange-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Link ke Tags -->
      <div class="text-center mb-12">
        <a href="/tags/" class="inline-flex items-center text-orange-600 hover:text-orange-700 font-medium bg-white px-4 py-2 rounded-full shadow-sm border border-orange-200 hover:shadow-md transition-all">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
          </svg>
          Lihat Semua Kategori Wisata
        </a>
      </div>
    </div>
    
    <!-- Loading State -->
    <div v-if="pending" class="text-center py-12">
      <p class="text-gray-600">Memuat artikel...</p>
    </div>
    
    <!-- Blog Posts Grid -->
    <div v-else-if="articles && articles.length" class="container mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Featured Post (First Post) -->
      <div v-if="!searchQuery && articles[0]" class="col-span-full mb-12">
        <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-xl overflow-hidden flex flex-col md:flex-row border-l-4 border-orange-500 relative">
          <!-- Decorative Elements - Jogja Theme -->
          <div class="absolute top-0 right-0 w-32 h-32 -mt-10 -mr-10 bg-gradient-to-br from-orange-400/20 to-yellow-400/20 rounded-full"></div>
          <div class="absolute bottom-0 left-0 w-24 h-24 -mb-8 -ml-8 bg-gradient-to-tr from-green-400/20 to-yellow-400/20 rounded-full"></div>
          <!-- Tambahan pattern tradisional -->
          <div class="absolute top-4 left-4 w-8 h-8 border-2 border-orange-300/30 rounded-md rotate-45"></div>
          <div class="absolute bottom-4 right-4 w-6 h-6 border-2 border-yellow-300/30 rounded-full"></div>
          
          <div class="md:w-1/2 flex items-center justify-center p-8 min-h-[360px] bg-gradient-to-br from-orange-50 via-yellow-50 to-green-50 relative">
            <!-- Icon wisata sebagai background -->
            <div class="absolute inset-0 flex items-center justify-center opacity-5">
              <svg class="w-32 h-32 text-orange-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14,2A8,8 0 0,0 6,10A8,8 0 0,0 14,18A8,8 0 0,0 22,10H20C20,13.32 17.32,16 14,16A6,6 0 0,1 8,10A6,6 0 0,1 14,4C14.34,4 14.67,4.04 15,4.1V2.1C14.67,2.04 14.34,2 14,2M14,6A4,4 0 0,0 10,10A4,4 0 0,0 14,14A4,4 0 0,0 18,10A4,4 0 0,0 14,6M14,8A2,2 0 0,1 16,10A2,2 0 0,1 14,12A2,2 0 0,1 12,10A2,2 0 0,1 14,8Z"/>
              </svg>
            </div>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white text-center leading-tight relative z-10">
              {{ articles[0].title }}
            </h2>
          </div>
          <div class="md:w-1/2 p-8 flex flex-col bg-gradient-to-br from-white to-orange-50">
            <div v-if="articles[0].badge" class="mb-3">
              <span class="bg-gradient-to-r from-orange-500 to-yellow-500 text-white text-xs font-medium px-3 py-1 rounded-full shadow-sm">
                🏛️ {{ articles[0].badge.label }}
              </span>
            </div>
            <p class="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">{{ articles[0].description }}</p>
            <div class="flex items-center mt-auto text-sm">
              <div v-if="articles[0].authors && articles[0].authors.length" class="flex items-center">
                <span class="text-gray-700 font-medium">👤 {{ articles[0].authors[0].name }}</span>
              </div>
              <span class="mx-2 text-orange-300">•</span>
              <time class="text-gray-600 flex items-center" :datetime="articles[0].date">
                📅 {{ formatDate(articles[0].date) }}
              </time>
            </div>
            <div class="mt-6">
              <NuxtLink 
                :to="getPostUrl(articles[0])" 
                class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-500 to-yellow-500 text-white rounded-xl hover:from-orange-600 hover:to-yellow-600 transition-all shadow-lg hover:shadow-xl font-medium"
              >
                🎯 Baca Panduan Lengkap
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                </svg>
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Search Results -->
      <div v-if="searchQuery && filteredArticles.length > 0" class="col-span-full mb-6">
        <p class="text-gray-700 mb-8 text-center bg-white px-6 py-3 rounded-xl shadow-sm border border-orange-200">📍 Menampilkan <span class="font-bold text-orange-600">{{ filteredArticles.length }}</span> destinasi untuk "<span class="text-orange-700 font-semibold">{{ searchQuery }}</span>"</p>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div 
            v-for="(article, index) in filteredArticles" 
            :key="article._path || index" 
            class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg hover:shadow-xl overflow-hidden flex flex-col h-full border-l-4 border-orange-400 relative group transition-all hover:-translate-y-1"
          >
            <!-- Decorative elements -->
            <div class="absolute top-0 right-0 w-16 h-16 -mt-5 -mr-5 bg-gradient-to-br from-orange-400/20 to-yellow-400/20 rounded-full group-hover:from-orange-400/30 group-hover:to-yellow-400/30 transition-all"></div>
            <div class="absolute bottom-0 left-0 w-12 h-12 -mb-4 -ml-4 bg-gradient-to-tr from-green-400/20 to-yellow-400/20 rounded-full group-hover:from-green-400/30 group-hover:to-yellow-400/30 transition-all"></div>
            
            <div class="bg-gradient-to-br from-orange-50 via-yellow-50 to-green-50 p-6 flex items-center justify-center min-h-[200px] relative">
              <!-- Icon wisata mini -->
              <div class="absolute top-2 left-2 text-orange-400/30 text-2xl">🏛️</div>
              <h2 class="text-2xl font-bold text-gray-800 dark:text-white text-center leading-tight">
                {{ article.title }}
              </h2>
            </div>
            <div class="p-6 flex-grow bg-gradient-to-b from-white to-orange-50/30">
              <div v-if="article.badge" class="mb-3">
                <span class="bg-gradient-to-r from-orange-400 to-yellow-400 text-white text-xs font-medium px-3 py-1 rounded-full shadow-sm">
                  🎯 {{ article.badge.label }}
                </span>
              </div>
              <p class="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">{{ article.description }}</p>
              <div class="flex items-center mt-auto text-sm">
                <div v-if="article.authors && article.authors.length" class="flex items-center">
                  <span class="text-gray-700 font-medium">👤 {{ article.authors[0].name }}</span>
                </div>
                <span class="mx-2 text-orange-300">•</span>
                <time class="text-gray-600 flex items-center" :datetime="article.date">
                  📅 {{ formatDate(article.date) }}
                </time>
              </div>
              <div class="mt-6">
                <NuxtLink 
                  :to="getPostUrl(article)" 
                  class="inline-flex items-center px-4 py-2 border-2 border-orange-300 text-orange-700 hover:bg-orange-500 hover:text-white rounded-xl font-medium transition-all hover:shadow-lg"
                >
                  🧭 Jelajahi Destinasi
                  <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                  </svg>
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Regular Posts Grid -->
      <template v-else-if="!searchQuery">
        <div 
          v-for="(article, index) in articles.slice(1)" 
          :key="article._path || index" 
          class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg hover:shadow-xl overflow-hidden flex flex-col h-full border-l-4 border-orange-400 relative group transition-all hover:-translate-y-1"
        >
          <!-- Decorative elements dengan variasi -->
          <div class="absolute top-0 right-0 w-16 h-16 -mt-5 -mr-5 bg-gradient-to-br from-orange-400/20 to-yellow-400/20 rounded-full group-hover:from-orange-400/30 group-hover:to-yellow-400/30 transition-all"></div>
          <div class="absolute bottom-0 left-0 w-12 h-12 -mb-4 -ml-4 bg-gradient-to-tr from-green-400/20 to-yellow-400/20 rounded-full group-hover:from-green-400/30 group-hover:to-yellow-400/30 transition-all"></div>
          
          <!-- Pattern traditional Jogja -->
          <div class="absolute top-3 left-3 w-4 h-4 border border-orange-300/40 rounded-sm rotate-45 group-hover:border-orange-400/60 transition-all"></div>
          
          <div class="bg-gradient-to-br from-orange-50 via-yellow-50 to-green-50 p-6 flex items-center justify-center min-h-[200px] relative">
            <!-- Icon dinamis berdasarkan index -->
            <div class="absolute top-2 left-2 text-orange-400/30 text-2xl">
              {{ index % 4 === 0 ? '🏛️' : index % 4 === 1 ? '🍽️' : index % 4 === 2 ? '🏖️' : '🗻' }}
            </div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white text-center leading-tight">
              {{ article.title }}
            </h2>
          </div>
          <div class="p-6 flex-grow bg-gradient-to-b from-white to-orange-50/30">
            <div v-if="article.badge" class="mb-3">
              <span class="bg-gradient-to-r from-orange-400 to-yellow-400 text-white text-xs font-medium px-3 py-1 rounded-full shadow-sm">
                🎯 {{ article.badge.label }}
              </span>
            </div>
            <p class="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">{{ article.description }}</p>
            <div class="flex items-center mt-auto text-sm">
              <div v-if="article.authors && article.authors.length" class="flex items-center">
                <span class="text-gray-700 font-medium">👤 {{ article.authors[0].name }}</span>
              </div>
              <span class="mx-2 text-orange-300">•</span>
              <time class="text-gray-600 flex items-center" :datetime="article.date">
                📅 {{ formatDate(article.date) }}
              </time>
            </div>
            <div class="mt-6">
              <NuxtLink 
                :to="getPostUrl(article)" 
                class="inline-flex items-center px-4 py-2 border-2 border-orange-300 text-orange-700 hover:bg-orange-500 hover:text-white rounded-xl font-medium transition-all hover:shadow-lg"
              >
                🧭 Jelajahi Destinasi
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                </svg>
              </NuxtLink>
            </div>
          </div>
        </div>
      </template>
    </div>
    
    <!-- No Search Results -->
    <div v-else-if="searchQuery && filteredArticles.length === 0" class="container mx-auto text-center py-12 bg-gradient-to-br from-orange-50 to-yellow-50 rounded-2xl max-w-3xl border border-orange-200 shadow-lg">
      <div class="text-6xl mb-4">🔍</div>
      <p class="text-gray-700 mb-4 text-lg">Maaf, tidak ada destinasi yang ditemukan untuk "<span class="font-bold text-orange-600">{{ searchQuery }}</span>"</p>
      <p class="text-gray-600 mb-6">Coba kata kunci lain seperti "candi", "pantai", "kuliner", atau "gunung"</p>
      <button 
        @click="clearSearch" 
        class="px-6 py-3 bg-gradient-to-r from-orange-500 to-yellow-500 text-white rounded-xl hover:from-orange-600 hover:to-yellow-600 transition-all shadow-lg hover:shadow-xl font-medium"
      >
        🏠 Kembali ke Semua Artikel
      </button>
    </div>
    
    <!-- No Articles -->
    <div v-else class="container mx-auto text-center py-12 bg-gradient-to-br from-orange-50 to-yellow-50 rounded-2xl max-w-3xl border border-orange-200 shadow-lg">
      <div class="text-6xl mb-4">📝</div>
      <p class="text-gray-700 text-lg">Artikel wisata sedang dalam persiapan...</p>
      <p class="text-gray-600 mt-2">Segera hadir panduan lengkap destinasi wisata Jogja terbaik!</p>
    </div>
  </main>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// State untuk artikel dan pencarian
const articles = ref([])
const pending = ref(true)
const error = ref(null)
const searchQuery = ref('')
const searchTimeout = ref(null)

// Fungsi untuk mengambil artikel
async function fetchArticles() {
  pending.value = true
  error.value = null
  
  try {
    // Menggunakan queryCollection untuk mengambil semua artikel
    const allPosts = await queryCollection('posts').all()
    console.log('Hasil queryCollection("posts").all():', allPosts)
    
    if (allPosts && allPosts.length > 0) {
      // Urutkan berdasarkan tanggal terbaru
      articles.value = allPosts.sort((a, b) => {
        const dateA = new Date(a.date)
        const dateB = new Date(b.date)
        return dateB - dateA
      })
      return
    }
    
    // Jika tidak ada artikel, gunakan data fallback
    console.log('Tidak ada artikel ditemukan, menggunakan data fallback')
    articles.value = getFallbackArticles()
  } catch (err) {
    console.error('Error saat mengambil artikel blog:', err)
    error.value = err
    articles.value = getFallbackArticles()
  } finally {
    pending.value = false
  }
}

// Data fallback jika tidak ada artikel yang ditemukan
function getFallbackArticles() {
  return [
    {
      _path: '/blog/strategi-seo-terbaru',
      title: 'Strategi SEO Terbaru untuk Meningkatkan Peringkat Website di 2025',
      description: 'Pelajari strategi SEO terbaru yang efektif untuk meningkatkan peringkat website Anda di mesin pencari pada tahun 2025.',
      date: '2025-04-19',
      image: { src: '/blog/images/strategi-seo.webp' },
      authors: [{ name: 'Roofel Team', avatar: { src: '/images/avatar.webp' } }],
      badge: { label: 'SEO' },
      tags: ['SEO', 'Digital Marketing', 'Website', 'Google', 'Optimasi']
    }
  ]
}

// Filter artikel berdasarkan pencarian
const filteredArticles = computed(() => {
  if (!articles.value || !articles.value.length) return []
  if (!searchQuery.value) return articles.value
  
  const query = searchQuery.value.toLowerCase()
  return articles.value.filter(article => {
    // Cari di judul
    if (article.title && article.title.toLowerCase().includes(query)) return true
    // Cari di deskripsi
    if (article.description && article.description.toLowerCase().includes(query)) return true
    // Cari di tag
    if (article.tags && article.tags.some(tag => tag.toLowerCase().includes(query))) return true
    // Cari di badge
    if (article.badge && article.badge.label && article.badge.label.toLowerCase().includes(query)) return true
    
    return false
  })
})

// Fungsi untuk menangani pencarian dengan debounce
function handleSearch() {
  // Clear previous timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
  
  // Set new timeout for debounce
  searchTimeout.value = setTimeout(() => {
    console.log('Searching for:', searchQuery.value)
  }, 300)
}

// Fungsi untuk menghapus pencarian
function clearSearch() {
  searchQuery.value = ''
}

// Format tanggal ke format yang lebih mudah dibaca
function formatDate(dateString) {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('id-ID', { year: 'numeric', month: 'long', day: 'numeric' })
}

// Helper function untuk mendapatkan URL post (dari [tag].vue)
function getPostUrl(post) {
  // Coba semua kemungkinan properti path
  if (post.path) return post.path.endsWith('/') ? post.path : `${post.path}/`
  if (post._path) return post._path.endsWith('/') ? post._path : `${post._path}/`
  
  // Fallback ke slug dari _path atau _file
  const slug = post._path?.split('/').pop() || 
               post._file?.split('/').pop()?.replace('.md', '') ||
               post._id?.split(':').pop()
  
  return `/${slug}/`
}

// Fungsi untuk mendapatkan gambar dari artikel
function getImageSrc(article) {
  // Jika artikel memiliki properti image dengan src, gunakan itu
  if (article.image && article.image.src) {
    return article.image.src
  }
  
  // Jika artikel memiliki properti cover, gunakan itu
  if (article.cover) {
    return article.cover
  }
  
  // Jika artikel memiliki properti img, gunakan itu
  if (article.img) {
    return article.img
  }
  
  // Jika artikel memiliki properti thumbnail, gunakan itu
  if (article.thumbnail) {
    return article.thumbnail
  }
  
  // Jika artikel memiliki properti featuredImage, gunakan itu
  if (article.featuredImage) {
    return article.featuredImage
  }
  
  // Jika artikel memiliki properti hero, gunakan itu
  if (article.hero) {
    return article.hero
  }
  
  // Fallback ke gambar default
  return '/blog/images/default-cover.webp'
}

// Panggil fungsi fetchArticles saat komponen dimount
onMounted(() => {
  fetchArticles()
})

// Enhanced SEO metadata untuk Blog Index
const baseUrl = 'https://www.jogjaliburan.com'
const currentUrl = `${baseUrl}/blog/`
const title = 'Blog Jogja Liburan - Panduan Wisata Terlengkap Yogyakarta'
const description = 'Temukan panduan wisata lengkap, tips liburan, rekomendasi destinasi terbaik, kuliner khas, dan informasi travel Yogyakarta dan sekitarnya'
const imageUrl = `${baseUrl}/images/blog-og.jpg`

// Structured data untuk Blog Index
const blogSchema = {
  '@context': 'https://schema.org',
  '@graph': [
    {
      '@type': 'Organization',
      '@id': `${baseUrl}/#organization`,
      'name': '3J Tour - Jogja Liburan',
      'description': 'Travel agency terpercaya untuk wisata Yogyakarta dan sekitarnya',
      'url': baseUrl,
      'logo': {
        '@type': 'ImageObject',
        'url': `${baseUrl}/3j-tour-logo.webp`,
        'width': 300,
        'height': 100
      },
      'contactPoint': {
        '@type': 'ContactPoint',
        'telephone': '+62-274-123456',
        'contactType': 'Customer Service',
        'areaServed': 'ID',
        'availableLanguage': ['Indonesian', 'English']
      },
      'address': {
        '@type': 'PostalAddress',
        'addressLocality': 'Yogyakarta',
        'addressRegion': 'Daerah Istimewa Yogyakarta',
        'addressCountry': 'ID'
      },
      'sameAs': [
        'https://www.facebook.com/jogjaliburan',
        'https://www.instagram.com/jogjaliburan',
        'https://wa.me/6285186888837'
      ]
    },
    {
      '@type': 'WebSite',
      '@id': `${baseUrl}/#website`,
      'name': '3J Tour - Jogja Liburan',
      'description': 'Portal wisata terlengkap untuk liburan di Yogyakarta',
      'url': baseUrl,
      'publisher': {
        '@id': `${baseUrl}/#organization`
      },
      'inLanguage': 'id-ID',
      'potentialAction': {
        '@type': 'SearchAction',
        'target': {
          '@type': 'EntryPoint',
          'urlTemplate': `${currentUrl}?q={search_term_string}`
        },
        'query-input': 'required name=search_term_string'
      }
    },
    {
      '@type': 'WebPage',
      '@id': currentUrl,
      'name': title,
      'description': description,
      'url': currentUrl,
      'inLanguage': 'id-ID',
      'isPartOf': {
        '@id': `${baseUrl}/#website`
      },
      'breadcrumb': {
        '@type': 'BreadcrumbList',
        'itemListElement': [
          {
            '@type': 'ListItem',
            'position': 1,
            'name': 'Beranda',
            'item': baseUrl
          },
          {
            '@type': 'ListItem',
            'position': 2,
            'name': 'Blog',
            'item': currentUrl
          }
        ]
      }
    },
    {
      '@type': 'Blog',
      '@id': `${currentUrl}#blog`,
      'name': 'Blog Jogja Liburan',
      'description': description,
      'url': currentUrl,
      'inLanguage': 'id-ID',
      'publisher': {
        '@id': `${baseUrl}/#organization`
      },
      'mainEntityOfPage': {
        '@id': currentUrl
      },
      'about': {
        '@type': 'Thing',
        'name': 'Wisata Yogyakarta',
        'description': 'Panduan lengkap wisata dan liburan di Yogyakarta'
      }
    }
  ]
}

useSeoMeta({
  title: title,
  description: description,
  keywords: 'blog jogja, panduan wisata yogyakarta, destinasi jogja, kuliner jogja, travel guide yogyakarta, liburan jogja, wisata budaya jogja',
  author: '3J Tour Team',
  robots: 'index, follow, max-image-preview:large',
  
  // Open Graph
  ogTitle: title,
  ogDescription: description,
  ogType: 'website',
  ogUrl: currentUrl,
  ogImage: imageUrl,
  ogImageWidth: 1200,
  ogImageHeight: 630,
  ogImageAlt: 'Blog Jogja Liburan - Panduan Wisata Yogyakarta',
  ogSiteName: '3J Tour - Jogja Liburan',
  ogLocale: 'id_ID',
  
  // Twitter Card
  twitterCard: 'summary_large_image',
  twitterTitle: title,
  twitterDescription: description,
  twitterImage: imageUrl,
  twitterImageAlt: 'Blog Jogja Liburan - Panduan Wisata Yogyakarta',
  twitterSite: '@jogja_jalan_jalan',
  twitterCreator: '@jogja_jalan_jalan',
  
  // Additional meta
  themeColor: '#f97316',
  viewport: 'width=device-width, initial-scale=1, viewport-fit=cover'
})

useHead({
  link: [
    { rel: 'canonical', href: currentUrl },
    { rel: 'alternate', hreflang: 'id', href: currentUrl },
    { rel: 'alternate', hreflang: 'x-default', href: currentUrl }
  ],
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify(blogSchema)
    }
  ]
})
</script>

<style>
/* Jogja Liburan Theme - Warna hangat dan natural */

/* Orange Theme Colors */
.text-orange-500 {
  color: #f97316;
}

.text-orange-600 {
  color: #ea580c;
}

.text-orange-700 {
  color: #c2410c;
}

.bg-orange-500 {
  background-color: #f97316;
}

.bg-orange-600 {
  background-color: #ea580c;
}

.border-orange-200 {
  border-color: #fed7aa;
}

.border-orange-300 {
  border-color: #fdba74;
}

.border-orange-400 {
  border-color: #fb923c;
}

.border-orange-500 {
  border-color: #f97316;
}

/* Yellow Theme Colors */
.text-yellow-500 {
  color: #eab308;
}

.bg-yellow-500 {
  background-color: #eab308;
}

.bg-yellow-600 {
  background-color: #ca8a04;
}

/* Green Theme Colors */
.text-green-600 {
  color: #16a34a;
}

/* Focus states untuk form */
.focus\:ring-orange-500:focus {
  --tw-ring-color: #f97316;
}

.focus\:border-orange-500:focus {
  border-color: #f97316;
}

/* Hover effects */
.hover\:text-orange-700:hover {
  color: #c2410c;
}

/* Custom gradients untuk tema Jogja */
.from-orange-50 {
  --tw-gradient-from: #fff7ed;
}

.via-yellow-50 {
  --tw-gradient-via: #fefce8;
}

.to-green-50 {
  --tw-gradient-to: #f0fdf4;
}

.from-orange-500 {
  --tw-gradient-from: #f97316;
}

.to-yellow-500 {
  --tw-gradient-to: #eab308;
}

.from-orange-600 {
  --tw-gradient-from: #ea580c;
}

.to-yellow-600 {
  --tw-gradient-to: #ca8a04;
}

.from-orange-400\/20 {
  --tw-gradient-from: rgba(251, 146, 60, 0.2);
}

.to-yellow-400\/20 {
  --tw-gradient-to: rgba(250, 204, 21, 0.2);
}

.from-green-400\/20 {
  --tw-gradient-from: rgba(74, 222, 128, 0.2);
}

/* Animations dan transitions untuk UX yang smooth */
.group:hover .group-hover\:from-orange-400\/30 {
  --tw-gradient-from: rgba(251, 146, 60, 0.3);
}

.group:hover .group-hover\:to-yellow-400\/30 {
  --tw-gradient-to: rgba(250, 204, 21, 0.3);
}

.group:hover .group-hover\:from-green-400\/30 {
  --tw-gradient-from: rgba(74, 222, 128, 0.3);
}

/* Utility classes */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom shadow untuk efek depth */
.shadow-jogja {
  box-shadow: 0 10px 25px -3px rgba(249, 115, 22, 0.1), 0 4px 6px -2px rgba(249, 115, 22, 0.05);
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* Dark mode support tetap dipertahankan */
@media (prefers-color-scheme: dark) {
  .dark\:bg-gray-800 {
    background-color: #1f2937;
  }
  
  .dark\:bg-gray-900 {
    background-color: #111827;
  }
  
  .dark\:text-white {
    color: #ffffff;
  }
  
  .dark\:text-gray-300 {
    color: #d1d5db;
  }
}
</style>
