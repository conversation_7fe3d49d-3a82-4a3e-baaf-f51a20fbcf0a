module.exports = {
  content: [
    "./components/**/*.{js,vue,ts}",
    "./layouts/**/*.vue",
    "./pages/**/*.vue",
    "./plugins/**/*.{js,ts}",
    "./nuxt.config.{js,ts}",
    "./app.vue"
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#DC2626', // red-600
          dark: '#B91C1C',    // red-700
          light: '#EF4444'    // red-500
        },
        secondary: {
          DEFAULT: '#1A1A1A',
          light: '#333333'
        }
      },
      fontFamily: {
        sans: ['Poppins', 'sans-serif']
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '1.5rem'
      },
      typography: (theme) => ({
        DEFAULT: {
          css: {
            maxWidth: 'none',
            color: theme('colors.zinc.700'),
            lineHeight: '1.75',
            fontSize: '1.1rem',
            '--tw-prose-body': theme('colors.zinc.700'),
            '--tw-prose-headings': theme('colors.zinc.900'),
            '--tw-prose-lead': theme('colors.zinc.600'),
            '--tw-prose-links': theme('colors.red.600'),
            '--tw-prose-bold': theme('colors.zinc.900'),
            '--tw-prose-counters': theme('colors.zinc.500'),
            '--tw-prose-bullets': theme('colors.zinc.300'),
            '--tw-prose-hr': theme('colors.zinc.200'),
            '--tw-prose-quotes': theme('colors.zinc.900'),
            '--tw-prose-quote-borders': theme('colors.zinc.200'),
            '--tw-prose-captions': theme('colors.zinc.500'),
            '--tw-prose-code': theme('colors.zinc.900'),
            '--tw-prose-pre-code': theme('colors.zinc.200'),
            '--tw-prose-pre-bg': theme('colors.zinc.800'),
            '--tw-prose-th-borders': theme('colors.zinc.300'),
            '--tw-prose-td-borders': theme('colors.zinc.200'),
            h1: {
              fontSize: '2.5rem',
              fontWeight: '800',
              lineHeight: '1.2',
              marginTop: '0',
              marginBottom: '2rem',
              color: theme('colors.zinc.900'),
            },
            h2: {
              fontSize: '2rem',
              fontWeight: '700',
              lineHeight: '1.3',
              marginTop: '3rem',
              marginBottom: '1.5rem',
              color: theme('colors.zinc.900'),
              borderBottom: `2px solid ${theme('colors.red.100')}`,
              paddingBottom: '0.5rem',
            },
            h3: {
              fontSize: '1.5rem',
              fontWeight: '600',
              lineHeight: '1.4',
              marginTop: '2.5rem',
              marginBottom: '1rem',
              color: theme('colors.zinc.800'),
            },
            h4: {
              fontSize: '1.25rem',
              fontWeight: '600',
              lineHeight: '1.4',
              marginTop: '2rem',
              marginBottom: '0.75rem',
              color: theme('colors.zinc.800'),
            },
            p: {
              marginTop: '1.5rem',
              marginBottom: '1.5rem',
              lineHeight: '1.8',
            },
            strong: {
              fontWeight: '600',
              color: theme('colors.red.600'),
            },
            ul: {
              marginTop: '1.5rem',
              marginBottom: '1.5rem',
            },
            ol: {
              marginTop: '1.5rem',
              marginBottom: '1.5rem',
            },
            li: {
              marginTop: '0.5rem',
              marginBottom: '0.5rem',
            },
            blockquote: {
              fontStyle: 'italic',
              borderLeftColor: theme('colors.red.200'),
              borderLeftWidth: '4px',
              paddingLeft: '1.5rem',
              marginTop: '2rem',
              marginBottom: '2rem',
              backgroundColor: theme('colors.red.50'),
              padding: '1.5rem',
              borderRadius: '0.5rem',
            },
            img: {
              borderRadius: '0.75rem',
              marginTop: '2.5rem',
              marginBottom: '2.5rem',
              boxShadow: theme('boxShadow.lg'),
            },
            figure: {
              marginTop: '2.5rem',
              marginBottom: '2.5rem',
            },
            'figure img': {
              marginTop: '0',
              marginBottom: '0',
            },
            figcaption: {
              fontSize: '0.9rem',
              color: theme('colors.zinc.500'),
              textAlign: 'center',
              marginTop: '0.75rem',
              fontStyle: 'italic',
            },
            a: {
              color: theme('colors.red.600'),
              textDecoration: 'none',
              fontWeight: '500',
              '&:hover': {
                color: theme('colors.red.700'),
                textDecoration: 'underline',
              },
            },
            code: {
              fontSize: '0.9rem',
              fontWeight: '500',
              backgroundColor: theme('colors.zinc.100'),
              padding: '0.25rem 0.375rem',
              borderRadius: '0.25rem',
              border: `1px solid ${theme('colors.zinc.200')}`,
            },
            'code::before': {
              content: '""',
            },
            'code::after': {
              content: '""',
            },
          },
        },
        lg: {
          css: {
            fontSize: '1.125rem',
            lineHeight: '1.8',
            h1: {
              fontSize: '3rem',
              marginBottom: '2.5rem',
            },
            h2: {
              fontSize: '2.25rem',
              marginTop: '3.5rem',
              marginBottom: '2rem',
            },
            h3: {
              fontSize: '1.75rem',
              marginTop: '3rem',
              marginBottom: '1.5rem',
            },
            h4: {
              fontSize: '1.375rem',
              marginTop: '2.5rem',
              marginBottom: '1rem',
            },
            p: {
              marginTop: '2rem',
              marginBottom: '2rem',
            },
          },
        },
      }),
    }
  },
  plugins: [
    require('@tailwindcss/typography'),
  ]
}