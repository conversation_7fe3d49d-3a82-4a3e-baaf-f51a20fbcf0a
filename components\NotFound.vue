<template>
  <div class="container mx-auto px-4 py-16 flex flex-col items-center justify-center min-h-[70vh] text-center">
    <h1 class="text-8xl md:text-9xl font-bold text-blue-600 mb-4">404</h1>
    <h2 class="text-3xl md:text-4xl font-semibold text-gray-400 mb-6">Page Not Found</h2>
    <p class="text-lg text-gray-500 mb-10 max-w-md">
      Sorry, we couldn't find the page you're looking for. It might have been removed, renamed, or doesn't exist.
    </p>

    <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
      <NuxtLink
        to="/"
        class="px-8 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-lg font-medium">
        Back to Home
      </NuxtLink>

      <NuxtLink
        to="/contact-us"
        class="px-8 py-3 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-100 hover:border-gray-400 transition-colors text-lg font-medium">
        Contact Us
      </NuxtLink>
    </div>
  </div>
</template>

<script setup>
// Fungsi goBack tidak lagi digunakan dengan desain baru, bisa dihapus jika tidak ada tombol "Halaman Sebelumnya"
// const goBack = () => {
// window.history.length > 1 ? navigateTo(-1) : navigateTo('/');
// };

// Atur SEO untuk halaman 404
useHead({
  title: 'Page Not Found - Roofel', // Pastikan title sesuai dengan desain baru
  meta: [
    { name: 'robots', content: 'noindex, nofollow' },
    { name: 'description', content: 'Halaman yang Anda cari tidak ditemukan' }
  ]
});

// Atur status HTTP 404 untuk SEO jika dalam konteks server
const event = useRequestEvent();
if (event) {
  setResponseStatus(event, 404);
}
</script>
