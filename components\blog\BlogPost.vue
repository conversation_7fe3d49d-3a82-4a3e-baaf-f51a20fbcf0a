<template>
    <article class="prose lg:prose-lg mx-auto px-4">
      <header class="mb-8 border-b pb-4">
        <img :src="doc.image" :alt="doc.title" class="rounded-xl w-full mb-4" v-if="doc.image" />
        <h1 class="text-4xl font-bold">{{ doc.title }}</h1>
        <p class="text-gray-600">{{ doc.description }}</p>
        <time :datetime="doc.date" class="text-sm text-gray-500 block mt-2">
          Dipublikasikan pada {{ formatDate(doc.date) }}
        </time>
      </header>
  
      <ContentRenderer :value="doc.body" />
  
      <footer class="mt-12 text-sm text-gray-500 border-t pt-4">
  Ditulis oleh {{ doc.author || 'Admin' }} | Tags:
  <span v-for="tag in doc.tags" :key="tag" class="inline-block ml-1">
    <NuxtLink
      :to="`/blog/tag/${tag}`"
      class="text-blue-600 hover:underline"
    >
      #{{ tag }}
    </NuxtLink>
  </span>
</footer>

    </article>
  </template>
  
  <script setup>
  const props = defineProps({
    doc: Object
  })
  
  function formatDate(dateStr) {
    return new Date(dateStr).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }
  </script>
  