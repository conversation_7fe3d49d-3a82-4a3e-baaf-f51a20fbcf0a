# Test Perubahan URL Structure

## Perubahan yang Dilakukan:

1. ✅ Memindahkan `pages/blog/[slug].vue` → `pages/[slug].vue`
2. ✅ Memindahkan `pages/blog/tags/` → `pages/tags/`  
3. ✅ Memindahkan `pages/blog/authors/` → `pages/authors/`
4. ✅ Update `content.config.ts` untuk mapping path dari `/blog/` ke `/`
5. ✅ Update breadcrumb di halaman artikel
6. ✅ Update link tags dari `/blog/tags/` ke `/tags/`
7. ✅ Update related articles path logic
8. ✅ Update blog index page untuk link ke artikel baru
9. ✅ Update tags page untuk link ke artikel baru

## URL Sebelum:
- `/blog/panduan-lengkap-wisata-jogja-2025-tips-dan-destinasi-terbaik`
- `/blog/tags/wisata`
- `/blog/authors/roofel-team`

## URL Sesudah:
- `/panduan-lengkap-wisata-jogja-2025-tips-dan-destinasi-terbaik`
- `/tags/wisata`
- `/authors/roofel-team`

## Content yang Tetap:
- Content masih di `content/blog/` folder
- Hanya URL routing yang berubah
- Semua fitur tetap berfungsi (TOC, related articles, SEO, dll)

## Next Steps:
1. Test dengan `npm run dev`
2. Pastikan artikel dapat diakses di URL baru
3. Pastikan link internal bekerja
4. Test build untuk production
