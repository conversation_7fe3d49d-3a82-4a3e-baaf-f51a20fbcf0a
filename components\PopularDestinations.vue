<template>
  <section class="section bg-gray-50">
    <div class="container">
      <div class="text-center mb-12">
        <h2 class="section-title"><PERSON><PERSON><PERSON> Paket Wisata</h2>
        <p class="section-subtitle"><PERSON><PERSON><PERSON> paket wisata sesuai dengan kebutuhan dan preferensi Anda</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <div v-for="(category, index) in categories" :key="index" class="group relative rounded-xl overflow-hidden shadow-lg h-80">
          <div class="w-full h-full overflow-hidden">
            <img :src="category.image" :alt="category.title" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" />
            <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
            <div class="absolute bottom-0 left-0 p-6">
              <h3 class="text-2xl font-bold text-white mb-2">{{ category.title }}</h3>
              <div class="flex items-center gap-2 text-white/80">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                </svg>
                <span>{{ category.description }}</span>
              </div>
            </div>
            <NuxtLink :to="category.link" class="absolute inset-0" :aria-label="`Lihat paket ${category.title}`"></NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'

const categories = ref([
  {
    title: 'Paket 1 Hari',
    description: 'Tanpa Hotel',
    image: 'https://liburanjogja.b-cdn.net/gambar-taman-sari-destinasi-liburan-budaya-di-jogja.webp',
    link: '/paket-wisata-jogja/1-hari/'
  },
  {
    title: 'Paket 2 Hari 1 Malam',
    description: 'Dengan Hotel',
    image: 'https://liburanjogja.b-cdn.net/Heha-Ocean.webp',
    link: '/paket-wisata-jogja/2-hari-1-malam/'
  },
  {
    title: 'Paket Honeymoon',
    description: 'Romantis & Eksklusif',
    image: 'https://liburanjogja.b-cdn.net/Teras-Kaca.webp',
    link: '/paket-honeymoon/'
  },
  {
    title: 'Paket Gathering',
    description: 'Team Building & Corporate',
    image: 'https://liburanjogja.b-cdn.net/Jeep%20Gumuk%20Pasir.webp',
    link: '/paket-gathering/'
  }
])
</script>