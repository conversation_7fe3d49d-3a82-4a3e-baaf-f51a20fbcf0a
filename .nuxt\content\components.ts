export const DirectImg = () => import('./../../components/content/DirectImg.vue')
export const AppFooter = () => import('./../../components/AppFooter.vue')
export const AppHeader = () => import('./../../components/AppHeader.vue')
export const BlogCardsSection = () => import('./../../components/BlogCardsSection.vue')
export const BlogHeader = () => import('./../../components/BlogHeader.vue')
export const BlogList = () => import('./../../components/BlogList.vue')
export const BlogToc = () => import('./../../components/BlogToc.vue')
export const CategorySidebar = () => import('./../../components/CategorySidebar.vue')
export const FAQSection = () => import('./../../components/FAQSection.vue')
export const FeaturesSection = () => import('./../../components/FeaturesSection.vue')
export const HeroSection = () => import('./../../components/HeroSection.vue')
export const HoneymoonCard = () => import('./../../components/HoneymoonCard.vue')
export const HowItWorks = () => import('./../../components/HowItWorks.vue')
export const ImageGallery = () => import('./../../components/ImageGallery.vue')
export const NewsletterSection = () => import('./../../components/NewsletterSection.vue')
export const NotFound = () => import('./../../components/NotFound.vue')
export const PaketWisataCard = () => import('./../../components/PaketWisataCard.vue')
export const PaketWisataFilter = () => import('./../../components/PaketWisataFilter.vue')
export const PopularDestinations = () => import('./../../components/PopularDestinations.vue')
export const PopularDestinationsJogja = () => import('./../../components/PopularDestinationsJogja.vue')
export const RecommendedSection = () => import('./../../components/RecommendedSection.vue')
export const ReviewsSection = () => import('./../../components/ReviewsSection.vue')
export const SearchSection = () => import('./../../components/SearchSection.vue')
export const ServicesSection = () => import('./../../components/ServicesSection.vue')
export const SewaMobilCard = () => import('./../../components/SewaMobilCard.vue')
export const SewaMobilFilter = () => import('./../../components/SewaMobilFilter.vue')
export const TestimonialsSection = () => import('./../../components/TestimonialsSection.vue')
export const TrackingExample = () => import('./../../components/TrackingExample.vue')
export const WhyChooseUs = () => import('./../../components/WhyChooseUs.vue')
export const BlogBreadcrumb = () => import('./../../components/blog/BlogBreadcrumb.vue')
export const BlogComments = () => import('./../../components/blog/BlogComments.vue')
export const BlogFeaturedPost = () => import('./../../components/blog/BlogFeaturedPost.vue')
export const BlogImage = () => import('./../../components/blog/BlogImage.vue')
export const BlogPost = () => import('./../../components/blog/BlogPost.vue')
export const BlogPostCard = () => import('./../../components/blog/BlogPostCard.vue')
export const BlogPostHeader = () => import('./../../components/blog/BlogPostHeader.vue')
export const BlogPostNavigation = () => import('./../../components/blog/BlogPostNavigation.vue')
export const BlogSidebar = () => import('./../../components/blog/BlogSidebar.vue')
export const BlogTagsList = () => import('./../../components/blog/BlogTagsList.vue')
export const BlogBreadcrumbs = () => import('./../../components/blog/Breadcrumbs.vue')
export const NuxtWelcome = () => import('./../../node_modules/nuxt/dist/app/components/welcome.vue')
export const NuxtLayout = () => import('./../../node_modules/nuxt/dist/app/components/nuxt-layout')
export const NuxtErrorBoundary = () => import('./../../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue')
export const ClientOnly = () => import('./../../node_modules/nuxt/dist/app/components/client-only')
export const DevOnly = () => import('./../../node_modules/nuxt/dist/app/components/dev-only')
export const ServerPlaceholder = () => import('./../../node_modules/nuxt/dist/app/components/server-placeholder')
export const NuxtLink = () => import('./../../node_modules/nuxt/dist/app/components/nuxt-link')
export const NuxtLoadingIndicator = () => import('./../../node_modules/nuxt/dist/app/components/nuxt-loading-indicator')
export const NuxtTime = () => import('./../../node_modules/nuxt/dist/app/components/nuxt-time.vue')
export const NuxtRouteAnnouncer = () => import('./../../node_modules/nuxt/dist/app/components/nuxt-route-announcer')
export const NuxtImg = () => import('./../../node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue')
export const NuxtPicture = () => import('./../../node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue')
export const ContentRenderer = () => import('./../../node_modules/@nuxt/content/dist/runtime/components/ContentRenderer.vue')
export const MDC = () => import('./../../node_modules/@nuxtjs/mdc/dist/runtime/components/MDC.vue')
export const MDCCached = () => import('./../../node_modules/@nuxtjs/mdc/dist/runtime/components/MDCCached.vue')
export const MDCRenderer = () => import('./../../node_modules/@nuxtjs/mdc/dist/runtime/components/MDCRenderer.vue')
export const MDCSlot = () => import('./../../node_modules/@nuxtjs/mdc/dist/runtime/components/MDCSlot.vue')
export const NuxtPage = () => import('./../../node_modules/nuxt/dist/pages/runtime/page')
export const NoScript = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const Link = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const Base = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const Title = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const Meta = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const Style = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const Head = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const Html = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const Body = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const NuxtIsland = () => import('./../../node_modules/nuxt/dist/app/components/nuxt-island')
export const globalComponents: string[] = ["ProseA","ProseBlockquote","ProseCode","ProseEm","ProseH1","ProseH2","ProseH3","ProseH4","ProseH5","ProseH6","ProseHr","ProseImg","ProseLi","ProseOl","ProseP","ProsePre","ProseScript","ProseStrong","ProseTable","ProseTbody","ProseTd","ProseTh","ProseThead","ProseTr","ProseUl"]
export const localComponents: string[] = ["DirectImg","AppFooter","AppHeader","BlogCardsSection","BlogHeader","BlogList","BlogToc","CategorySidebar","FAQSection","FeaturesSection","HeroSection","HoneymoonCard","HowItWorks","ImageGallery","NewsletterSection","NotFound","PaketWisataCard","PaketWisataFilter","PopularDestinations","PopularDestinationsJogja","RecommendedSection","ReviewsSection","SearchSection","ServicesSection","SewaMobilCard","SewaMobilFilter","TestimonialsSection","TrackingExample","WhyChooseUs","BlogBreadcrumb","BlogComments","BlogFeaturedPost","BlogImage","BlogPost","BlogPostCard","BlogPostHeader","BlogPostNavigation","BlogSidebar","BlogTagsList","BlogBreadcrumbs","NuxtWelcome","NuxtLayout","NuxtErrorBoundary","ClientOnly","DevOnly","ServerPlaceholder","NuxtLink","NuxtLoadingIndicator","NuxtTime","NuxtRouteAnnouncer","NuxtImg","NuxtPicture","ContentRenderer","MDC","MDCCached","MDCRenderer","MDCSlot","NuxtPage","NoScript","Link","Base","Title","Meta","Style","Head","Html","Body","NuxtIsland"]