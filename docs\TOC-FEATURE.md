# Table of Contents (TOC) Feature untuk Blog

## Overview
Fitur Table of Contents (TOC) telah berhasil ditambahkan ke halaman single blog untuk meningkatkan SEO dan user experience. TOC secara otomatis mengekstrak heading dari konten blog dan menampilkannya sebagai navigasi yang interaktif.

## Fitur yang Ditambahkan

### 1. Komponen BlogTOC (`components/BlogTOC.vue`)
- **Auto-extract headings**: Secara otomatis mengekstrak H2, H3, H4, H5, H6 dari konten blog
- **Sticky navigation**: TOC tetap terlihat saat user scroll
- **Active heading tracking**: Menandai heading yang sedang dibaca user
- **Reading progress**: Menampilkan progress bar membaca artikel
- **Smooth scrolling**: Navigasi yang smooth ke heading yang dipilih
- **Responsive design**: Berbeda tampilan untuk desktop dan mobile
- **Mobile toggle**: Dapat dibuka/tutup di mobile untuk menghemat ruang

### 2. SEO Improvements
- **Structured Data**: Menambahkan JSON-LD untuk Article dan BreadcrumbList
- **Breadcrumb navigation**: Navigasi breadcrumb untuk SEO
- **Proper heading hierarchy**: Heading dengan ID yang SEO-friendly
- **Scroll margin**: Heading memiliki scroll margin untuk navigasi yang tepat

### 3. Layout Improvements
- **Grid layout**: Layout 3:1 untuk desktop (content:sidebar)
- **Mobile-first**: TOC ditampilkan di atas konten pada mobile
- **Responsive behavior**: Menyesuaikan dengan ukuran layar

## Struktur File

```
components/
├── BlogTOC.vue          # Komponen TOC utama

pages/blog/
├── [...slug].vue        # Halaman blog single dengan TOC terintegrasi

content/blog/
├── *.md                 # File konten blog dengan heading structure
```

## Cara Kerja TOC

### 1. Ekstraksi Heading
```javascript
// Mengekstrak heading dari DOM setelah konten di-render
const headings = document.querySelectorAll('.prose h2, .prose h3, .prose h4, .prose h5, .prose h6')
```

### 2. Generate ID
```javascript
// Generate ID dari text heading jika belum ada
if (!heading.id) {
  const id = heading.textContent
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '')
  heading.id = id
}
```

### 3. Active Tracking
- Menggunakan Intersection Observer concept dengan scroll position
- Update active heading berdasarkan posisi scroll
- Throttling untuk performance

### 4. Reading Progress
```javascript
// Menghitung progress berdasarkan scroll position
readingProgress.value = Math.min(100, (scrollTop / (documentHeight - windowHeight)) * 100)
```

## Fitur SEO

### 1. Structured Data Article
```json
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "...",
  "description": "...",
  "author": {...},
  "publisher": {...},
  "datePublished": "...",
  "dateModified": "..."
}
```

### 2. Structured Data Breadcrumb
```json
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [...]
}
```

### 3. CSS Improvements
- `scroll-margin-top: 100px` untuk semua heading
- `scroll-behavior: smooth` untuk smooth scrolling
- Proper heading hierarchy styling

## Responsive Design

### Desktop (lg+)
- TOC di sidebar kanan (sticky)
- Selalu terlihat
- Grid layout 3:1

### Mobile (<lg)
- TOC di atas konten
- Collapsible dengan toggle button
- Auto-close setelah klik link
- Full width

## Benefits untuk SEO

1. **Better User Experience**: Navigasi yang mudah meningkatkan time on page
2. **Structured Data**: Membantu search engine memahami konten
3. **Breadcrumb**: Membantu search engine memahami struktur site
4. **Proper Heading Structure**: Hierarki heading yang jelas
5. **Internal Linking**: TOC menciptakan internal anchor links
6. **Reading Progress**: Meningkatkan engagement metrics

## Penggunaan

TOC akan otomatis muncul di semua halaman blog yang memiliki heading (H2-H6). Tidak perlu konfigurasi tambahan, cukup pastikan konten blog memiliki struktur heading yang baik.

### Contoh Struktur Heading yang Baik:
```markdown
## Heading Level 2
### Heading Level 3
#### Heading Level 4
## Heading Level 2 Lainnya
### Sub Heading
```

## Customization

### Mengubah Depth TOC
Edit di `components/BlogTOC.vue`:
```javascript
// Untuk hanya menampilkan H2 dan H3
const headings = document.querySelectorAll('.prose h2, .prose h3')
```

### Mengubah Styling
Edit CSS di `components/BlogTOC.vue` atau `pages/blog/[...slug].vue`

### Mengubah Scroll Offset
Edit di `components/BlogTOC.vue`:
```javascript
const offsetTop = targetElement.offsetTop - 100 // Ubah nilai 100
```

## Sticky TOC Improvements

### Enhanced Sticky Positioning
Telah ditambahkan perbaikan untuk memastikan TOC benar-benar sticky di desktop:

1. **CSS Improvements**:
   - `position: -webkit-sticky` untuk kompatibilitas Safari
   - `align-self: flex-start` untuk positioning yang tepat
   - `will-change: transform` untuk optimasi performa
   - `!important` declarations untuk override conflicts

2. **JavaScript Enhancements**:
   - Force reflow untuk memastikan sticky positioning terhitung dengan benar
   - Dynamic style application untuk desktop
   - Parent container height adjustment

3. **Layout Optimizations**:
   - Grid layout yang tidak mengganggu sticky positioning
   - Proper container heights untuk sticky context
   - Responsive behavior yang konsisten

### CSS Classes Added
```css
/* Desktop sticky positioning */
@media (min-width: 1024px) {
  .toc-container {
    position: -webkit-sticky !important;
    position: sticky !important;
    top: 2rem !important;
    align-self: flex-start;
    will-change: transform;
  }
}
```

## Testing

1. Buka halaman blog: `http://localhost:3000/blog/candi-borobudur-keajaiban-dunia-yang-memukau`
2. Pastikan TOC muncul di sidebar (desktop) atau atas konten (mobile)
3. **Test sticky behavior**: Scroll halaman dan pastikan TOC tetap terlihat di desktop
4. Test klik link TOC untuk navigasi smooth
5. Test scroll untuk melihat active heading tracking
6. Test responsive behavior di berbagai ukuran layar
7. **Test konten panjang**: Halaman sekarang memiliki konten yang cukup panjang untuk test sticky

## Performance

- Menggunakan `requestAnimationFrame` untuk throttling scroll events
- Lazy loading TOC generation setelah content rendered
- Minimal DOM queries dengan caching
- Efficient event listeners dengan proper cleanup
