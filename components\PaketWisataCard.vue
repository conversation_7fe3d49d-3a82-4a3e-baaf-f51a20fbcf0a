<template>
  <div class="bg-white rounded-xl shadow-sm overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 h-full flex flex-col">
    <div class="relative h-56">
      <img
        :src="getSafeImageUrl(paket.image, paket.category)"
        :alt="paket.title"
        class="w-full h-full object-cover"
      />
      <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
      <div class="absolute bottom-4 left-4 right-4">
        <!-- <div class="flex items-center gap-2 text-white mb-2">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5 text-yellow-400">
            <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
          </svg>
          <span class="font-medium">{{ paket.rating }}</span>
          <span class="text-sm">({{ paket.reviews }} ulasan)</span>
        </div> -->
        <h3 class="text-xl font-bold text-white line-clamp-2">
          {{ paket.title }}
        </h3>
      </div>
    </div>
    <div class="p-6 flex flex-col flex-grow">
      <div class="flex flex-wrap items-center gap-4 text-gray-600 text-sm mb-3">
        <div class="flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
          </svg>
          <span>{{ paket.location }}</span>
        </div>
        <div class="flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ getDurationLabel(paket.duration) }}</span>
        </div>
      </div>

      <!-- Category & Duration -->
      <div class="flex flex-wrap gap-2 mb-3">
        <NuxtLink
          :to="`/paket-wisata-jogja/${paket.category}/`"
          class="px-2 py-0.5 bg-primary/10 text-primary text-xs rounded-full hover:bg-primary/20 transition-colors"
        >
          {{ getCategoryLabel(paket.category) }}
        </NuxtLink>

        <NuxtLink
          :to="`/paket-wisata-jogja/${paket.duration}/`"
          class="px-2 py-0.5 bg-gray-100 text-gray-700 text-xs rounded-full hover:bg-gray-200 transition-colors"
        >
          {{ getDurationLabel(paket.duration) }}
        </NuxtLink>
      </div>

      <p class="text-gray-600 mb-4 line-clamp-3 text-sm">{{ paket.description }}</p>
      
      <!-- Price & Detail Button -->
      <div class="mt-auto">
        <div class="flex items-center justify-between mb-3">
          <span class="text-gray-500 text-sm">Harga mulai</span>
          <div class="text-lg font-bold text-primary">{{ paket.price }}</div>
        </div>
        <NuxtLink 
          :to="`/paket-wisata-jogja/${paket.slug}/`" 
          class="block w-full py-2 px-4 bg-primary text-white text-center rounded-lg text-sm font-medium hover:bg-primary-dark transition-colors"
        >
          Cek Paket
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup>
import { usePaketWisata } from '~/composables/usePaketWisata'
import { useImageWithFallback } from '~/composables/useImageWithFallback'

const props = defineProps({
  paket: {
    type: Object,
    required: true
  }
})

const { getCategoryLabel, getDurationLabel } = usePaketWisata()
const { getSafeImageUrl } = useImageWithFallback()
</script>
