export default defineEventHandler(async (event) => {
  try {
    // Import data paket wisata
    const paketWisataData = await import('~/data/paket-wisata.json')
    const paketWisata = paketWisataData.default || paketWisataData
    
    // Generate sitemap URLs untuk paket wisata
    const urls = paketWisata.map((paket: any) => ({
      loc: `/paket-wisata-jogja/${paket.slug}/`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: 0.8
    }))
    
    return urls
  } catch (error) {
    console.error('Error generating paket wisata sitemap:', error)
    return []
  }
}) 