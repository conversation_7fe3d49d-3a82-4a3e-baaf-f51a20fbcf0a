<template>
  <section class="section bg-white">
    <div class="container">
      <div class="bg-gray-100 rounded-2xl p-8 md:p-12">
        <div class="text-center mb-10">
          <h2 class="section-title">Let's Explore Your Dream Destination Here!</h2>
          <p class="section-subtitle">Find and book your perfect trip with our easy-to-use search tools</p>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-sm">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Location</label>
              <div class="relative">
                <select class="w-full p-3 bg-gray-50 border border-gray-200 rounded-lg appearance-none focus:outline-none focus:ring-2 focus:ring-primary/50">
                  <option value="" disabled selected>Select location</option>
                  <option value="europe">Europe</option>
                  <option value="asia">Asia</option>
                  <option value="america">America</option>
                  <option value="africa">Africa</option>
                  <option value="australia">Australia</option>
                </select>
                <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-gray-400">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                  </svg>
                </div>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Person</label>
              <div class="relative">
                <select class="w-full p-3 bg-gray-50 border border-gray-200 rounded-lg appearance-none focus:outline-none focus:ring-2 focus:ring-primary/50">
                  <option value="" disabled selected>Select person</option>
                  <option value="1">1 Person</option>
                  <option value="2">2 Persons</option>
                  <option value="3">3 Persons</option>
                  <option value="4">4 Persons</option>
                  <option value="5+">5+ Persons</option>
                </select>
                <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-gray-400">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                  </svg>
                </div>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Check In</label>
              <input type="date" class="w-full p-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50" />
            </div>
            
            <div class="flex items-end">
              <button class="w-full btn btn-primary">Search</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>