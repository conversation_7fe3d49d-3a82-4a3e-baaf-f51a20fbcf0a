export default defineEventHandler(async (event) => {
  try {
    // Get destinations data from storage
    const destinations = await useStorage().getItem('server:data/destinations.json')
    
    // Log the data for debugging
    console.log('Destinations data retrieved:', destinations)
    
    // Return the data
    return destinations || { destinations: [] }
  } catch (error) {
    console.error('Error fetching destinations:', error)
    // Return empty array in case of error
    return { destinations: [] }
  }
})