<script setup>
import { ref, computed, onMounted, watch } from 'vue'

const props = defineProps({
  src: {
    type: String,
    default: ''
  },
  alt: {
    type: String,
    default: ''
  },
  width: {
    type: [String, Number],
    default: undefined
  },
  height: {
    type: [String, Number],
    default: undefined
  }
})

// Gunakan ref untuk melacak URL gambar saat ini
const currentSrc = ref('')
const imageLoaded = ref(false)
const imageError = ref(false)
const errorCount = ref(0)

// Daftar domain alternatif untuk mencoba
const fallbackDomains = [
  'https://liburanjogja.b-cdn.net/',
  'https://www.jogjaliburan.com/',
  'https://jogjaliburan.com/',
  '/images/'
]

// Fungsi untuk memeriksa apakah URL adalah eksternal
const isExternalUrl = (url) => {
  try {
    const parsedUrl = new URL(url)
    return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:'
  } catch {
    return false
  }
}

// Fungsi untuk memeriksa apakah URL dari BunnyCDN
const isBunnyCDN = (url) => {
  return url.includes('liburanjogja.b-cdn.net') || url.includes('b-cdn.net')
}

// Fungsi untuk mendapatkan URL relatif dari URL lengkap
const getRelativePath = (url) => {
  for (const domain of fallbackDomains) {
    if (url.startsWith(domain)) {
      return url.substring(domain.length)
    }
  }

  if (url.startsWith('/')) {
    return url.substring(1)
  }

  return url
}

// Fungsi untuk menangani error loading gambar
const handleError = (e) => {
  console.warn('Image load error:', currentSrc.value)
  
  // Jika sudah mencoba semua domain alternatif
  if (errorCount.value >= fallbackDomains.length - 1) {
    console.error('Semua domain alternatif sudah dicoba, gambar tidak dapat dimuat:', props.src)
    imageError.value = true
    imageLoaded.value = true
    return
  }

  // Coba domain alternatif berikutnya
  errorCount.value++
  const relativePath = getRelativePath(props.src)
  const nextDomainIndex = errorCount.value % fallbackDomains.length
  currentSrc.value = fallbackDomains[nextDomainIndex] + relativePath

  console.warn(`Mencoba domain alternatif (${errorCount.value}):`, currentSrc.value)
}

// Fungsi untuk menangani gambar berhasil dimuat
const handleLoad = () => {
  imageLoaded.value = true
  imageError.value = false
  console.log('Gambar berhasil dimuat:', currentSrc.value)
}

// Inisialisasi URL gambar
onMounted(() => {
  console.log('BlogImage mounted with src:', props.src)
  
  // Jika URL eksternal (BunnyCDN), gunakan langsung
  if (isExternalUrl(props.src)) {
    currentSrc.value = props.src
    console.log('Using external URL (bypass Nuxt Image):', props.src)
  } else {
    // Untuk URL relatif, konversi ke BunnyCDN
    const relativePath = getRelativePath(props.src)
    // Force BunnyCDN URL untuk production Vercel
    currentSrc.value = 'https://liburanjogja.b-cdn.net/' + relativePath
    console.log('Converting relative to BunnyCDN (production safe):', props.src, '=>', currentSrc.value)
  }
})

// Computed property untuk class gambar
const imageClasses = computed(() => {
  return {
    'rounded-lg shadow-md mx-auto w-full h-auto object-cover': true,
    'opacity-0': !imageLoaded.value,
    'opacity-100 transition-opacity duration-300': imageLoaded.value
  }
})

// Watch untuk perubahan props.src
watch(() => props.src, (newSrc) => {
  if (newSrc) {
    imageLoaded.value = false
    imageError.value = false
    errorCount.value = 0
    
    if (isExternalUrl(newSrc)) {
      currentSrc.value = newSrc
    } else {
      const relativePath = getRelativePath(newSrc)
      currentSrc.value = 'https://liburanjogja.b-cdn.net/' + relativePath
    }
  }
}, { immediate: true })
</script>

<template>
  <figure class="my-16 mb-20">
    <div class="relative w-full overflow-hidden bg-gray-50 rounded-lg">
      <!-- Loading placeholder -->
      <div v-if="!imageLoaded" class="w-full h-[400px] md:h-[500px] bg-gray-200 animate-pulse rounded-lg flex items-center justify-center">
        <div class="text-gray-500 text-sm">Memuat gambar...</div>
      </div>

      <!-- Error placeholder -->
      <div v-if="imageError && imageLoaded" class="w-full h-[400px] md:h-[500px] bg-gray-100 rounded-lg flex items-center justify-center">
        <div class="text-center text-gray-500">
          <div class="text-sm mb-2">❌ Gambar tidak dapat dimuat</div>
          <div class="text-xs">{{ props.src }}</div>
        </div>
      </div>

      <!-- Actual image - Use raw img tag to bypass Nuxt Image optimization -->
      <img
        v-show="!imageError"
        :src="currentSrc"
        :alt="alt"
        :width="width || 1200"
        :height="height || 800"
        :class="imageClasses"
        style="max-height: 600px; object-fit: contain; display: block;"
        loading="lazy"
        crossorigin="anonymous"
        @error="handleError"
        @load="handleLoad"
      />
    </div>
    <figcaption v-if="alt" class="text-center text-sm text-gray-500 mt-4 pt-2">
      {{ alt }}
    </figcaption>
  </figure>
</template>

