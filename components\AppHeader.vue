<template>
  <header 
    class="sticky top-0 z-50 transition-all duration-300"
    :class="[
      isTransparent ? 'bg-transparent' : 'bg-white shadow-sm', 
      atTop ? 'py-6' : 'py-3'
    ]"
  >
    <div class="container">
      <div class="flex items-center justify-between">
        <a href="/" class="flex items-center gap-2">
          <img src="/3j-tour-logo.webp" alt="Jogja Liburan <PERSON>" class="h-8" />
        </a>
        
        <nav class="hidden md:flex items-center gap-8">
           <a
            href="/sewa-mobil-jogja/"
            class="font-medium transition-colors"
            :class="isTransparent ? 'text-white hover:text-white/80' : 'text-gray-800 hover:text-primary'"
          >
            Sewa Mobil Jogja
          </a>
          <a
            href="/paket-wisata-jogja/"
            class="font-medium transition-colors"
            :class="isTransparent ? 'text-white hover:text-white/80' : 'text-gray-800 hover:text-primary'"
          >
            Paket Wisata Jogja
          </a>
          <a
            href="/paket-honeymoon/"
            class="font-medium transition-colors"
            :class="isTransparent ? 'text-white hover:text-white/80' : 'text-gray-800 hover:text-primary'"
          >
            Paket Honeymoon
          </a>
          <a
            href="/paket-gathering/"
            class="font-medium transition-colors"
            :class="isTransparent ? 'text-white hover:text-white/80' : 'text-gray-800 hover:text-primary'"
          >
            Paket Gathering
          </a>
          <!-- <a
            href="/tentang-kami/"
            class="font-medium transition-colors"
            :class="isTransparent ? 'text-white hover:text-white/80' : 'text-gray-800 hover:text-primary'"
          >
            Tentang Kami
          </a> -->
          <a
            href="/pelanggan-kami/"
            class="font-medium transition-colors"
            :class="isTransparent ? 'text-white hover:text-white/80' : 'text-gray-800 hover:text-primary'"
          >
            Pelanggan Kami
          </a>
          <!-- <a
            href="/contact/"
            class="font-medium transition-colors"
            :class="isTransparent ? 'text-white hover:text-white/80' : 'text-gray-800 hover:text-primary'"
          >
            contact
          </a> -->
          <a
            href="/blog/"
            class="font-medium transition-colors"
            :class="isTransparent ? 'text-white hover:text-white/80' : 'text-gray-800 hover:text-primary'"
          >
            Blog
          </a>
        </nav>
        
        <div class="flex items-center gap-4">
          <button
            class="md:hidden hamburger-button p-2 rounded-lg transition-colors"
            :class="[
              isTransparent ? 'text-white hover:bg-white/10' : 'text-gray-800 hover:bg-gray-100',
              mobileMenuOpen ? 'bg-gray-100' : ''
            ]"
            @click="toggleMobileMenu($event)"
            :aria-label="mobileMenuOpen ? 'Close menu' : 'Open menu'"
            :aria-expanded="mobileMenuOpen"
          >
            <!-- Hamburger Icon -->
            <svg
              v-if="!mobileMenuOpen"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="w-6 h-6 transition-transform duration-200"
            >
              <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
            </svg>

            <!-- Close Icon -->
            <svg
              v-else
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="w-6 h-6 transition-transform duration-200"
            >
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>
    
    <!-- Mobile Menu with Transition -->
    <ClientOnly>
      <Transition
        enter-active-class="transition-all duration-300 ease-out"
        enter-from-class="opacity-0 -translate-y-2"
        enter-to-class="opacity-100 translate-y-0"
        leave-active-class="transition-all duration-200 ease-in"
        leave-from-class="opacity-100 translate-y-0"
        leave-to-class="opacity-0 -translate-y-2"
      >
        <div v-if="mobileMenuOpen" class="md:hidden bg-white py-4 shadow-md mobile-menu">
          <div class="container">
            <nav class="flex flex-col gap-4">
              <a href="/paket-wisata-jogja/" @click="closeMobileMenu" class="font-medium hover:text-primary transition-colors py-2 px-4 rounded-lg hover:bg-primary/5">Paket Wisata Jogja</a>
              <a href="/paket-honeymoon/" @click="closeMobileMenu" class="font-medium hover:text-primary transition-colors py-2 px-4 rounded-lg hover:bg-primary/5">Paket Honeymoon</a>
              <a href="/paket-gathering/" @click="closeMobileMenu" class="font-medium hover:text-primary transition-colors py-2 px-4 rounded-lg hover:bg-primary/5">Paket Gathering</a>
              <a href="/sewa-mobil-jogja/" @click="closeMobileMenu" class="font-medium hover:text-primary transition-colors py-2 px-4 rounded-lg hover:bg-primary/5">Sewa Mobil Jogja</a>
              <!-- <a href="/tentang-kami/" @click="closeMobileMenu" class="font-medium hover:text-primary transition-colors py-2 px-4 rounded-lg hover:bg-primary/5">Tentang Kami</a> -->
              <a href="/pelanggan-kami/" @click="closeMobileMenu" class="font-medium hover:text-primary transition-colors py-2 px-4 rounded-lg hover:bg-primary/5">Pelanggan Kami</a>
              <!-- <a href="/contact/" @click="closeMobileMenu" class="font-medium hover:text-primary transition-colors py-2 px-4 rounded-lg hover:bg-primary/5">contact</a> -->
              <a href="/blog/" @click="closeMobileMenu" class="font-medium hover:text-primary transition-colors py-2 px-4 rounded-lg hover:bg-primary/5">Blog</a>
            </nav>
          </div>
        </div>
      </Transition>
    </ClientOnly>
  </header>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

// Props for flexibility
const props = defineProps({
  isTransparent: {
    type: Boolean,
    default: false
  }
});

// State
const mobileMenuOpen = ref(false);
const atTop = ref(true);

// Toggle mobile menu
const toggleMobileMenu = (event) => {
  event.stopPropagation();
  mobileMenuOpen.value = !mobileMenuOpen.value;
};

// Close mobile menu
const closeMobileMenu = () => {
  mobileMenuOpen.value = false;
};

// Check scroll position
const handleScroll = () => {
  atTop.value = window.scrollY < 10;
};

// Close mobile menu when clicking outside
const handleClickOutside = (event) => {
  // Don't close if clicking on hamburger button or its children
  if (event.target.closest('.hamburger-button')) {
    return;
  }

  // Don't close if clicking inside the mobile menu
  if (event.target.closest('.mobile-menu')) {
    return;
  }

  // Close menu if clicking outside
  if (mobileMenuOpen.value) {
    closeMobileMenu();
  }
};

// Close mobile menu when pressing Escape key
const handleKeyDown = (event) => {
  if (event.key === 'Escape' && mobileMenuOpen.value) {
    closeMobileMenu();
  }
};

// Lifecycle hooks
onMounted(() => {
  handleScroll(); // Initial check
  window.addEventListener('scroll', handleScroll);
  document.addEventListener('click', handleClickOutside);
  document.addEventListener('keydown', handleKeyDown);
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
  document.removeEventListener('click', handleClickOutside);
  document.removeEventListener('keydown', handleKeyDown);
});
</script>
