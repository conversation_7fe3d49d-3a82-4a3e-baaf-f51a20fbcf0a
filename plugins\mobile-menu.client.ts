export default defineNuxtPlugin(() => {
  // Mobile menu functionality - Vue 3 way, replacing jQ<PERSON>y
  const initMobileMenu = () => {
    const mobileMenuItems = document.querySelectorAll('ul.et_mobile_menu li.menu-item-has-children, ul.et_mobile_menu li.page_item_has_children');
    
    mobileMenuItems.forEach(item => {
      // Add mobile toggle button if not exists
      if (!item.querySelector('.mobile-toggle')) {
        const toggleButton = document.createElement('a');
        toggleButton.href = '#';
        toggleButton.className = 'mobile-toggle';
        toggleButton.setAttribute('data-icon', 'P');
        item.appendChild(toggleButton);
        
        // Add click event listener
        toggleButton.addEventListener('click', (event) => {
          event.preventDefault();
          
          // Toggle open class
          item.classList.toggle('dt-open');
          
          // Toggle submenu visibility
          const childrenMenu = item.querySelector('ul.children');
          const subMenu = item.querySelector('ul.sub-menu');
          
          if (childrenMenu) {
            childrenMenu.classList.toggle('visible');
          }
          if (subMenu) {
            subMenu.classList.toggle('visible');
          }
        });
        
        // Add hover effects
        toggleButton.addEventListener('mouseover', () => {
          item.classList.add('is-hover');
        });
        
        toggleButton.addEventListener('mouseout', () => {
          item.classList.remove('is-hover');
        });
      }
      
      // Set data-icon attribute
      item.setAttribute('data-icon', 'P');
    });
  };

  // Initialize on DOM ready
  const initOnReady = () => {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initMobileMenu);
    } else {
      initMobileMenu();
    }
  };

  // Initialize when plugin loads
  initOnReady();

  // Re-initialize on route changes (for SPA navigation)
  const router = useRouter();
  router.afterEach(() => {
    nextTick(() => {
      initMobileMenu();
    });
  });
}); 