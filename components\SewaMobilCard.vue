<template>
  <div class="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow overflow-hidden group">
    <div class="relative overflow-hidden">
      <img 
        :src="mobil.image" 
        :alt="mobil.name"
        class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
      />
      <div class="absolute top-3 right-3">
        <span class="bg-white/90 backdrop-blur-sm text-xs font-medium px-2 py-1 rounded-full">
          {{ mobil.capacity }}
        </span>
      </div>
    </div>
    
    <div class="p-6">
      <div class="flex items-start justify-between mb-3">
        <h3 class="text-lg font-bold text-gray-900 group-hover:text-primary transition-colors">
          {{ mobil.name }}
        </h3>
        <div class="flex items-center gap-1 ml-2">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4 text-yellow-400">
            <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
          </svg>
          <span class="text-sm font-medium text-gray-600">{{ mobil.rating }}</span>
        </div>
      </div>
      
      <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ mobil.description }}</p>
      
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center gap-3 text-xs text-gray-500">
          <span class="flex items-center gap-1">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
              <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            {{ mobil.capacity }}
          </span>
          <span class="flex items-center gap-1">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
              <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            {{ mobil.transmission }}
          </span>
        </div>
      </div>
      
      <div class="border-t border-gray-100 pt-4">
        <div class="flex items-center justify-between mb-3">
          <div class="text-sm text-gray-500">Mulai dari</div>
          <div class="text-lg font-bold text-primary">
            {{ getLowestPrice(mobil) }}
          </div>
        </div>
        
        <div class="flex gap-2">
          <NuxtLink 
            :to="`/sewa-mobil-jogja/${mobil.slug}/`"
            class="flex-1 bg-primary text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-primary-dark transition-colors text-center"
          >
            Lihat Detail
          </NuxtLink>
          <button 
            @click="openWhatsApp(mobil)"
            class="flex-1 border border-primary text-primary py-2 px-4 rounded-lg text-sm font-medium hover:bg-primary/10 transition-colors"
          >
            Pesan Sekarang
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  mobil: {
    type: Object,
    required: true
  }
})

// Get lowest price from pricing options
const getLowestPrice = (mobil) => {
  if (mobil.packages) {
    // New pricing structure with packages
    const prices = []
    Object.values(mobil.packages).forEach(pkg => {
      if (pkg.pricing) {
        prices.push(pkg.pricing['12jam'], pkg.pricing['24jam'])
      }
    })
    const lowestPrice = Math.min(...prices)
    return `Rp ${lowestPrice.toLocaleString('id-ID')}`
  } else if (mobil.pricing) {
    // Old pricing structure
    const priceString = mobil.pricing['12jam'] || mobil.pricing.sopir12jam || '0'
    return priceString
  }
  return 'Hubungi Kami'
}

// Open WhatsApp for car rental booking
const openWhatsApp = (mobil) => {
  const message = `Halo! 🚗

Saya tertarik untuk sewa ${mobil.name}.

📋 *Detail Mobil:*
🚗 Kapasitas: ${mobil.capacity}
⚙️ Transmisi: ${mobil.transmission}
⭐ Rating: ${mobil.rating}/5.0

Bisa minta informasi lengkap tentang:
• Harga dan paket sewa mobil
• Syarat dan ketentuan sewa
• Jadwal ketersediaan mobil
• Proses booking dan pembayaran

Terima kasih! 🙏`

  const whatsappUrl = `https://wa.me/6281325005958?text=${encodeURIComponent(message)}`
  window.open(whatsappUrl, '_blank')
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 