<template>
  <section class="section bg-white">
    <div class="container">
      <div class="text-center mb-12">
        <h2 class="section-title">Paket Wisata <PERSON></h2>
        <p class="section-subtitle">Je<PERSON><PERSON><PERSON> paket wisata terbaik kami untuk pengalaman liburan yang tak terlupakan di Jogja</p>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div v-for="paket in recommendedPackages" :key="paket.id" class="bg-white rounded-xl overflow-hidden shadow-lg group">
          <div class="relative h-64 overflow-hidden">
            <img :src="paket.image" :alt="paket.title" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" />
            <div class="absolute top-4 right-4 bg-white px-3 py-1 rounded-full text-sm font-medium">
              {{ paket.price }}
            </div>
          </div>
          <div class="p-6">
            <div class="flex items-center gap-2 text-sm text-gray-500 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-primary">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
              </svg>
              <span>{{ paket.location }}</span>
            </div>
            <h3 class="text-xl font-bold mb-2">{{ paket.title }}</h3>
            <p class="text-gray-600 mb-4 line-clamp-2">{{ paket.description }}</p>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5 text-yellow-500">
                  <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
                </svg>
                <span class="font-medium">{{ paket.rating }}</span>
                <span class="text-gray-500">({{ paket.reviews }})</span>
              </div>
              <NuxtLink :to="`/paket-wisata-jogja/${paket.slug}/`" class="text-primary font-medium hover:underline">Lihat Detail</NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import paketWisataData from '~/data/paket-wisata.json'

const recommendedPackages = ref([])

onMounted(() => {
  // Ambil 3 paket wisata dengan rating tertinggi
  recommendedPackages.value = [...paketWisataData]
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 3)
})
</script>