export const useGoogleAds = () => {
  // Function untuk melacak konversi WhatsApp
  const trackWhatsAppConversion = (url?: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      const callback = () => {
        if (typeof url !== 'undefined') {
          window.location.href = url;
        }
      };

      window.gtag('event', 'conversion', {
        'send_to': 'AW-606851190/dfOLCLzgx4cYEPagr6EC',
        'event_callback': callback
      });
    }
    return false;
  };

  // Function untuk melacak konversi telepon
  const trackPhoneConversion = () => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'conversion', {
        'send_to': 'AW-606851190/phone_conversion',
      });
    }
  };

  // Function untuk melacak konversi form
  const trackFormConversion = (formType: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'conversion', {
        'send_to': 'AW-606851190/form_conversion',
        'custom_parameters': {
          'form_type': formType
        }
      });
    }
  };

  // Function untuk melacak page view dengan enhanced ecommerce
  const trackPageView = (page_title: string, page_location: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', 'AW-606851190', {
        page_title: page_title,
        page_location: page_location
      });
    }
  };

  return {
    trackWhatsAppConversion,
    trackPhoneConversion,
    trackFormConversion,
    trackPageView
  };
};

// Type declarations untuk gtag
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
} 