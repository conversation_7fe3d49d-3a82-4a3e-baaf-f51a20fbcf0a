
<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';

const props = defineProps({
  // Identifier unik untuk thread komentar
  identifier: {
    type: String,
    required: true
  },
  // URL halaman untuk komentar
  url: {
    type: String,
    required: true
  },
  // Judul artikel
  title: {
    type: String,
    required: true
  }
});

// Status loading
const isLoaded = ref(false);
const isError = ref(false);

// Function untuk memuat Disqus
function loadDisqus() {
  if (isLoaded.value) return;
  
  try {
    // Dapatkan shortname Disqus dari environment variable
    const disqusShortname = 'roofel';
    
    // Reset Disqus jika sudah ada
    if (window.DISQUS) {
      window.DISQUS.reset({
        reload: true,
        config: function() {
          this.page.identifier = props.identifier;
          this.page.url = props.url;
          this.page.title = props.title;
        }
      });
      isLoaded.value = true;
      return;
    }
    
    // Konfigurasi Disqus
    window.disqus_config = function() {
      this.page.identifier = props.identifier;
      this.page.url = props.url;
      this.page.title = props.title;
    };
    
    // Load Disqus script
    const script = document.createElement('script');
    script.src = `https://${disqusShortname}.disqus.com/embed.js`;
    script.setAttribute('data-timestamp', +new Date());
    script.async = true;
    script.onload = () => {
      isLoaded.value = true;
    };
    script.onerror = () => {
      isError.value = true;
    };
    
    document.head.appendChild(script);
  } catch (error) {
    console.error('Error loading Disqus:', error);
    isError.value = true;
  }
}

// Muat Disqus saat komponen dimount
onMounted(() => {
  if (process.client) {
    // Delay loading untuk memastikan DOM sudah siap
    setTimeout(() => {
      loadDisqus();
    }, 1000);
  }
});

// Perhatikan perubahan pada identifier untuk memuat ulang Disqus jika diperlukan
watch(() => props.identifier, (newVal, oldVal) => {
  if (newVal !== oldVal && process.client) {
    loadDisqus();
  }
});
</script>

<template>
  <div class="blog-comments bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden p-8 mt-8">
    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Komentar</h2>
    
    <!-- Loading State -->
    <div v-if="!isLoaded && !isError" class="text-center py-8">
      <p class="text-gray-600 dark:text-gray-400">Memuat komentar...</p>
    </div>
    
    <!-- Error State -->
    <div v-else-if="isError" class="text-center py-8 bg-red-50 dark:bg-red-900/20 rounded-lg">
      <p class="text-red-600 dark:text-red-400">Gagal memuat komentar. Silakan coba lagi nanti.</p>
    </div>
    
    <!-- Disqus Container -->
    <div id="disqus_thread"></div>
    
    <!-- Privacy Notice -->
    <div class="mt-6 text-sm text-gray-500 dark:text-gray-400">
      <p>Komentar dikelola oleh Disqus. Dengan berkomentar, Anda menyetujui <a href="https://help.disqus.com/en/articles/1717103-disqus-privacy-policy" target="_blank" rel="noopener noreferrer" class="text-primary hover:underline">Kebijakan Privasi Disqus</a>.</p>
    </div>
  </div>
</template>

<style scoped>
/* Custom styling for Disqus container */
#disqus_thread {
  min-height: 200px;
}
</style>
