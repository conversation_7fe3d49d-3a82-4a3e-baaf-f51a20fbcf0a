{
  // https://nuxt.com/docs/guide/concepts/typescript
  "extends": "./.nuxt/tsconfig.json",
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "strict": true,
    "allowJs": true,
    "noEmit": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "types": [
      "node",
      "@nuxt/types",
      "@nuxtjs/tailwindcss",
      "@nuxt/image-edge"
    ],
    "baseUrl": ".",
    "paths": {
      "~/*": ["./*"],
      "@/*": ["./*"],
      "#imports": ["./.nuxt/types/imports.d.ts"],
      "#app": ["./.nuxt/types/app.d.ts"],
      "#app/*": ["./.nuxt/types/app/*.d.ts"]
    }
  },
  "exclude": [
    "node_modules",
    "dist"
  ]
} 