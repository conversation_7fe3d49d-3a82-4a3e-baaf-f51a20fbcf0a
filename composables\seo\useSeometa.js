// composables/seo/useSeoMeta.js
export const useJogjaSeometa = () => {
  const { baseUrl } = useSchema()

  // Default meta tags untuk seluruh situs
  const getDefaultMeta = () => ({
    charset: 'utf-8',
    viewport: 'width=device-width, initial-scale=1, viewport-fit=cover',
    'format-detection': 'telephone=no',
    'theme-color': '#f97316',
    'msapplication-TileColor': '#f97316',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': '3J Tour Jogja',
    
    // Security headers
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    
    // Performance hints
    'dns-prefetch': 'true',
    'preconnect': 'https://fonts.googleapis.com',
    'preconnect': 'https://www.google-analytics.com',
    
    // Geo location - Updated dengan koordinat yang benar
    'geo.region': 'ID-YO',
    'geo.placename': 'Bantul, Yogyakarta',
    'geo.position': '-7.822014292198785;110.40547447500533',
    'ICBM': '-7.822014292198785, 110.40547447500533',
    
    // Language
    'language': 'id',
    'content-language': 'id',
    
    // Robots
    'robots': 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1',
    'googlebot': 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1',
    'bingbot': 'index, follow',
    
    // Copyright
    'copyright': '3J Tour - Jogja Liburan',
    'author': '3J Tour Team',
    'generator': 'Nuxt.js',
    
    // Business info
    'business:contact_data:phone_number': '+62-851-8688-8837',
    'business:contact_data:country_name': 'Indonesia',
    'business:contact_data:region': 'Yogyakarta',
    'business:contact_data:locality': 'Bantul',
    'business:contact_data:street_address': 'Jl. Merpati, Modalan, Banguntapan',
    'business:contact_data:postal_code': '55198',
    
    // Site verification (replace with actual codes)
    'google-site-verification': 'your-google-verification-code',
    'msvalidate.01': 'your-bing-verification-code',
    'yandex-verification': 'your-yandex-verification-code'
  })

  // Blog post meta generator
  const getBlogPostMeta = (post, url) => {
    const title = `${post.title} | 3J Tour - Jogja Liburan`
    const description = post.description
    const imageUrl = post.image?.src || `${baseUrl}/images/og-default.jpg`
    const articleTags = post.tags?.join(', ') || 'wisata jogja, travel yogyakarta'

    return {
      title,
      description,
      keywords: `${articleTags}, panduan wisata jogja, destinasi yogyakarta, liburan jogja`,
      author: post.authors?.[0]?.name || '3J Tour Team',
      
      // Open Graph
      ogTitle: title,
      ogDescription: description,
      ogType: 'article',
      ogUrl: url,
      ogImage: imageUrl,
      ogImageWidth: 1200,
      ogImageHeight: 630,
      ogImageAlt: post.title,
      ogSiteName: '3J Tour - Jogja Liburan',
      ogLocale: 'id_ID',
      
      // Article specific Open Graph
      'article:published_time': new Date(post.date).toISOString(),
      'article:modified_time': new Date(post.date).toISOString(),
      'article:author': post.authors?.[0]?.name || '3J Tour Team',
      'article:section': 'Travel Guide',
      'article:tag': post.tags || [],
      
      // Twitter Card
      twitterCard: 'summary_large_image',
      twitterTitle: title,
      twitterDescription: description,
      twitterImage: imageUrl,
      twitterImageAlt: post.title,
      twitterSite: '@jogja_jalan_jalan',
      twitterCreator: '@jogja_jalan_jalan',
      
      // Additional meta
      'article:reading_time': '5',
      'article:word_count': '1500'
    }
  }

  // Blog index meta
  const getBlogIndexMeta = () => {
    const title = 'Blog Jogja Liburan - Panduan Wisata Terlengkap Yogyakarta'
    const description = 'Temukan panduan wisata lengkap, tips liburan, rekomendasi destinasi terbaik, kuliner khas, dan informasi travel Yogyakarta dan sekitarnya'
    const url = `${baseUrl}/blog/`
    const imageUrl = `${baseUrl}/images/blog-og.jpg`

    return {
      title,
      description,
      keywords: 'blog jogja, panduan wisata yogyakarta, destinasi jogja, kuliner jogja, travel guide yogyakarta, liburan jogja, wisata budaya jogja',
      
      // Open Graph
      ogTitle: title,
      ogDescription: description,
      ogType: 'website',
      ogUrl: url,
      ogImage: imageUrl,
      ogImageWidth: 1200,
      ogImageHeight: 630,
      ogImageAlt: 'Blog Jogja Liburan - Panduan Wisata Yogyakarta',
      ogSiteName: '3J Tour - Jogja Liburan',
      ogLocale: 'id_ID',
      
      // Twitter Card
      twitterCard: 'summary_large_image',
      twitterTitle: title,
      twitterDescription: description,
      twitterImage: imageUrl,
      twitterImageAlt: 'Blog Jogja Liburan - Panduan Wisata Yogyakarta',
      twitterSite: '@jogja_jalan_jalan',
      twitterCreator: '@jogja_jalan_jalan'
    }
  }

  // Tags index meta
  const getTagsIndexMeta = () => {
    const title = 'Kategori Wisata - Blog Jogja Liburan | Temukan Destinasi Sesuai Minat'
    const description = 'Jelajahi berbagai kategori wisata Yogyakarta: candi, pantai, kuliner, gunung, budaya, dan destinasi menarik lainnya. Temukan panduan sesuai minat liburan Anda'
    const url = `${baseUrl}/tags/`
    const imageUrl = `${baseUrl}/images/tags-og.jpg`

    return {
      title,
      description,
      keywords: 'kategori wisata jogja, destinasi yogyakarta, wisata candi, pantai jogja, kuliner yogyakarta, gunung merapi, budaya jogja, travel guide',
      
      // Open Graph
      ogTitle: title,
      ogDescription: description,
      ogType: 'website',
      ogUrl: url,
      ogImage: imageUrl,
      ogSiteName: '3J Tour - Jogja Liburan',
      ogLocale: 'id_ID',
      
      // Twitter Card
      twitterCard: 'summary_large_image',
      twitterTitle: title,
      twitterDescription: description,
      twitterImage: imageUrl,
      twitterSite: '@jogja_jalan_jalan'
    }
  }

  // Individual tag page meta
  const getTagPageMeta = (tagName, postsCount) => {
    const title = `${tagName} - Panduan Wisata Yogyakarta | 3J Tour Jogja Liburan`
    const description = `Temukan ${postsCount} panduan wisata dan tips ${tagName} terbaik di Yogyakarta. Informasi lengkap destinasi, kuliner, dan aktivitas seru untuk liburan Anda`
    const url = `${baseUrl}/tags/${encodeURIComponent(tagName.toLowerCase())}/`
    const imageUrl = `${baseUrl}/images/tags/${tagName.toLowerCase()}-og.jpg`

    return {
      title,
      description,
      keywords: `${tagName} jogja, wisata ${tagName} yogyakarta, panduan ${tagName}, destinasi ${tagName}, travel guide ${tagName}`,
      
      // Open Graph
      ogTitle: title,
      ogDescription: description,
      ogType: 'website',
      ogUrl: url,
      ogImage: imageUrl,
      ogSiteName: '3J Tour - Jogja Liburan',
      ogLocale: 'id_ID',
      
      // Twitter Card
      twitterCard: 'summary_large_image',
      twitterTitle: title,
      twitterDescription: description,
      twitterImage: imageUrl,
      twitterSite: '@jogja_jalan_jalan'
    }
  }

  // Common link tags
  const getCommonLinks = (url) => [
    { rel: 'canonical', href: url },
    { rel: 'alternate', hreflang: 'id', href: url },
    { rel: 'alternate', hreflang: 'x-default', href: url },
    
    // Preconnect for performance
    { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
    { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: true },
    { rel: 'preconnect', href: 'https://www.google-analytics.com' },
    { rel: 'dns-prefetch', href: 'https://www.googletagmanager.com' },
    
    // Icons
    { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
    { rel: 'icon', type: 'image/png', sizes: '32x32', href: '/favicon-32x32.png' },
    { rel: 'icon', type: 'image/png', sizes: '16x16', href: '/favicon-16x16.png' },
    { rel: 'apple-touch-icon', sizes: '180x180', href: '/apple-touch-icon.png' },
    { rel: 'manifest', href: '/site.webmanifest' },
    
    // RSS Feed
    { rel: 'alternate', type: 'application/rss+xml', title: '3J Tour - Jogja Liburan RSS Feed', href: `${baseUrl}/rss.xml` }
  ]

  return {
    getDefaultMeta,
    getBlogPostMeta,
    getBlogIndexMeta,
    getTagsIndexMeta,
    getTagPageMeta,
    getCommonLinks
  }
}
